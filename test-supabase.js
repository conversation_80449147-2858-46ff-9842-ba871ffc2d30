#!/usr/bin/env node

/**
 * Test du système Supabase pour Nowee
 */

import 'dotenv/config';
import { dbService } from './src/services/databaseServiceUnified.js';

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.bold}${colors.blue}\n🗄️ ${msg}${colors.reset}`)
};

async function testDatabase() {
  log.title('Test du Système de Base de Données Nowee');

  try {
    // 1. Vérifier la configuration
    log.info('Vérification de la configuration...');
    
    if (dbService.isUsingSupabase()) {
      log.success('Supabase configuré et utilisé');
    } else {
      log.warning('Supabase non configuré, utilisation du mode fallback en mémoire');
    }

    // 2. Test de création d'utilisateur
    log.info('Test de création d\'utilisateur...');
    
    const testPhone = '+221771234567';
    const user = await dbService.getUserProfile(testPhone);
    
    log.success(`Utilisateur créé/récupéré: ${user.id}`);
    console.log(`   Téléphone: ${user.phone}`);
    console.log(`   Nom: ${user.name || 'Non défini'}`);
    console.log(`   Ville: ${user.city || 'Non définie'}`);

    // 3. Test de création de besoin
    log.info('Test de création de besoin...');
    
    const need = await dbService.recordUserNeed(testPhone, {
      type: 'MATERIAL',
      description: 'J\'ai besoin d\'une perceuse pour des travaux urgents à Dakar',
      urgency: 3,
      location: {
        city: 'Dakar',
        country: 'Sénégal',
        coordinates: { latitude: 14.7167, longitude: -17.4677 }
      }
    });
    
    log.success(`Besoin créé: ${need.id}`);
    console.log(`   Titre: ${need.title}`);
    console.log(`   Catégorie: ${need.category}`);
    console.log(`   Urgence: ${need.urgency}`);

    // 4. Test de création d'offre
    log.info('Test de création d\'offre...');
    
    const testPhone2 = '+221772345678';
    const user2 = await dbService.getUserProfile(testPhone2);
    
    const offer = await dbService.recordUserOffer(testPhone2, {
      type: 'MATERIAL',
      description: 'Je peux prêter ma perceuse électrique, disponible tous les jours',
      location: {
        city: 'Dakar',
        country: 'Sénégal',
        coordinates: { latitude: 14.7200, longitude: -17.4700 }
      },
      availability: {
        flexible: true,
        hours: '8h-18h'
      }
    });
    
    log.success(`Offre créée: ${offer.id}`);
    console.log(`   Titre: ${offer.title}`);
    console.log(`   Catégorie: ${offer.category}`);

    // 5. Test de matching
    log.info('Test de matching...');
    
    const matches = await dbService.findMatches(need.id, 20);
    
    if (matches.length > 0) {
      log.success(`${matches.length} correspondance(s) trouvée(s) !`);
      
      matches.forEach((match, index) => {
        console.log(`\n   Match ${index + 1}:`);
        console.log(`   - Titre: ${match.title}`);
        console.log(`   - Score: ${match.match_score || 'N/A'}`);
        console.log(`   - Distance: ${match.distance_km ? match.distance_km.toFixed(2) + ' km' : 'N/A'}`);
      });
    } else {
      log.warning('Aucune correspondance trouvée');
    }

    // 6. Test des statistiques
    log.info('Test des statistiques...');
    
    const stats = await dbService.getGlobalStats();
    
    log.success('Statistiques récupérées:');
    console.log(`   - Utilisateurs: ${stats.totalUsers}`);
    console.log(`   - Ressources: ${stats.totalResources || stats.totalNeeds + stats.totalOffers || 'N/A'}`);
    console.log(`   - Besoins: ${stats.totalNeeds || 'N/A'}`);
    console.log(`   - Offres: ${stats.totalOffers || 'N/A'}`);
    console.log(`   - Correspondances: ${stats.totalMatches}`);

    // 7. Test de log d'événement
    log.info('Test de log d\'événement...');
    
    const event = await dbService.logEvent('test_event', user.id, {
      action: 'test_database',
      timestamp: new Date(),
      success: true
    });
    
    if (event) {
      log.success(`Événement enregistré: ${event.id}`);
    }

    // 8. Affichage du mode utilisé
    log.info('Mode de base de données:');
    if (dbService.isUsingSupabase()) {
      console.log('   🚀 Supabase (production)');
    } else {
      console.log('   💾 Mémoire (développement)');
      
      // Afficher les données en mémoire
      const memoryData = dbService.getMemoryStorage();
      console.log(`   - Utilisateurs en mémoire: ${memoryData.users.size}`);
      console.log(`   - Ressources en mémoire: ${memoryData.resources.size}`);
      console.log(`   - Événements en mémoire: ${memoryData.events.length}`);
    }

    log.success('\n🎉 Tous les tests de base de données réussis !');

  } catch (error) {
    log.error(`Erreur lors du test: ${error.message}`);
    console.error(error);
  }
}

// Fonction principale
async function main() {
  await testDatabase();
  process.exit(0);
}

// Gestion des erreurs
process.on('unhandledRejection', (error) => {
  log.error(`Erreur non gérée: ${error.message}`);
  process.exit(1);
});

// Exécuter le test
main().catch(error => {
  log.error(`Erreur: ${error.message}`);
  process.exit(1);
});
