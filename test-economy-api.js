#!/usr/bin/env node

/**
 * Test de l'API économique Nowee
 */

import 'dotenv/config';
import express from 'express';
import bodyParser from 'body-parser';
import economyAPI from './src/api/economyAPI.js';
import { dbService } from './src/services/databaseServiceUnified.js';

const app = express();
const PORT = 3001; // Port différent pour éviter les conflits

// Middleware
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

// Routes API économiques
app.use('/api/economy', economyAPI);

// Route de santé
app.get('/health', async (req, res) => {
  try {
    const stats = await dbService.getGlobalStats();
    
    res.json({
      status: 'OK',
      service: 'Nowee Economy API',
      database: dbService.isUsingSupabase() ? 'Supabase' : 'Memory',
      stats: stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      error: error.message
    });
  }
});

// Route de test simple
app.get('/test', (req, res) => {
  res.json({
    message: 'API économique Nowee fonctionnelle !',
    endpoints: [
      'GET /api/economy/wallet/:phone',
      'POST /api/economy/transfer',
      'GET /api/economy/transactions/:phone',
      'GET /api/economy/stats',
      'POST /api/economy/barter/propose',
      'GET /api/economy/barter/:phone'
    ]
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 API Économique Nowee démarrée sur le port ${PORT}`);
  console.log(`📊 Base de données: ${dbService.isUsingSupabase() ? 'Supabase' : 'Mémoire'}`);
  console.log(`🔗 Health: http://localhost:${PORT}/health`);
  console.log(`🧪 Test: http://localhost:${PORT}/test`);
  console.log(`💰 API: http://localhost:${PORT}/api/economy/stats`);
  console.log(`\n💡 Testez avec curl ou votre navigateur !`);
});
