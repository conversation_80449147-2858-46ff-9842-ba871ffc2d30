#!/usr/bin/env node

/**
 * Test des fonctionnalités de base avec Supabase
 */

import 'dotenv/config';
import { dbService } from './src/services/databaseServiceUnified.js';

async function testSupabaseBasic() {
  console.log('🗄️ Test Supabase - Fonctionnalités de Base\n');
  
  try {
    console.log(`📊 Mode: ${dbService.isUsingSupabase() ? 'Supabase' : 'Fallback'}\n`);
    
    // 1. Test création utilisateur
    console.log('1. Test création utilisateur...');
    const user = await dbService.getUserProfile('+221771234567');
    console.log(`✅ Utilisateur créé: ${user.id}`);
    console.log(`   Téléphone: ${user.phone}`);
    console.log(`   Nom: ${user.name || 'Non défini'}\n`);
    
    // 2. Test création de besoin
    console.log('2. Test création de besoin...');
    const need = await dbService.recordUserNeed('+221771234567', {
      type: 'MATERIAL',
      description: 'J\'ai besoin d\'une perceuse pour des travaux à Dakar',
      urgency: 3,
      location: {
        city: 'Dakar',
        coordinates: { latitude: 14.7167, longitude: -17.4677 }
      }
    });
    console.log(`✅ Besoin créé: ${need.id}`);
    console.log(`   Titre: ${need.title}`);
    console.log(`   Catégorie: ${need.category}\n`);
    
    // 3. Test création d'offre
    console.log('3. Test création d\'offre...');
    const user2 = await dbService.getUserProfile('+221772345678');
    const offer = await dbService.recordUserOffer('+221772345678', {
      type: 'MATERIAL',
      description: 'Je peux prêter ma perceuse électrique',
      location: {
        city: 'Dakar',
        coordinates: { latitude: 14.7200, longitude: -17.4700 }
      }
    });
    console.log(`✅ Offre créée: ${offer.id}`);
    console.log(`   Titre: ${offer.title}\n`);
    
    // 4. Test de matching
    console.log('4. Test de matching...');
    const matches = await dbService.findMatches(need.id, 20);
    console.log(`✅ ${matches.length} correspondance(s) trouvée(s)`);
    
    if (matches.length > 0) {
      matches.forEach((match, index) => {
        console.log(`   Match ${index + 1}:`);
        console.log(`   - Titre: ${match.title}`);
        console.log(`   - Score: ${match.match_score || 'N/A'}`);
        console.log(`   - Distance: ${match.distance_km ? match.distance_km.toFixed(2) + ' km' : 'N/A'}`);
      });
    }
    
    // 5. Test des statistiques
    console.log('\n5. Test des statistiques...');
    const stats = await dbService.getGlobalStats();
    console.log('✅ Statistiques:');
    console.log(`   - Utilisateurs: ${stats.totalUsers}`);
    console.log(`   - Ressources: ${stats.totalResources || 'N/A'}`);
    console.log(`   - Correspondances: ${stats.totalMatches}`);
    
    console.log('\n🎉 Tests de base réussis !');
    
    if (dbService.isUsingSupabase()) {
      console.log('\n📋 Prochaine étape:');
      console.log('Appliquez le schéma économique complet pour activer:');
      console.log('• 💰 Portefeuilles et NoweeCoins');
      console.log('• 🔄 Système de troc');
      console.log('• 🏆 Récompenses automatiques');
    }
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

testSupabaseBasic();
