/**
 * NOWEE PRO - Design System World-Class
 * Version professionnelle pour lancement mondial
 * Inspiré par les meilleurs design systems (Apple, Google, Stripe)
 */

/* ===== VARIABLES CSS PROFESSIONNELLES ===== */
:root {
  /* === PALETTE COULEURS NOWEE PRO === */
  
  /* Couleurs Primaires - Identité Nowee */
  --nowee-primary: #2563eb;           /* Bleu confiance et innovation */
  --nowee-primary-light: #3b82f6;     /* Bleu clair interactions */
  --nowee-primary-dark: #1d4ed8;      /* Bleu foncé profondeur */
  --nowee-primary-50: #eff6ff;        /* Bleu très clair backgrounds */
  --nowee-primary-100: #dbeafe;       /* Bleu clair subtle */
  --nowee-primary-900: #1e3a8a;       /* Bleu très foncé textes */
  
  /* Couleurs Secondaires - Cha<PERSON>ur <PERSON> */
  --nowee-secondary: #f59e0b;         /* Orange chaleur et énergie */
  --nowee-secondary-light: #fbbf24;   /* Orange clair accents */
  --nowee-secondary-dark: #d97706;    /* Orange foncé emphasis */
  
  /* Couleurs Système - Feedback Utilisateur */
  --nowee-success: #10b981;           /* Vert succès */
  --nowee-success-light: #34d399;     /* Vert clair */
  --nowee-success-bg: #ecfdf5;        /* Background succès */
  
  --nowee-warning: #f59e0b;           /* Orange attention */
  --nowee-warning-light: #fbbf24;     /* Orange clair */
  --nowee-warning-bg: #fffbeb;        /* Background warning */
  
  --nowee-error: #ef4444;             /* Rouge erreur */
  --nowee-error-light: #f87171;       /* Rouge clair */
  --nowee-error-bg: #fef2f2;          /* Background erreur */
  
  --nowee-info: #3b82f6;              /* Bleu information */
  --nowee-info-light: #60a5fa;        /* Bleu clair */
  --nowee-info-bg: #eff6ff;           /* Background info */
  
  /* Couleurs Neutres - Hiérarchie Visuelle */
  --nowee-gray-50: #f9fafb;           /* Background très clair */
  --nowee-gray-100: #f3f4f6;          /* Background clair */
  --nowee-gray-200: #e5e7eb;          /* Borders subtiles */
  --nowee-gray-300: #d1d5db;          /* Borders normales */
  --nowee-gray-400: #9ca3af;          /* Texte secondaire */
  --nowee-gray-500: #6b7280;          /* Texte tertiaire */
  --nowee-gray-600: #4b5563;          /* Texte principal */
  --nowee-gray-700: #374151;          /* Texte emphasis */
  --nowee-gray-800: #1f2937;          /* Texte fort */
  --nowee-gray-900: #111827;          /* Texte maximum */
  
  /* Couleurs Spéciales - Nowee Magic */
  --nowee-mesh: #8b5cf6;              /* Violet mesh networking */
  --nowee-mesh-light: #a78bfa;        /* Violet clair */
  --nowee-mesh-bg: #f5f3ff;           /* Background mesh */
  
  --nowee-coins: #f59e0b;             /* Orange NoweeCoins */
  --nowee-coins-light: #fbbf24;       /* Orange clair coins */
  --nowee-coins-bg: #fffbeb;          /* Background coins */
  
  --nowee-community: #10b981;         /* Vert communauté */
  --nowee-community-light: #34d399;   /* Vert clair */
  --nowee-community-bg: #ecfdf5;      /* Background communauté */
  
  /* === TYPOGRAPHIE PROFESSIONNELLE === */
  
  /* Familles de Polices */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-display: 'Cal Sans', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  
  /* Échelle Typographique Harmonieuse */
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;     /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */
  --text-2xl: 1.5rem;      /* 24px */
  --text-3xl: 1.875rem;    /* 30px */
  --text-4xl: 2.25rem;     /* 36px */
  --text-5xl: 3rem;        /* 48px */
  --text-6xl: 3.75rem;     /* 60px */
  --text-7xl: 4.5rem;      /* 72px */
  
  /* Poids de Police */
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;
  
  /* Hauteurs de Ligne */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  /* === ESPACEMENTS COHÉRENTS === */
  
  /* Échelle d'Espacement (basée sur 4px) */
  --space-0: 0;
  --space-1: 0.25rem;      /* 4px */
  --space-2: 0.5rem;       /* 8px */
  --space-3: 0.75rem;      /* 12px */
  --space-4: 1rem;         /* 16px */
  --space-5: 1.25rem;      /* 20px */
  --space-6: 1.5rem;       /* 24px */
  --space-8: 2rem;         /* 32px */
  --space-10: 2.5rem;      /* 40px */
  --space-12: 3rem;        /* 48px */
  --space-16: 4rem;        /* 64px */
  --space-20: 5rem;        /* 80px */
  --space-24: 6rem;        /* 96px */
  --space-32: 8rem;        /* 128px */
  
  /* === RAYONS DE BORDURE === */
  
  --radius-none: 0;
  --radius-sm: 0.125rem;    /* 2px */
  --radius-base: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;    /* 6px */
  --radius-lg: 0.5rem;      /* 8px */
  --radius-xl: 0.75rem;     /* 12px */
  --radius-2xl: 1rem;       /* 16px */
  --radius-3xl: 1.5rem;     /* 24px */
  --radius-full: 9999px;    /* Cercle parfait */
  
  /* === OMBRES ÉLÉGANTES === */
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* Ombres Colorées Nowee */
  --shadow-primary: 0 10px 15px -3px rgba(37, 99, 235, 0.1), 0 4px 6px -2px rgba(37, 99, 235, 0.05);
  --shadow-secondary: 0 10px 15px -3px rgba(245, 158, 11, 0.1), 0 4px 6px -2px rgba(245, 158, 11, 0.05);
  --shadow-success: 0 10px 15px -3px rgba(16, 185, 129, 0.1), 0 4px 6px -2px rgba(16, 185, 129, 0.05);
  
  /* === TRANSITIONS FLUIDES === */
  
  --transition-fast: 150ms ease-in-out;
  --transition-base: 200ms ease-in-out;
  --transition-slow: 300ms ease-in-out;
  --transition-slower: 500ms ease-in-out;
  
  /* Courbes d'Animation Professionnelles */
  --ease-in-out-cubic: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out-cubic: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-cubic: cubic-bezier(0.4, 0, 1, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* === BREAKPOINTS RESPONSIVE === */
  
  --breakpoint-sm: 640px;   /* Mobile large */
  --breakpoint-md: 768px;   /* Tablette */
  --breakpoint-lg: 1024px;  /* Desktop */
  --breakpoint-xl: 1280px;  /* Desktop large */
  --breakpoint-2xl: 1536px; /* Desktop très large */
  
  /* === Z-INDEX HIÉRARCHIE === */
  
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ===== RESET ET BASE PROFESSIONNELS ===== */

/* Reset Moderne */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: var(--leading-normal);
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  color: var(--nowee-gray-900);
  background-color: var(--nowee-gray-50);
  line-height: var(--leading-normal);
}

/* ===== CLASSES UTILITAIRES PROFESSIONNELLES ===== */

/* Typographie */
.text-display {
  font-family: var(--font-display);
  font-weight: var(--font-bold);
  letter-spacing: -0.025em;
}

.text-gradient-primary {
  background: linear-gradient(135deg, var(--nowee-primary), var(--nowee-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-mesh {
  background: linear-gradient(135deg, var(--nowee-mesh), var(--nowee-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Conteneurs */
.container-nowee {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 640px) {
  .container-nowee {
    padding: 0 var(--space-6);
  }
}

@media (min-width: 1024px) {
  .container-nowee {
    padding: 0 var(--space-8);
  }
}

/* Grilles Responsives */
.grid-nowee {
  display: grid;
  gap: var(--space-6);
}

.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Flexbox Utilitaires */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* Animations Professionnelles */
.animate-fade-in {
  animation: fadeIn var(--transition-base) var(--ease-out-cubic);
}

.animate-slide-up {
  animation: slideUp var(--transition-base) var(--ease-out-cubic);
}

.animate-bounce-in {
  animation: bounceIn var(--transition-slow) var(--ease-bounce);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes bounceIn {
  0% { 
    opacity: 0; 
    transform: scale(0.3); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.05); 
  }
  70% { 
    transform: scale(0.9); 
  }
  100% { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* États Interactifs */
.interactive {
  transition: all var(--transition-base) var(--ease-in-out-cubic);
  cursor: pointer;
}

.interactive:hover {
  transform: translateY(-2px);
}

.interactive:active {
  transform: translateY(0);
}

/* Focus Accessible */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus {
  outline: 2px solid var(--nowee-primary);
  outline-offset: 2px;
}

/* Responsive Utilities */
.hide-mobile {
  display: none;
}

@media (min-width: 768px) {
  .hide-mobile {
    display: block;
  }
  
  .hide-desktop {
    display: none;
  }
}

/* ===== COMPOSANTS DE BASE ===== */

/* Cartes Élégantes */
.card-nowee {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-base);
  border: 1px solid var(--nowee-gray-200);
  transition: all var(--transition-base) var(--ease-in-out-cubic);
}

.card-nowee:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-nowee-primary {
  border-color: var(--nowee-primary-100);
  box-shadow: var(--shadow-primary);
}

.card-nowee-mesh {
  background: linear-gradient(135deg, var(--nowee-mesh-bg), white);
  border-color: var(--nowee-mesh-light);
}

/* Badges Professionnels */
.badge-nowee {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: var(--leading-tight);
}

.badge-primary {
  background: var(--nowee-primary-100);
  color: var(--nowee-primary-900);
}

.badge-success {
  background: var(--nowee-success-bg);
  color: var(--nowee-success);
}

.badge-warning {
  background: var(--nowee-warning-bg);
  color: var(--nowee-warning);
}

.badge-mesh {
  background: var(--nowee-mesh-bg);
  color: var(--nowee-mesh);
}

/* Indicateurs de Statut */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  margin-right: var(--space-2);
}

.status-online {
  background: var(--nowee-success);
  box-shadow: 0 0 0 2px var(--nowee-success-bg);
}

.status-offline {
  background: var(--nowee-gray-400);
  box-shadow: 0 0 0 2px var(--nowee-gray-100);
}

.status-warning {
  background: var(--nowee-warning);
  box-shadow: 0 0 0 2px var(--nowee-warning-bg);
}

/* ===== THÈME SOMBRE (OPTIONNEL) ===== */

@media (prefers-color-scheme: dark) {
  :root {
    --nowee-gray-50: #111827;
    --nowee-gray-100: #1f2937;
    --nowee-gray-200: #374151;
    --nowee-gray-300: #4b5563;
    --nowee-gray-400: #6b7280;
    --nowee-gray-500: #9ca3af;
    --nowee-gray-600: #d1d5db;
    --nowee-gray-700: #e5e7eb;
    --nowee-gray-800: #f3f4f6;
    --nowee-gray-900: #f9fafb;
  }
  
  body {
    background-color: var(--nowee-gray-50);
    color: var(--nowee-gray-900);
  }
  
  .card-nowee {
    background: var(--nowee-gray-100);
    border-color: var(--nowee-gray-200);
  }
}
