#!/usr/bin/env node

/**
 * Serveur de test basique
 */

import 'dotenv/config';
import express from 'express';
import bodyParser from 'body-parser';

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

// Route de santé
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'Serveur Nowee fonctionnel',
    version: '1.0.0'
  });
});

// Route de test
app.post('/test', (req, res) => {
  const { message } = req.body;
  
  res.json({
    success: true,
    echo: message || 'Hello Nowee!',
    timestamp: new Date().toISOString()
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur Nowee démarré sur le port ${PORT}`);
  console.log(`🔗 Health: http://localhost:${PORT}/health`);
  console.log(`🧪 Test: POST http://localhost:${PORT}/test`);
});
