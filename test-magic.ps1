Write-Host "🎊 DÉCOUVERTE DE LA MAGIE NOWEE 🎊" -ForegroundColor Magenta
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Test 1: Chat IA
Write-Host "🤖 Test 1: Chat IA Contextuel" -ForegroundColor Blue
$chatBody = '{"message":"Salut Nowee ! J ai besoin d aide pour demenager ce weekend a Dakar","phone":"+221701234567","context":"test_magic"}'

try {
    $chatResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/chat/ai" -Method POST -Body $chatBody -ContentType "application/json"
    $chatResult = $chatResponse.Content | ConvertFrom-Json
    
    Write-Host "✅ IA Nowee répond:" -ForegroundColor Green
    Write-Host $chatResult.response -ForegroundColor Yellow
    Write-Host "📊 Modèle: $($chatResult.model) | Tokens: $($chatResult.tokensUsed)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Erreur Chat IA: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "----------------------------------------" -ForegroundColor Cyan

# Test 2: Portefeuille Économique
Write-Host "💰 Test 2: Système Économique" -ForegroundColor Blue

try {
    $walletResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/economy/wallet/+221701234567"
    $walletResult = $walletResponse.Content | ConvertFrom-Json
    
    Write-Host "✅ Portefeuille Aminata:" -ForegroundColor Green
    Write-Host "💰 NoweeCoins: $($walletResult.wallet.noweeCoins)" -ForegroundColor Yellow
    Write-Host "⏰ Crédits Temps: $($walletResult.wallet.timeCredits)h" -ForegroundColor Yellow
    Write-Host "⭐ Réputation: $($walletResult.wallet.reputation)" -ForegroundColor Yellow
    Write-Host "🏆 Niveau: $($walletResult.wallet.stats.reputationLevel)" -ForegroundColor Yellow
    Write-Host "📊 Valeur Totale: $($walletResult.wallet.stats.totalValue)" -ForegroundColor Yellow
} catch {
    Write-Host "❌ Erreur Portefeuille: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "----------------------------------------" -ForegroundColor Cyan

# Test 3: Besoins Locaux
Write-Host "🗺️ Test 3: Besoins Géolocalisés" -ForegroundColor Blue

try {
    $needsResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/needs"
    $needsResult = $needsResponse.Content | ConvertFrom-Json
    
    Write-Host "✅ Besoins disponibles:" -ForegroundColor Green
    foreach ($need in $needsResult.needs) {
        Write-Host "📍 $($need.title) - $($need.address)" -ForegroundColor Yellow
        Write-Host "   Catégorie: $($need.category) | Urgence: $($need.urgency)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ Erreur Besoins: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "----------------------------------------" -ForegroundColor Cyan

# Test 4: Statistiques Avancées
Write-Host "📊 Test 4: Analytics Communautaires" -ForegroundColor Blue

try {
    $statsResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/stats/advanced"
    $statsResult = $statsResponse.Content | ConvertFrom-Json
    
    Write-Host "✅ Statistiques Nowee:" -ForegroundColor Green
    Write-Host "👥 Utilisateurs: $($statsResult.stats.community.totalUsers)" -ForegroundColor Yellow
    Write-Host "🤝 Aides: $($statsResult.stats.community.totalNeeds)" -ForegroundColor Yellow
    Write-Host "💰 NoweeCoins: $($statsResult.stats.economy.totalCoins)" -ForegroundColor Yellow
    Write-Host "🕸️ Nœuds Mesh: $($statsResult.stats.mesh.totalNodes)" -ForegroundColor Yellow
    Write-Host "🤖 Conversations IA: $($statsResult.stats.ai.totalConversations)" -ForegroundColor Yellow
    Write-Host "😊 Satisfaction: $([math]::Round($statsResult.stats.ai.satisfactionRate * 100))%" -ForegroundColor Yellow
} catch {
    Write-Host "❌ Erreur Stats: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "----------------------------------------" -ForegroundColor Cyan

# Test 5: Connectivité APIs
Write-Host "🔗 Test 5: Connectivité APIs Externes" -ForegroundColor Blue

try {
    $connectResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/test/connectivity"
    $connectResult = $connectResponse.Content | ConvertFrom-Json
    
    Write-Host "✅ État des APIs:" -ForegroundColor Green
    Write-Host "🤖 OpenAI: $($connectResult.openai.status) - $($connectResult.openai.response)" -ForegroundColor Yellow
    Write-Host "📱 Twilio: $($connectResult.twilio.status) - $($connectResult.twilio.response)" -ForegroundColor Yellow
} catch {
    Write-Host "❌ Erreur Connectivité: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎊 MAGIE NOWEE DÉCOUVERTE ! 🎊" -ForegroundColor Magenta
Write-Host "Toutes les fonctionnalités sont opérationnelles !" -ForegroundColor Green
