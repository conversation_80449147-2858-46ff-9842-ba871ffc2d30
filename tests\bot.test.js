/**
 * Tests unitaires pour le bot WhatsApp Nowee
 */

import { jest } from '@jest/globals';

// Mock des modules externes
jest.mock('openai');
jest.mock('twilio');

describe('Nowee WhatsApp Bot', () => {
  let app;
  let request;

  beforeAll(async () => {
    // Configuration des variables d'environnement pour les tests
    process.env.OPENAI_API_KEY = 'test-key';
    process.env.TWILIO_ACCOUNT_SID = 'test-sid';
    process.env.TWILIO_AUTH_TOKEN = 'test-token';
    
    // Import dynamique après configuration des env vars
    const { default: testApp } = await import('../src/bot/nowee-whatsapp-bot.js');
    app = testApp;
    
    // Import de supertest pour les tests HTTP
    const supertest = await import('supertest');
    request = supertest.default(app);
  });

  describe('Route de santé', () => {
    test('GET /health devrait retourner le statut OK', async () => {
      const response = await request
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'OK');
      expect(response.body).toHaveProperty('service', 'Nowee WhatsApp Bot');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('Route des statistiques', () => {
    test('GET /stats devrait retourner les statistiques', async () => {
      const response = await request
        .get('/stats')
        .expect(200);

      expect(response.body).toHaveProperty('totalUsers');
      expect(response.body).toHaveProperty('totalMessages');
      expect(response.body).toHaveProperty('uptime');
      expect(typeof response.body.totalUsers).toBe('number');
    });
  });

  describe('Webhook WhatsApp', () => {
    test('POST /webhook avec message vide devrait retourner message d\'accueil', async () => {
      const response = await request
        .post('/webhook')
        .send({
          Body: '',
          From: 'whatsapp:+221771234567'
        })
        .expect(200);

      expect(response.text).toContain('Bonjour');
      expect(response.headers['content-type']).toContain('text/xml');
    });

    test('POST /webhook avec "bonjour" devrait retourner message d\'accueil', async () => {
      const response = await request
        .post('/webhook')
        .send({
          Body: 'bonjour',
          From: 'whatsapp:+221771234567'
        })
        .expect(200);

      expect(response.text).toContain('Salut');
      expect(response.text).toContain('Nowee');
    });

    test('POST /webhook devrait gérer les erreurs gracieusement', async () => {
      // Test avec des données malformées
      const response = await request
        .post('/webhook')
        .send({
          // Pas de Body ni From
        })
        .expect(200);

      expect(response.headers['content-type']).toContain('text/xml');
    });
  });
});

describe('Utilitaires de localisation', () => {
  let locationUtils;

  beforeAll(async () => {
    locationUtils = await import('../src/utils/locationUtils.js');
  });

  describe('extractLocationFromMessage', () => {
    test('devrait extraire Dakar du message', () => {
      const message = "J'ai besoin d'une perceuse à Dakar";
      const location = locationUtils.extractLocationFromMessage(message);
      
      expect(location).not.toBeNull();
      expect(location.city).toBe('Dakar');
      expect(location.country).toBe('Sénégal');
    });

    test('devrait extraire Paris du message', () => {
      const message = "Qui peut m'aider à Paris ?";
      const location = locationUtils.extractLocationFromMessage(message);
      
      expect(location).not.toBeNull();
      expect(location.city).toBe('Paris');
      expect(location.country).toBe('France');
    });

    test('devrait retourner null si aucune ville trouvée', () => {
      const message = "J'ai besoin d'aide";
      const location = locationUtils.extractLocationFromMessage(message);
      
      expect(location).toBeNull();
    });

    test('devrait gérer les messages vides', () => {
      const location = locationUtils.extractLocationFromMessage('');
      expect(location).toBeNull();
      
      const location2 = locationUtils.extractLocationFromMessage(null);
      expect(location2).toBeNull();
    });
  });

  describe('formatLocation', () => {
    test('devrait formater correctement une localisation', () => {
      const location = {
        city: 'Dakar',
        country: 'Sénégal'
      };
      
      const formatted = locationUtils.formatLocation(location);
      expect(formatted).toBe('Dakar, Sénégal');
    });

    test('devrait gérer les localisations nulles', () => {
      const formatted = locationUtils.formatLocation(null);
      expect(formatted).toBe('Localisation inconnue');
    });
  });

  describe('calculateDistance', () => {
    test('devrait calculer la distance entre Paris et Marseille', () => {
      // Coordonnées approximatives
      const parisLat = 48.8566, parisLng = 2.3522;
      const marseilleLat = 43.2965, marseilleLng = 5.3698;
      
      const distance = locationUtils.calculateDistance(
        parisLat, parisLng, 
        marseilleLat, marseilleLng
      );
      
      // Distance approximative Paris-Marseille: ~660km
      expect(distance).toBeGreaterThan(600);
      expect(distance).toBeLessThan(700);
    });

    test('devrait retourner 0 pour des points identiques', () => {
      const distance = locationUtils.calculateDistance(48.8566, 2.3522, 48.8566, 2.3522);
      expect(distance).toBe(0);
    });
  });
});
