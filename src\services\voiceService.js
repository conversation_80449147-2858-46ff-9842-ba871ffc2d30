/**
 * Service vocal pour Nowee
 * Gère la reconnaissance vocale et la synthèse vocale
 */

import OpenAI from 'openai';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { dbService } from './databaseService.js';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Configuration OpenAI pour la reconnaissance vocale
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Langues supportées
const SUPPORTED_LANGUAGES = {
  'fr': 'français',
  'en': 'english',
  'wo': 'wolof',
  'ar': 'arabic',
  'es': 'español',
  'pt': 'português'
};

/**
 * Service vocal principal
 */
export class VoiceService {
  
  /**
   * Transcrit un fichier audio en texte
   */
  static async transcribeAudio(audioBuffer, language = 'fr') {
    try {
      // Sauvegarder temporairement le fichier audio
      const tempPath = path.join(__dirname, '../../temp', `audio_${Date.now()}.ogg`);
      await this.ensureTempDir();
      await fs.writeFile(tempPath, audioBuffer);

      // Transcrire avec OpenAI Whisper
      const transcription = await openai.audio.transcriptions.create({
        file: fs.createReadStream(tempPath),
        model: 'whisper-1',
        language: language,
        response_format: 'json',
        temperature: 0.2
      });

      // Nettoyer le fichier temporaire
      await fs.unlink(tempPath).catch(() => {});

      return {
        success: true,
        text: transcription.text,
        language: language,
        confidence: transcription.confidence || 0.9
      };

    } catch (error) {
      console.error('Erreur transcription audio:', error);
      return {
        success: false,
        error: error.message,
        text: null
      };
    }
  }

  /**
   * Convertit du texte en audio
   */
  static async textToSpeech(text, voice = 'alloy', language = 'fr') {
    try {
      // Adapter le texte selon la langue
      const adaptedText = this.adaptTextForLanguage(text, language);

      const mp3 = await openai.audio.speech.create({
        model: 'tts-1',
        voice: voice, // alloy, echo, fable, onyx, nova, shimmer
        input: adaptedText,
        response_format: 'mp3',
        speed: 0.9 // Légèrement plus lent pour la compréhension
      });

      const buffer = Buffer.from(await mp3.arrayBuffer());
      
      return {
        success: true,
        audioBuffer: buffer,
        format: 'mp3',
        text: adaptedText
      };

    } catch (error) {
      console.error('Erreur synthèse vocale:', error);
      return {
        success: false,
        error: error.message,
        audioBuffer: null
      };
    }
  }

  /**
   * Traite un message vocal complet (transcription + réponse + synthèse)
   */
  static async processVoiceMessage(audioBuffer, userPhone, language = 'fr') {
    try {
      console.log(`🎤 Traitement message vocal de ${userPhone}`);

      // 1. Transcrire l'audio
      const transcription = await this.transcribeAudio(audioBuffer, language);
      
      if (!transcription.success) {
        return {
          success: false,
          error: 'Impossible de comprendre l\'audio',
          audioResponse: await this.textToSpeech(
            "Désolé, je n'ai pas pu comprendre votre message vocal. Pouvez-vous répéter ?",
            'alloy',
            language
          )
        };
      }

      console.log(`📝 Transcription: "${transcription.text}"`);

      // 2. Traiter le message comme un message texte normal
      const { analyzeMessage, generateContextualPrompt } = await import('../ai/promptEngine.js');
      const userProfile = await dbService.getUserProfile(userPhone);
      
      // Analyser le message transcrit
      const messageAnalysis = analyzeMessage(transcription.text, userProfile);
      
      // Générer une réponse adaptée au vocal
      const contextualPrompt = this.adaptPromptForVoice(
        generateContextualPrompt(messageAnalysis),
        language
      );

      // 3. Générer la réponse IA
      let aiResponse;
      try {
        const completion = await openai.chat.completions.create({
          model: 'gpt-4o-mini',
          messages: [{ role: 'user', content: contextualPrompt }],
          max_tokens: 100, // Plus court pour le vocal
          temperature: 0.8,
        });
        
        aiResponse = completion.choices[0].message.content.trim();
      } catch (error) {
        console.error('Erreur OpenAI:', error);
        aiResponse = "Je vous ai bien entendu ! Laissez-moi vous aider avec votre demande.";
      }

      // 4. Convertir la réponse en audio
      const audioResponse = await this.textToSpeech(aiResponse, 'alloy', language);

      // 5. Enregistrer l'interaction
      await this.logVoiceInteraction(userPhone, transcription.text, aiResponse, language);

      return {
        success: true,
        transcription: transcription.text,
        textResponse: aiResponse,
        audioResponse: audioResponse.audioBuffer,
        language: language,
        analysis: messageAnalysis
      };

    } catch (error) {
      console.error('Erreur traitement message vocal:', error);
      return {
        success: false,
        error: error.message,
        audioResponse: await this.textToSpeech(
          "Une erreur s'est produite. Veuillez réessayer.",
          'alloy',
          language
        )
      };
    }
  }

  /**
   * Adapte le prompt pour les réponses vocales
   */
  static adaptPromptForVoice(originalPrompt, language) {
    const voiceInstructions = {
      'fr': `
INSTRUCTIONS SPÉCIALES POUR RÉPONSE VOCALE:
- Réponse TRÈS courte (maximum 2 phrases)
- Langage simple et clair
- Pas d'emojis
- Ton chaleureux et personnel
- Éviter les termes techniques
`,
      'en': `
SPECIAL INSTRUCTIONS FOR VOICE RESPONSE:
- VERY short response (maximum 2 sentences)
- Simple and clear language
- No emojis
- Warm and personal tone
- Avoid technical terms
`,
      'wo': `
INSTRUCTIONS SPÉCIALES POUR WOLOF:
- Réponse très courte
- Langage simple
- Ton respectueux
`
    };

    return originalPrompt + (voiceInstructions[language] || voiceInstructions['fr']);
  }

  /**
   * Adapte le texte selon la langue
   */
  static adaptTextForLanguage(text, language) {
    // Remplacer les emojis par des mots
    let adaptedText = text
      .replace(/👋/g, '')
      .replace(/🎯/g, '')
      .replace(/✅/g, 'Parfait!')
      .replace(/❌/g, 'Attention:')
      .replace(/🚨/g, 'Urgent:')
      .replace(/💡/g, 'Conseil:')
      .replace(/📱/g, '')
      .replace(/🤝/g, '');

    // Nettoyer les caractères spéciaux
    adaptedText = adaptedText
      .replace(/\*/g, '') // Enlever les *
      .replace(/`/g, '') // Enlever les `
      .replace(/\n\n+/g, '. ') // Remplacer les sauts de ligne par des points
      .replace(/\n/g, '. ')
      .trim();

    // Adaptations spécifiques par langue
    if (language === 'wo') {
      // Adaptations pour le wolof (à développer avec des locuteurs natifs)
      adaptedText = adaptedText
        .replace(/Bonjour/g, 'Asalaam aleekum')
        .replace(/Merci/g, 'Jërëjëf')
        .replace(/Au revoir/g, 'Ba beneen yoon');
    }

    return adaptedText;
  }

  /**
   * Détecte la langue d'un audio
   */
  static async detectLanguage(audioBuffer) {
    try {
      // Transcription sans spécifier la langue pour détecter automatiquement
      const tempPath = path.join(__dirname, '../../temp', `detect_${Date.now()}.ogg`);
      await this.ensureTempDir();
      await fs.writeFile(tempPath, audioBuffer);

      const transcription = await openai.audio.transcriptions.create({
        file: fs.createReadStream(tempPath),
        model: 'whisper-1',
        response_format: 'json'
      });

      await fs.unlink(tempPath).catch(() => {});

      // Détecter la langue du texte transcrit
      const detectedLanguage = await this.detectTextLanguage(transcription.text);
      
      return {
        language: detectedLanguage,
        text: transcription.text,
        confidence: 0.8
      };

    } catch (error) {
      console.error('Erreur détection langue:', error);
      return {
        language: 'fr', // Défaut français
        text: '',
        confidence: 0.5
      };
    }
  }

  /**
   * Détecte la langue d'un texte
   */
  static async detectTextLanguage(text) {
    // Patterns simples pour détecter les langues courantes
    const patterns = {
      'fr': /\b(je|tu|il|elle|nous|vous|ils|elles|le|la|les|un|une|des|et|ou|mais|donc|car|ni|or)\b/i,
      'en': /\b(i|you|he|she|we|they|the|a|an|and|or|but|so|because|if|when)\b/i,
      'wo': /\b(man|yow|moom|ñoom|nu|leen|ak|walla|waaye|ndax|su|bu)\b/i,
      'ar': /[\u0600-\u06FF]/,
      'es': /\b(yo|tú|él|ella|nosotros|vosotros|ellos|ellas|el|la|los|las|un|una|y|o|pero|porque)\b/i
    };

    for (const [lang, pattern] of Object.entries(patterns)) {
      if (pattern.test(text)) {
        return lang;
      }
    }

    return 'fr'; // Défaut
  }

  /**
   * Génère des instructions vocales pour les nouveaux utilisateurs
   */
  static async generateVoiceOnboarding(language = 'fr') {
    const onboardingTexts = {
      'fr': `Bienvenue sur Nowee ! Je suis votre assistant vocal d'entraide locale. 
             Vous pouvez me parler normalement pour exprimer vos besoins. 
             Par exemple, dites "J'ai besoin d'une perceuse" ou "Je peux aider pour un déménagement". 
             Je vous mettrai en contact avec des personnes près de chez vous qui peuvent vous aider.`,
      
      'en': `Welcome to Nowee! I'm your voice assistant for local mutual aid. 
             You can speak to me normally to express your needs. 
             For example, say "I need a drill" or "I can help with moving". 
             I'll connect you with people near you who can help.`,
      
      'wo': `Dalal ak jamm ci Nowee! Man la assistant vocal bu jëkkër ci walluy jëm. 
             Mën nga wax ak man ci mbir mi normal ngir wax sa besoins. 
             Misaal, wax "Dama soxla perceuse" walla "Mën naa walluy déménagement".`
    };

    const text = onboardingTexts[language] || onboardingTexts['fr'];
    return await this.textToSpeech(text, 'alloy', language);
  }

  /**
   * Crée le répertoire temporaire si nécessaire
   */
  static async ensureTempDir() {
    const tempDir = path.join(__dirname, '../../temp');
    try {
      await fs.access(tempDir);
    } catch {
      await fs.mkdir(tempDir, { recursive: true });
    }
  }

  /**
   * Enregistre une interaction vocale
   */
  static async logVoiceInteraction(userPhone, transcription, response, language) {
    try {
      await dbService.logEvent('voice_interaction', null, {
        userPhone,
        transcription,
        response,
        language,
        timestamp: new Date()
      });
    } catch (error) {
      console.error('Erreur log interaction vocale:', error);
    }
  }

  /**
   * Nettoie les fichiers temporaires anciens
   */
  static async cleanupTempFiles(maxAgeHours = 1) {
    try {
      const tempDir = path.join(__dirname, '../../temp');
      const files = await fs.readdir(tempDir);
      const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);

      for (const file of files) {
        const filePath = path.join(tempDir, file);
        const stats = await fs.stat(filePath);
        
        if (stats.mtime.getTime() < cutoffTime) {
          await fs.unlink(filePath);
        }
      }

      console.log(`🧹 Fichiers temporaires nettoyés (>${maxAgeHours}h)`);

    } catch (error) {
      console.error('Erreur nettoyage fichiers temporaires:', error);
    }
  }
}

export default VoiceService;
