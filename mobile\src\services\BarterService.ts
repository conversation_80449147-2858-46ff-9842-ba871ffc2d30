/**
 * Service de troc pour l'application mobile Nowee
 * Interface avec l'API backend pour les fonctionnalités de troc
 */

import axios, { AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configuration de l'API
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api/economy' 
  : 'https://nowee-app.herokuapp.com/api/economy';

// Types
interface BarterProposal {
  id: string;
  exchange_type: string;
  status: string;
  offered_coins: number;
  offered_time_hours: number;
  requested_coins: number;
  requested_time_hours: number;
  proposal_message: string;
  created_at: string;
  expires_at: string;
  is_proposer: boolean;
}

interface CreateProposalRequest {
  proposerPhone: string;
  targetPhone: string;
  offeredResourceId: string;
  requestedResourceId: string;
  exchangeType: string;
  offeredCoins?: number;
  offeredTimeHours?: number;
  requestedCoins?: number;
  requestedTimeHours?: number;
  conditions?: string;
  proposalMessage?: string;
}

interface BarterSuggestion {
  type: string;
  match_score: number;
  user_need: {
    id: string;
    title: string;
    category: string;
  };
  offered_resource: {
    id: string;
    title: string;
    category: string;
  };
  offerer_need: {
    id: string;
    title: string;
    category: string;
  };
}

interface Resource {
  id: string;
  title: string;
  description: string;
  category: string;
  type: 'NEED' | 'OFFER';
  user_id: string;
  created_at: string;
}

/**
 * Service de troc principal
 */
export class BarterService {
  private static instance: BarterService;
  private apiClient = axios.create({
    baseURL: API_BASE_URL,
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  constructor() {
    this.setupInterceptors();
  }

  static getInstance(): BarterService {
    if (!BarterService.instance) {
      BarterService.instance = new BarterService();
    }
    return BarterService.instance;
  }

  private setupInterceptors() {
    // Intercepteur de requête pour ajouter l'authentification
    this.apiClient.interceptors.request.use(
      async (config) => {
        try {
          const token = await AsyncStorage.getItem('auth_token');
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
        } catch (error) {
          console.warn('Erreur récupération token:', error);
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Intercepteur de réponse pour gérer les erreurs
    this.apiClient.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('Erreur API Barter:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Récupère les propositions de troc d'un utilisateur
   */
  async getUserProposals(phone: string, type: 'all' | 'sent' | 'received' = 'all'): Promise<BarterProposal[]> {
    try {
      const response: AxiosResponse<{ success: boolean; proposals: BarterProposal[] }> = 
        await this.apiClient.get(`/barter/${encodeURIComponent(phone)}?type=${type}`);
      
      if (!response.data.success) {
        throw new Error('Erreur récupération propositions');
      }

      return response.data.proposals;
    } catch (error) {
      console.error('Erreur getUserProposals:', error);
      // Retourner des données de test en cas d'erreur
      return this.getMockProposals();
    }
  }

  /**
   * Crée une nouvelle proposition de troc
   */
  async createProposal(userPhone: string, proposalData: Omit<CreateProposalRequest, 'proposerPhone'>): Promise<boolean> {
    try {
      const fullProposalData: CreateProposalRequest = {
        proposerPhone: userPhone,
        ...proposalData,
      };

      const response: AxiosResponse<{ success: boolean; message: string }> = 
        await this.apiClient.post('/barter/propose', fullProposalData);
      
      return response.data.success;
    } catch (error) {
      console.error('Erreur createProposal:', error);
      throw new Error('Impossible de créer la proposition de troc');
    }
  }

  /**
   * Accepte une proposition de troc
   */
  async acceptProposal(proposalId: string, acceptingPhone: string): Promise<boolean> {
    try {
      const response: AxiosResponse<{ success: boolean; message: string }> = 
        await this.apiClient.post(`/barter/${proposalId}/accept`, {
          acceptingPhone,
        });
      
      return response.data.success;
    } catch (error) {
      console.error('Erreur acceptProposal:', error);
      throw new Error('Impossible d\'accepter la proposition');
    }
  }

  /**
   * Rejette une proposition de troc
   */
  async rejectProposal(proposalId: string, rejectingPhone: string, reason?: string): Promise<boolean> {
    try {
      const response: AxiosResponse<{ success: boolean; message: string }> = 
        await this.apiClient.post(`/barter/${proposalId}/reject`, {
          rejectingPhone,
          reason,
        });
      
      return response.data.success;
    } catch (error) {
      console.error('Erreur rejectProposal:', error);
      throw new Error('Impossible de rejeter la proposition');
    }
  }

  /**
   * Récupère les suggestions de troc pour un utilisateur
   */
  async getBarterSuggestions(phone: string): Promise<BarterSuggestion[]> {
    try {
      const response: AxiosResponse<{ success: boolean; suggestions: BarterSuggestion[] }> = 
        await this.apiClient.get(`/barter/${encodeURIComponent(phone)}/suggestions`);
      
      if (!response.data.success) {
        throw new Error('Erreur récupération suggestions');
      }

      return response.data.suggestions;
    } catch (error) {
      console.error('Erreur getBarterSuggestions:', error);
      return [];
    }
  }

  /**
   * Récupère les ressources d'un utilisateur
   */
  async getUserResources(phone: string): Promise<Resource[]> {
    try {
      // Note: Cette API n'existe pas encore dans le backend, 
      // nous utilisons des données mock pour le moment
      return this.getMockResources();
    } catch (error) {
      console.error('Erreur getUserResources:', error);
      return [];
    }
  }

  /**
   * Cache les propositions de troc localement
   */
  async cacheProposals(phone: string, proposals: BarterProposal[]): Promise<void> {
    try {
      const cacheKey = `barter_proposals_${phone}`;
      const cacheData = {
        data: proposals,
        timestamp: Date.now(),
      };
      await AsyncStorage.setItem(cacheKey, JSON.stringify(cacheData));
    } catch (error) {
      console.error('Erreur cache proposals:', error);
    }
  }

  /**
   * Récupère les propositions depuis le cache
   */
  async getCachedProposals(phone: string): Promise<BarterProposal[] | null> {
    try {
      const cacheKey = `barter_proposals_${phone}`;
      const cachedData = await AsyncStorage.getItem(cacheKey);
      
      if (!cachedData) return null;
      
      const { data, timestamp } = JSON.parse(cachedData);
      
      // Vérifier si le cache n'est pas trop ancien (10 minutes)
      if (Date.now() - timestamp > 10 * 60 * 1000) {
        return null;
      }
      
      return data;
    } catch (error) {
      console.error('Erreur récupération cache proposals:', error);
      return null;
    }
  }

  /**
   * Synchronise les données de troc
   */
  async syncBarterData(phone: string): Promise<void> {
    try {
      const [proposals, suggestions] = await Promise.all([
        this.getUserProposals(phone),
        this.getBarterSuggestions(phone),
      ]);

      // Mettre en cache
      await this.cacheProposals(phone, proposals);
      
      // Cache des suggestions
      const suggestionsCacheKey = `barter_suggestions_${phone}`;
      await AsyncStorage.setItem(suggestionsCacheKey, JSON.stringify({
        data: suggestions,
        timestamp: Date.now(),
      }));

    } catch (error) {
      console.error('Erreur synchronisation troc:', error);
    }
  }

  /**
   * Données mock pour les propositions (développement)
   */
  private getMockProposals(): BarterProposal[] {
    return [
      {
        id: 'mock_1',
        exchange_type: 'DIRECT_BARTER',
        status: 'PENDING',
        offered_coins: 20,
        offered_time_hours: 1,
        requested_coins: 0,
        requested_time_hours: 0,
        proposal_message: 'Je propose 20 coins + 1h de mon temps contre votre perceuse',
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        is_proposer: true,
      },
      {
        id: 'mock_2',
        exchange_type: 'TIME_FOR_OBJECT',
        status: 'ACCEPTED',
        offered_coins: 0,
        offered_time_hours: 3,
        requested_coins: 15,
        requested_time_hours: 0,
        proposal_message: 'J\'offre 3h de cours de français contre 15 coins',
        created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        expires_at: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
        is_proposer: false,
      },
    ];
  }

  /**
   * Données mock pour les ressources (développement)
   */
  private getMockResources(): Resource[] {
    return [
      {
        id: 'resource_1',
        title: 'Perceuse électrique disponible',
        description: 'Je peux prêter ma perceuse pour des travaux',
        category: 'MATERIAL',
        type: 'OFFER',
        user_id: 'user_1',
        created_at: new Date().toISOString(),
      },
      {
        id: 'resource_2',
        title: 'Besoin d\'aide pour déménagement',
        description: 'Je cherche de l\'aide pour déménager ce weekend',
        category: 'SERVICE',
        type: 'NEED',
        user_id: 'user_1',
        created_at: new Date().toISOString(),
      },
      {
        id: 'resource_3',
        title: 'Cours de français',
        description: 'J\'offre des cours de français pour débutants',
        category: 'EDUCATION',
        type: 'OFFER',
        user_id: 'user_1',
        created_at: new Date().toISOString(),
      },
    ];
  }

  /**
   * Valide une proposition de troc
   */
  static validateProposal(proposal: Partial<CreateProposalRequest>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!proposal.targetPhone) {
      errors.push('Destinataire requis');
    }

    if (!proposal.offeredResourceId) {
      errors.push('Ressource offerte requise');
    }

    if (!proposal.requestedResourceId) {
      errors.push('Ressource demandée requise');
    }

    const totalOffered = (proposal.offeredCoins || 0) + (proposal.offeredTimeHours || 0);
    const totalRequested = (proposal.requestedCoins || 0) + (proposal.requestedTimeHours || 0);

    if (totalOffered === 0 && totalRequested === 0) {
      errors.push('Au moins une valeur d\'échange doit être spécifiée');
    }

    if ((proposal.offeredCoins || 0) > 1000) {
      errors.push('Maximum 1000 NoweeCoins par échange');
    }

    if ((proposal.offeredTimeHours || 0) > 40) {
      errors.push('Maximum 40 heures par échange');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Calcule la valeur équivalente d'un échange
   */
  static calculateExchangeValue(coins: number, timeHours: number, coinsPerHour: number = 50): number {
    return coins + (timeHours * coinsPerHour);
  }

  /**
   * Formate le type d'échange pour l'affichage
   */
  static formatExchangeType(exchangeType: string): string {
    const typeMap: { [key: string]: string } = {
      'DIRECT_BARTER': 'Troc Direct',
      'TIME_FOR_OBJECT': 'Temps contre Objet',
      'SERVICE_FOR_OBJECT': 'Service contre Objet',
      'MIXED_EXCHANGE': 'Échange Mixte',
    };

    return typeMap[exchangeType] || exchangeType;
  }

  /**
   * Formate le statut d'une proposition
   */
  static formatProposalStatus(status: string): { text: string; color: string; icon: string } {
    const statusMap: { [key: string]: { text: string; color: string; icon: string } } = {
      'PENDING': { text: 'En attente', color: '#FF9800', icon: 'hourglass-empty' },
      'ACCEPTED': { text: 'Accepté', color: '#4CAF50', icon: 'check-circle' },
      'REJECTED': { text: 'Rejeté', color: '#F44336', icon: 'cancel' },
      'COUNTER': { text: 'Contre-proposition', color: '#2196F3', icon: 'swap-horiz' },
      'COMPLETED': { text: 'Terminé', color: '#9C27B0', icon: 'done-all' },
      'CANCELLED': { text: 'Annulé', color: '#607D8B', icon: 'block' },
    };

    return statusMap[status] || { text: status, color: '#757575', icon: 'help' };
  }

  /**
   * Vérifie si une proposition a expiré
   */
  static isProposalExpired(expiresAt: string): boolean {
    return new Date(expiresAt) < new Date();
  }

  /**
   * Calcule le temps restant avant expiration
   */
  static getTimeUntilExpiration(expiresAt: string): string {
    const now = new Date();
    const expiry = new Date(expiresAt);
    const diffMs = expiry.getTime() - now.getTime();

    if (diffMs <= 0) {
      return 'Expiré';
    }

    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (diffDays > 0) {
      return `${diffDays}j ${diffHours}h`;
    } else if (diffHours > 0) {
      return `${diffHours}h`;
    } else {
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      return `${diffMinutes}min`;
    }
  }
}

// Instance singleton
export const barterService = BarterService.getInstance();
export default barterService;
