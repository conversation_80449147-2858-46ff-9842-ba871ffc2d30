{"name": "nowee", "version": "1.0.0", "description": "Nowee - Application d'entraide locale en temps réel", "main": "src/bot/nowee-whatsapp-bot.js", "type": "module", "scripts": {"start": "node src/bot/nowee-whatsapp-bot.js", "dev": "nodemon src/bot/nowee-whatsapp-bot.js", "test": "jest", "lint": "eslint src/", "format": "prettier --write src/"}, "keywords": ["entraide", "local", "whatsapp", "bot", "communauté", "solidarité"], "author": "Nowee Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "twilio": "^4.19.0", "openai": "^4.24.1", "dotenv": "^16.3.1", "body-parser": "^1.20.2", "cors": "^2.8.5", "helmet": "^7.1.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.56.0", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0"}}