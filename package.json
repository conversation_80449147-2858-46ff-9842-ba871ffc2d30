{"name": "nowee", "version": "1.0.0", "description": "Nowee - Application d'entraide locale en temps réel", "main": "src/bot/nowee-whatsapp-bot.js", "type": "module", "scripts": {"start": "node src/bot/nowee-whatsapp-bot.js", "dev": "nodemon src/bot/nowee-whatsapp-bot.js", "test": "jest", "lint": "eslint src/", "format": "prettier --write src/", "setup": "node setup-keys.js", "verify": "node verify-setup.js", "tunnel": "npx ngrok http 3000", "setup-complete": "npm run setup && npm run verify", "setup-db": "node setup-database.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "node prisma/seed.js", "full-setup": "npm run setup && npm run setup-db && npm run verify", "test:matching": "node test-matching.js", "test:matching:clean": "node test-matching.js --cleanup", "test:voice": "node test-voice.js", "test:voice:clean": "node test-voice.js --cleanup"}, "keywords": ["entraide", "local", "whatsapp", "bot", "communauté", "solidarité"], "author": "Nowee Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "openai": "^4.24.1", "twilio": "^4.19.0", "uuid": "^9.0.1"}, "devDependencies": {"eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "prisma": "^5.7.1"}, "engines": {"node": ">=18.0.0"}}