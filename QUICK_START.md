# 🚀 Démarrage Rapide - Nowee Bot

Guide express pour configurer et lancer votre bot Nowee en 10 minutes !

## ⚡ Installation Express

### 1. Installer les dépendances
```bash
npm install
```

### 2. Configuration automatique des clés API
```bash
npm run setup
```
Ce script interactif vous guidera pour :
- ✅ Obtenir votre clé OpenAI
- ✅ Configurer Twilio
- ✅ Créer votre fichier .env

### 3. Vérifier la configuration
```bash
npm run verify
```
Ce script teste toutes vos connexions API.

### 4. Démarrer le bot
```bash
npm start
```

### 5. Exposer le webhook (dans un autre terminal)
```bash
npm run tunnel
```
Copiez l'URL HTTPS générée par ngrok.

### 6. Configurer le webhook Twilio
1. Allez sur [Twilio Console](https://console.twilio.com/)
2. **Messaging** > **Settings** > **WhatsApp sandbox settings**
3. Collez votre URL ngrok + `/webhook`
   - Exemple: `https://abc123.ngrok.io/webhook`
4. Sauvegardez

### 7. Tester le bot
1. Envoyez `join xxxxx-xxxxx` au numéro Twilio WhatsApp
2. Envoyez `Bonjour` pour tester
3. Testez avec `J'ai besoin d'une perceuse à Dakar`

## 🎯 Commandes Utiles

```bash
# Configuration complète automatique
npm run setup-complete

# Démarrage en mode développement (auto-reload)
npm run dev

# Tests unitaires
npm test

# Vérification de santé du bot
curl http://localhost:3000/health

# Statistiques d'usage
curl http://localhost:3000/stats
```

## 🔧 Dépannage Express

### Bot ne répond pas ?
```bash
# Vérifiez la configuration
npm run verify

# Vérifiez les logs du serveur
# Regardez la console où tourne `npm start`

# Testez l'endpoint de santé
curl http://localhost:3000/health
```

### Webhook ne fonctionne pas ?
1. ✅ ngrok est-il démarré ? (`npm run tunnel`)
2. ✅ URL webhook configurée dans Twilio ?
3. ✅ URL commence par `https://` ?
4. ✅ Bot démarré sur le port 3000 ?

### Erreurs API ?
```bash
# Re-vérifiez vos clés
npm run verify

# Reconfigurez si nécessaire
npm run setup
```

## 📱 Test Rapide

Une fois tout configuré, testez ces messages :

```
Utilisateur: "Bonjour"
Bot: Message d'accueil avec instructions

Utilisateur: "J'ai besoin d'une perceuse à Dakar"
Bot: Réponse contextuelle avec suggestions

Utilisateur: "Qui peut m'aider à déménager ?"
Bot: Proposition de connexion avec la communauté
```

## 🎉 Félicitations !

Votre bot Nowee est maintenant opérationnel ! 

**Prochaines étapes :**
- Invitez des amis à tester
- Consultez les statistiques sur `/stats`
- Explorez le code dans `src/bot/`
- Lisez la documentation complète dans `docs/`

**Besoin d'aide ?**
- 📖 Documentation complète : `README.md`
- 🔧 Guide détaillé API : `docs/SETUP_API_KEYS.md`
- 🏗️ Architecture : `docs/ARCHITECTURE.md`

---

**Nowee** - *L'entraide locale en temps réel* 🌍❤️
