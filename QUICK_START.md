# 🚀 Démarrage Rapide - Nowee Complet

Guide express pour configurer et lancer votre écosystème Nowee complet !

## 🎯 Vue d'ensemble

Nowee est maintenant un écosystème complet comprenant :
- 🤖 **Bot WhatsApp intelligent** avec IA avancée
- 🗄️ **Base de données PostgreSQL** pour la persistance
- ☁️ **Déploiement cloud** prêt pour la production
- 📱 **Application mobile React Native** (structure créée)

## ⚡ Installation Express

### 1. Installer les dépendances
```bash
npm install
```

### 2. Configuration complète automatique
```bash
npm run full-setup
```
Ce script fait tout automatiquement :
- ✅ Configuration des clés API (OpenAI + Twilio)
- ✅ Configuration de la base de données PostgreSQL
- ✅ Vérification de toutes les connexions

**OU configuration étape par étape :**

#### 2a. Configuration des clés API
```bash
npm run setup
```

#### 2b. Configuration de la base de données
```bash
npm run setup-db
```

#### 2c. Vérification complète
```bash
npm run verify
```

### 3. Démarrer le bot
```bash
npm start
```

### 4. Exposer le webhook (dans un autre terminal)
```bash
npm run tunnel
```
Copiez l'URL HTTPS générée par ngrok.

### 5. Configurer le webhook Twilio
1. Allez sur [Twilio Console](https://console.twilio.com/)
2. **Messaging** > **Settings** > **WhatsApp sandbox settings**
3. Collez votre URL ngrok + `/webhook`
   - Exemple: `https://abc123.ngrok.io/webhook`
4. Sauvegardez

### 6. Tester le bot
1. Envoyez `join xxxxx-xxxxx` au numéro Twilio WhatsApp
2. Envoyez `Bonjour` pour tester
3. Testez avec `J'ai besoin d'une perceuse à Dakar`

## 🎯 Commandes Utiles

### Bot WhatsApp
```bash
# Configuration complète automatique
npm run full-setup

# Démarrage en mode développement (auto-reload)
npm run dev

# Tests unitaires
npm test

# Vérification de santé du bot
curl http://localhost:3000/health

# Statistiques d'usage
curl http://localhost:3000/stats
```

### Base de données
```bash
# Interface graphique Prisma
npm run db:studio

# Réinitialiser avec des données de test
npm run db:seed

# Générer le client Prisma après modification du schéma
npm run db:generate
```

### Application mobile (optionnel)
```bash
cd mobile

# Installer les dépendances
npm install

# Démarrer Metro (serveur de développement)
npm start

# Lancer sur Android
npm run android

# Lancer sur iOS
npm run ios
```

## 🔧 Dépannage Express

### Bot ne répond pas ?
```bash
# Vérifiez la configuration complète
npm run verify

# Vérifiez les logs du serveur
# Regardez la console où tourne `npm start`

# Testez l'endpoint de santé
curl http://localhost:3000/health
```

### Webhook ne fonctionne pas ?
1. ✅ ngrok est-il démarré ? (`npm run tunnel`)
2. ✅ URL webhook configurée dans Twilio ?
3. ✅ URL commence par `https://` ?
4. ✅ Bot démarré sur le port 3000 ?

### Erreurs API ?
```bash
# Re-vérifiez vos clés
npm run verify

# Reconfigurez si nécessaire
npm run setup
```

### Problèmes de base de données ?
```bash
# Vérifiez la connexion PostgreSQL
npm run db:push

# Réinitialisez la base de données
npm run setup-db

# Vérifiez avec l'interface graphique
npm run db:studio
```

## 🚀 Déploiement en production

### Option 1 : Railway (Recommandé)
1. Créez un compte sur [railway.app](https://railway.app)
2. Connectez votre repo GitHub
3. Ajoutez une base PostgreSQL
4. Configurez les variables d'environnement
5. Déployez automatiquement !

### Option 2 : Vercel + Supabase
1. Base de données sur [supabase.com](https://supabase.com)
2. Déploiement sur [vercel.com](https://vercel.com)
3. Configuration des variables d'environnement

### Option 3 : Docker
```bash
# Build et démarrage avec Docker Compose
docker-compose up -d
```

**Guide détaillé :** `docs/DEPLOYMENT.md`

## 📱 Test Rapide

Une fois tout configuré, testez ces messages :

```
Utilisateur: "Bonjour"
Bot: Message d'accueil avec instructions

Utilisateur: "J'ai besoin d'une perceuse à Dakar"
Bot: Réponse contextuelle avec suggestions

Utilisateur: "Qui peut m'aider à déménager ?"
Bot: Proposition de connexion avec la communauté
```

## 🎉 Félicitations !

Votre bot Nowee est maintenant opérationnel ! 

**Prochaines étapes :**
- Invitez des amis à tester
- Consultez les statistiques sur `/stats`
- Explorez le code dans `src/bot/`
- Lisez la documentation complète dans `docs/`

**Besoin d'aide ?**
- 📖 Documentation complète : `README.md`
- 🔧 Guide détaillé API : `docs/SETUP_API_KEYS.md`
- 🏗️ Architecture : `docs/ARCHITECTURE.md`

---

**Nowee** - *L'entraide locale en temps réel* 🌍❤️
