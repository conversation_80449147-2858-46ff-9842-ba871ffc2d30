/**
 * Moteur de prompts avancé pour Nowee
 * Gère la génération contextuelle de réponses intelligentes
 */

import { extractLocationFromMessage } from '../utils/locationUtils.js';

// Types de besoins détectables
export const NEED_TYPES = {
  MATERIAL: 'material',      // Objets physiques
  SERVICE: 'service',        // Services humains
  ADVICE: 'advice',          // Conseils/informations
  EMERGENCY: 'emergency',    // Urgences
  SOCIAL: 'social',          // Connexions sociales
  LEARNING: 'learning',      // Apprentissage/formation
  TRANSPORT: 'transport',    // Transport/déplacement
  FOOD: 'food',             // Nourriture/restauration
  HOUSING: 'housing',       // Logement/hébergement
  HEALTH: 'health'          // Santé/bien-être
};

// Niveaux d'urgence
export const URGENCY_LEVELS = {
  LOW: 1,      // Pas urgent
  MEDIUM: 2,   // Modéré
  HIGH: 3,     // Urgent
  CRITICAL: 4  // Critique/Urgence
};

// Contextes culturels
export const CULTURAL_CONTEXTS = {
  AFRICAN: 'african',
  EUROPEAN: 'european',
  NORTH_AMERICAN: 'north_american',
  UNIVERSAL: 'universal'
};

/**
 * Analyse un message pour extraire le contexte complet
 */
export function analyzeMessage(message, userProfile = {}) {
  const analysis = {
    originalMessage: message,
    needType: detectNeedType(message),
    urgency: detectUrgency(message),
    location: extractLocationFromMessage(message),
    culturalContext: detectCulturalContext(message, userProfile),
    keywords: extractKeywords(message),
    sentiment: analyzeSentiment(message),
    timeContext: detectTimeContext(message)
  };
  
  return analysis;
}

/**
 * Détecte le type de besoin principal
 */
function detectNeedType(message) {
  const lowerMsg = message.toLowerCase();
  
  // Patterns pour chaque type de besoin
  const patterns = {
    [NEED_TYPES.EMERGENCY]: [
      'urgent', 'urgence', 'emergency', 'aide immédiate', 'vite', 'rapidement',
      'problème grave', 'danger', 'accident', 'malade', 'blessé'
    ],
    [NEED_TYPES.MATERIAL]: [
      'perceuse', 'outil', 'objet', 'matériel', 'équipement', 'machine',
      'prêter', 'emprunter', 'louer', 'acheter', 'vendre'
    ],
    [NEED_TYPES.SERVICE]: [
      'plombier', 'électricien', 'mécanicien', 'réparation', 'service',
      'aide pour', 'quelqu\'un pour', 'professionnel', 'expert'
    ],
    [NEED_TYPES.TRANSPORT]: [
      'transport', 'voiture', 'taxi', 'bus', 'déplacer', 'aller à',
      'conduire', 'covoiturage', 'véhicule'
    ],
    [NEED_TYPES.FOOD]: [
      'manger', 'nourriture', 'restaurant', 'cuisine', 'repas',
      'faim', 'plat', 'commander', 'livraison'
    ],
    [NEED_TYPES.HOUSING]: [
      'logement', 'hébergement', 'dormir', 'chambre', 'appartement',
      'maison', 'toit', 'abri'
    ],
    [NEED_TYPES.HEALTH]: [
      'médecin', 'santé', 'hôpital', 'pharmacie', 'médicament',
      'soigner', 'mal', 'douleur', 'consultation'
    ],
    [NEED_TYPES.LEARNING]: [
      'apprendre', 'formation', 'cours', 'enseigner', 'expliquer',
      'comment', 'tutoriel', 'aide aux devoirs'
    ],
    [NEED_TYPES.SOCIAL]: [
      'rencontrer', 'amis', 'sortir', 'événement', 'fête',
      'communauté', 'groupe', 'club'
    ],
    [NEED_TYPES.ADVICE]: [
      'conseil', 'avis', 'recommandation', 'suggestion', 'que faire',
      'comment faire', 'opinion', 'expérience'
    ]
  };
  
  // Compter les matches pour chaque type
  const scores = {};
  for (const [type, keywords] of Object.entries(patterns)) {
    scores[type] = keywords.filter(keyword => lowerMsg.includes(keyword)).length;
  }
  
  // Retourner le type avec le score le plus élevé
  const bestMatch = Object.entries(scores).reduce((a, b) => scores[a[0]] > scores[b[0]] ? a : b);
  return bestMatch[1] > 0 ? bestMatch[0] : NEED_TYPES.ADVICE;
}

/**
 * Détecte le niveau d'urgence
 */
function detectUrgency(message) {
  const lowerMsg = message.toLowerCase();
  
  const urgencyKeywords = {
    [URGENCY_LEVELS.CRITICAL]: ['urgence', 'emergency', 'immédiatement', 'maintenant', 'vite', 'danger'],
    [URGENCY_LEVELS.HIGH]: ['urgent', 'rapidement', 'bientôt', 'aujourd\'hui'],
    [URGENCY_LEVELS.MEDIUM]: ['quand possible', 'cette semaine', 'prochainement'],
    [URGENCY_LEVELS.LOW]: ['pas pressé', 'quand vous voulez', 'un jour']
  };
  
  for (const [level, keywords] of Object.entries(urgencyKeywords)) {
    if (keywords.some(keyword => lowerMsg.includes(keyword))) {
      return parseInt(level);
    }
  }
  
  return URGENCY_LEVELS.MEDIUM; // Par défaut
}

/**
 * Détecte le contexte culturel
 */
function detectCulturalContext(message, userProfile) {
  const location = extractLocationFromMessage(message);
  
  if (location) {
    if (['Sénégal', 'Côte d\'Ivoire', 'Maroc', 'Tunisie', 'Algérie', 'Nigeria', 'Kenya'].includes(location.country)) {
      return CULTURAL_CONTEXTS.AFRICAN;
    } else if (['France', 'Belgique', 'Suisse'].includes(location.country)) {
      return CULTURAL_CONTEXTS.EUROPEAN;
    } else if (['Canada', 'États-Unis'].includes(location.country)) {
      return CULTURAL_CONTEXTS.NORTH_AMERICAN;
    }
  }
  
  return CULTURAL_CONTEXTS.UNIVERSAL;
}

/**
 * Extrait les mots-clés importants
 */
function extractKeywords(message) {
  const words = message.toLowerCase().split(/\s+/);
  const stopWords = ['le', 'la', 'les', 'un', 'une', 'des', 'de', 'du', 'et', 'ou', 'mais', 'donc', 'car', 'ni', 'or'];
  
  return words
    .filter(word => word.length > 2 && !stopWords.includes(word))
    .filter(word => /^[a-zA-ZÀ-ÿ]+$/.test(word))
    .slice(0, 10); // Limiter à 10 mots-clés
}

/**
 * Analyse le sentiment du message
 */
function analyzeSentiment(message) {
  const lowerMsg = message.toLowerCase();
  
  const positiveWords = ['merci', 'super', 'génial', 'parfait', 'excellent', 'content', 'heureux'];
  const negativeWords = ['problème', 'urgent', 'aide', 'difficile', 'impossible', 'triste', 'inquiet'];
  
  const positiveCount = positiveWords.filter(word => lowerMsg.includes(word)).length;
  const negativeCount = negativeWords.filter(word => lowerMsg.includes(word)).length;
  
  if (positiveCount > negativeCount) return 'positive';
  if (negativeCount > positiveCount) return 'negative';
  return 'neutral';
}

/**
 * Détecte le contexte temporel
 */
function detectTimeContext(message) {
  const lowerMsg = message.toLowerCase();
  
  const timePatterns = {
    immediate: ['maintenant', 'immédiatement', 'tout de suite', 'là'],
    today: ['aujourd\'hui', 'ce matin', 'cet après-midi', 'ce soir'],
    tomorrow: ['demain', 'demain matin', 'demain soir'],
    week: ['cette semaine', 'la semaine prochaine', 'dans la semaine'],
    flexible: ['quand possible', 'pas pressé', 'un jour']
  };
  
  for (const [context, patterns] of Object.entries(timePatterns)) {
    if (patterns.some(pattern => lowerMsg.includes(pattern))) {
      return context;
    }
  }
  
  return 'unspecified';
}

/**
 * Génère un prompt contextualisé pour OpenAI
 */
export function generateContextualPrompt(analysis, userSession = {}) {
  const { needType, urgency, location, culturalContext, sentiment, timeContext } = analysis;
  
  // Prompt de base selon le contexte culturel
  const culturalPrompts = {
    [CULTURAL_CONTEXTS.AFRICAN]: `Tu es Nowee, l'assistant d'entraide locale qui comprend parfaitement les réalités africaines. Tu connais l'importance de la solidarité communautaire, du respect des aînés, et des solutions créatives avec peu de moyens.`,
    
    [CULTURAL_CONTEXTS.EUROPEAN]: `Tu es Nowee, l'assistant d'entraide locale adapté au contexte européen. Tu comprends les systèmes sociaux, les réglementations, et l'importance de l'efficacité organisée.`,
    
    [CULTURAL_CONTEXTS.UNIVERSAL]: `Tu es Nowee, l'assistant d'entraide locale qui s'adapte à tous les contextes culturels avec respect et bienveillance.`
  };
  
  // Instructions spécifiques selon le type de besoin
  const needInstructions = {
    [NEED_TYPES.EMERGENCY]: `🚨 URGENCE DÉTECTÉE. Priorité absolue à la sécurité et aux solutions immédiates. Propose des actions concrètes et des contacts d'urgence si nécessaire.`,
    
    [NEED_TYPES.MATERIAL]: `🔧 Besoin matériel identifié. Concentre-toi sur la mise en relation avec des personnes qui possèdent l'objet, les alternatives de location/achat local, et les solutions de partage communautaire.`,
    
    [NEED_TYPES.SERVICE]: `👷 Service professionnel demandé. Propose des artisans/professionnels locaux, vérifie la réputation, et suggère des questions importantes à poser.`,
    
    [NEED_TYPES.TRANSPORT]: `🚗 Besoin de transport. Explore le covoiturage, les transports en commun, les solutions alternatives, et la sécurité du trajet.`,
    
    [NEED_TYPES.ADVICE]: `💡 Demande de conseil. Partage ton expertise, propose de connecter avec des personnes expérimentées, et encourage la réflexion collaborative.`
  };
  
  // Adaptation selon l'urgence
  const urgencyInstructions = {
    [URGENCY_LEVELS.CRITICAL]: `RÉPONSE IMMÉDIATE REQUISE. Sois direct, concret, et actionnable.`,
    [URGENCY_LEVELS.HIGH]: `Situation urgente. Propose des solutions rapides et efficaces.`,
    [URGENCY_LEVELS.MEDIUM]: `Prends le temps d'explorer plusieurs options et de bien conseiller.`,
    [URGENCY_LEVELS.LOW]: `Situation non urgente. Tu peux être plus détaillé et pédagogique.`
  };
  
  // Construction du prompt final
  const prompt = `${culturalPrompts[culturalContext] || culturalPrompts[CULTURAL_CONTEXTS.UNIVERSAL]}

CONTEXTE DE LA DEMANDE:
- Type de besoin: ${needType}
- Niveau d'urgence: ${urgency}/4
- Localisation: ${location ? `${location.city}, ${location.country}` : 'Non spécifiée'}
- Sentiment: ${sentiment}
- Contexte temporel: ${timeContext}
- Historique utilisateur: ${userSession.messageCount || 0} messages précédents

INSTRUCTIONS SPÉCIFIQUES:
${needInstructions[needType] || needInstructions[NEED_TYPES.ADVICE]}

${urgencyInstructions[urgency] || urgencyInstructions[URGENCY_LEVELS.MEDIUM]}

CONTRAINTES:
- Maximum 3 phrases courtes et percutantes
- Utilise des emojis appropriés au contexte
- Reste bienveillant et optimiste
- Propose des actions concrètes
- Adapte ton langage au contexte culturel

MESSAGE UTILISATEUR: "${analysis.originalMessage}"

RÉPONSE NOWEE:`;

  return prompt;
}

export default {
  analyzeMessage,
  generateContextualPrompt,
  NEED_TYPES,
  URGENCY_LEVELS,
  CULTURAL_CONTEXTS
};
