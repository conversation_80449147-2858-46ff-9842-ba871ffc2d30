#!/usr/bin/env node

/**
 * Script de build pour l'application mobile Nowee
 * Automatise la compilation Android et iOS
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.bold}${colors.blue}\n📱 ${msg}${colors.reset}`)
};

function checkPrerequisites() {
  log.title('Vérification des prérequis');
  
  // Vérifier Node.js
  try {
    const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
    log.success(`Node.js: ${nodeVersion}`);
  } catch (error) {
    log.error('Node.js non installé');
    return false;
  }
  
  // Vérifier React Native CLI
  try {
    execSync('npx react-native --version', { encoding: 'utf8' });
    log.success('React Native CLI disponible');
  } catch (error) {
    log.warning('React Native CLI non trouvé, installation...');
    try {
      execSync('npm install -g @react-native-community/cli', { stdio: 'inherit' });
      log.success('React Native CLI installé');
    } catch (installError) {
      log.error('Impossible d\'installer React Native CLI');
      return false;
    }
  }
  
  // Vérifier les dépendances
  if (!fs.existsSync('node_modules')) {
    log.warning('Dépendances manquantes, installation...');
    try {
      execSync('npm install', { stdio: 'inherit' });
      log.success('Dépendances installées');
    } catch (error) {
      log.error('Erreur installation dépendances');
      return false;
    }
  } else {
    log.success('Dépendances présentes');
  }
  
  return true;
}

function checkAndroidEnvironment() {
  log.title('Vérification environnement Android');
  
  // Vérifier Android SDK
  const androidHome = process.env.ANDROID_HOME || process.env.ANDROID_SDK_ROOT;
  if (!androidHome) {
    log.error('ANDROID_HOME non défini');
    log.info('Définissez ANDROID_HOME vers votre SDK Android');
    return false;
  }
  
  log.success(`Android SDK: ${androidHome}`);
  
  // Vérifier Java
  try {
    const javaVersion = execSync('java -version 2>&1 | head -n 1', { encoding: 'utf8' });
    log.success(`Java: ${javaVersion.trim()}`);
  } catch (error) {
    log.error('Java non installé ou non configuré');
    return false;
  }
  
  return true;
}

function buildAndroid(buildType = 'debug') {
  log.title(`Build Android (${buildType})`);
  
  try {
    // Nettoyer le projet
    log.info('Nettoyage du projet Android...');
    execSync('cd android && ./gradlew clean', { stdio: 'inherit' });
    
    // Build
    const buildCommand = buildType === 'release' 
      ? './gradlew assembleRelease' 
      : './gradlew assembleDebug';
    
    log.info(`Compilation Android ${buildType}...`);
    execSync(`cd android && ${buildCommand}`, { stdio: 'inherit' });
    
    // Localiser l'APK
    const apkPath = buildType === 'release'
      ? 'android/app/build/outputs/apk/release/app-release.apk'
      : 'android/app/build/outputs/apk/debug/app-debug.apk';
    
    if (fs.existsSync(apkPath)) {
      const stats = fs.statSync(apkPath);
      const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
      
      log.success(`APK généré: ${apkPath}`);
      log.info(`Taille: ${fileSizeMB} MB`);
      
      // Copier vers le dossier de sortie
      const outputDir = 'build-output';
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir);
      }
      
      const outputPath = `${outputDir}/nowee-${buildType}-${new Date().toISOString().split('T')[0]}.apk`;
      fs.copyFileSync(apkPath, outputPath);
      log.success(`APK copié vers: ${outputPath}`);
      
      return outputPath;
    } else {
      log.error('APK non trouvé après compilation');
      return null;
    }
    
  } catch (error) {
    log.error(`Erreur build Android: ${error.message}`);
    return null;
  }
}

function generateBuildInfo() {
  log.title('Génération des informations de build');
  
  const buildInfo = {
    timestamp: new Date().toISOString(),
    version: require('./package.json').version,
    platform: process.platform,
    node_version: process.version,
    features: {
      wallet: true,
      barter: true,
      maps: true,
      voice: true,
      push_notifications: true,
      offline_mode: true
    },
    build_config: {
      minSdkVersion: 21,
      targetSdkVersion: 34,
      compileSdkVersion: 34
    }
  };
  
  const outputDir = 'build-output';
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir);
  }
  
  fs.writeFileSync(
    `${outputDir}/build-info.json`, 
    JSON.stringify(buildInfo, null, 2)
  );
  
  log.success('Informations de build générées');
  return buildInfo;
}

function showBuildInstructions() {
  log.title('Instructions de déploiement');
  
  console.log(`
📋 ${colors.bold}INSTRUCTIONS DE DÉPLOIEMENT${colors.reset}

${colors.blue}🤖 Android:${colors.reset}
1. APK généré dans build-output/
2. Pour installer: adb install app.apk
3. Pour publier: Uploadez sur Google Play Console

${colors.blue}📱 Test sur appareil:${colors.reset}
1. Activez le mode développeur
2. Activez le débogage USB
3. Connectez votre appareil
4. Lancez: npm run android

${colors.blue}🚀 Publication:${colors.reset}
1. Générez une clé de signature
2. Configurez android/app/build.gradle
3. Build release: npm run build:android:release
4. Uploadez sur les stores

${colors.green}✅ L'application mobile Nowee est prête !${colors.reset}
`);
}

async function main() {
  const args = process.argv.slice(2);
  const buildType = args.includes('--release') ? 'release' : 'debug';
  const platform = args.includes('--ios') ? 'ios' : 'android';
  
  try {
    log.title('Build Application Mobile Nowee');
    
    // Vérifications
    if (!checkPrerequisites()) {
      process.exit(1);
    }
    
    if (platform === 'android') {
      if (!checkAndroidEnvironment()) {
        log.warning('Environnement Android non configuré, build ignoré');
      } else {
        const apkPath = buildAndroid(buildType);
        if (!apkPath) {
          process.exit(1);
        }
      }
    }
    
    // Générer les infos de build
    const buildInfo = generateBuildInfo();
    
    // Instructions
    showBuildInstructions();
    
    log.success('Build terminé avec succès !');
    log.info(`Version: ${buildInfo.version}`);
    log.info(`Plateforme: ${platform}`);
    log.info(`Type: ${buildType}`);
    
  } catch (error) {
    log.error(`Erreur: ${error.message}`);
    process.exit(1);
  }
}

main();
