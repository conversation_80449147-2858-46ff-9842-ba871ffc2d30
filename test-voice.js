#!/usr/bin/env node

/**
 * Script de test du système vocal Nowee
 * Teste la reconnaissance vocale et la synthèse vocale
 */

import 'dotenv/config';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { VoiceService } from './src/services/voiceService.js';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.bold}${colors.blue}\n🎤 ${msg}${colors.reset}`)
};

async function testTextToSpeech() {
  log.title('Test de Synthèse Vocale');

  try {
    // Textes de test
    const testTexts = [
      "Bonjour ! Je suis Nowee, votre assistant d'entraide locale.",
      "J'ai bien reçu votre demande d'aide. Je cherche quelqu'un près de chez vous.",
      "Parfait ! Je vous mets en contact avec Marie qui peut vous aider.",
      "🎯 Aide trouvée ! Quelqu'un peut vous aider avec votre demande."
    ];

    for (let i = 0; i < testTexts.length; i++) {
      const text = testTexts[i];
      log.info(`Test ${i + 1}: "${text}"`);

      const result = await VoiceService.textToSpeech(text, 'alloy', 'fr');

      if (result.success) {
        // Sauvegarder le fichier audio
        const filename = `test_tts_${i + 1}.mp3`;
        const filepath = path.join(__dirname, 'temp', filename);
        
        await VoiceService.ensureTempDir();
        await fs.writeFile(filepath, result.audioBuffer);
        
        log.success(`Audio généré: ${filename} (${result.audioBuffer.length} bytes)`);
      } else {
        log.error(`Erreur: ${result.error}`);
      }
    }

  } catch (error) {
    log.error(`Erreur test TTS: ${error.message}`);
  }
}

async function testLanguageDetection() {
  log.title('Test de Détection de Langue');

  try {
    const testTexts = [
      { text: "Bonjour, j'ai besoin d'aide", expected: 'fr' },
      { text: "Hello, I need help", expected: 'en' },
      { text: "Man dëgg Nowee", expected: 'wo' },
      { text: "Hola, necesito ayuda", expected: 'es' },
      { text: "مرحبا، أحتاج مساعدة", expected: 'ar' }
    ];

    for (const test of testTexts) {
      const detected = await VoiceService.detectTextLanguage(test.text);
      
      if (detected === test.expected) {
        log.success(`"${test.text}" → ${detected} ✓`);
      } else {
        log.warning(`"${test.text}" → ${detected} (attendu: ${test.expected})`);
      }
    }

  } catch (error) {
    log.error(`Erreur test détection langue: ${error.message}`);
  }
}

async function testVoiceOnboarding() {
  log.title('Test de l\'Onboarding Vocal');

  try {
    const languages = ['fr', 'en', 'wo'];

    for (const lang of languages) {
      log.info(`Génération onboarding en ${lang}...`);
      
      const result = await VoiceService.generateVoiceOnboarding(lang);
      
      if (result.success) {
        const filename = `onboarding_${lang}.mp3`;
        const filepath = path.join(__dirname, 'temp', filename);
        
        await VoiceService.ensureTempDir();
        await fs.writeFile(filepath, result.audioBuffer);
        
        log.success(`Onboarding ${lang}: ${filename}`);
      } else {
        log.error(`Erreur onboarding ${lang}: ${result.error}`);
      }
    }

  } catch (error) {
    log.error(`Erreur test onboarding: ${error.message}`);
  }
}

async function testTextAdaptation() {
  log.title('Test d\'Adaptation de Texte');

  try {
    const testTexts = [
      "🎯 Aide trouvée ! ✅ Quelqu'un peut vous aider.",
      "**Urgent** : J'ai besoin d'aide `immédiatement`.",
      "Voici votre réponse :\n\n• Point 1\n• Point 2\n\nMerci !",
      "👋 Salut ! 🤝 Je peux t'aider avec ça. 😊"
    ];

    for (const text of testTexts) {
      log.info(`Original: "${text}"`);
      
      const adaptedFr = VoiceService.adaptTextForLanguage(text, 'fr');
      const adaptedWo = VoiceService.adaptTextForLanguage(text, 'wo');
      
      console.log(`  FR: "${adaptedFr}"`);
      console.log(`  WO: "${adaptedWo}"`);
      console.log('');
    }

  } catch (error) {
    log.error(`Erreur test adaptation: ${error.message}`);
  }
}

async function testVoiceMessageProcessing() {
  log.title('Test de Traitement de Message Vocal');

  try {
    // Simuler un fichier audio (en réalité, on utiliserait un vrai fichier OGG)
    log.warning('Note: Ce test nécessite un vrai fichier audio pour fonctionner complètement');
    
    const testPhone = '+221771234567';
    const testMessages = [
      "J'ai besoin d'une perceuse à Dakar",
      "Je peux aider pour un déménagement",
      "Qui peut me prêter une voiture ?"
    ];

    for (const message of testMessages) {
      log.info(`Simulation traitement: "${message}"`);
      
      // Simuler le traitement (sans audio réel)
      const mockAudioBuffer = Buffer.from('mock audio data');
      
      // Test de l'adaptation du prompt pour le vocal
      const adaptedPrompt = VoiceService.adaptPromptForVoice(
        `Répondre à: "${message}"`,
        'fr'
      );
      
      log.success(`Prompt adapté généré (${adaptedPrompt.length} caractères)`);
    }

  } catch (error) {
    log.error(`Erreur test traitement vocal: ${error.message}`);
  }
}

async function testCleanup() {
  log.title('Test de Nettoyage');

  try {
    // Créer des fichiers temporaires anciens
    const tempDir = path.join(__dirname, 'temp');
    await VoiceService.ensureTempDir();
    
    const oldFile = path.join(tempDir, 'old_file.mp3');
    await fs.writeFile(oldFile, 'test data');
    
    // Modifier la date de modification pour simuler un fichier ancien
    const oldDate = new Date(Date.now() - 2 * 60 * 60 * 1000); // 2 heures
    await fs.utimes(oldFile, oldDate, oldDate);
    
    log.info('Fichier temporaire ancien créé');
    
    // Tester le nettoyage
    await VoiceService.cleanupTempFiles(1); // 1 heure
    
    // Vérifier que le fichier a été supprimé
    try {
      await fs.access(oldFile);
      log.warning('Le fichier ancien n\'a pas été supprimé');
    } catch {
      log.success('Fichier ancien supprimé avec succès');
    }

  } catch (error) {
    log.error(`Erreur test nettoyage: ${error.message}`);
  }
}

async function runAllTests() {
  log.title('Tests du Système Vocal Nowee');

  console.log('🎤 Tests des fonctionnalités vocales de Nowee\n');

  // Vérifier les prérequis
  if (!process.env.OPENAI_API_KEY) {
    log.error('OPENAI_API_KEY manquante dans .env');
    return;
  }

  try {
    await testTextToSpeech();
    await testLanguageDetection();
    await testVoiceOnboarding();
    await testTextAdaptation();
    await testVoiceMessageProcessing();
    await testCleanup();

    log.success('\n🎉 Tous les tests vocaux terminés !');
    
    console.log('\n📁 Fichiers générés dans le dossier temp/:');
    try {
      const tempDir = path.join(__dirname, 'temp');
      const files = await fs.readdir(tempDir);
      files.forEach(file => {
        if (file.endsWith('.mp3')) {
          console.log(`  🔊 ${file}`);
        }
      });
    } catch {
      console.log('  (Aucun fichier généré)');
    }

  } catch (error) {
    log.error(`Erreur générale: ${error.message}`);
  }
}

async function cleanupTestFiles() {
  log.info('Nettoyage des fichiers de test...');
  
  try {
    const tempDir = path.join(__dirname, 'temp');
    const files = await fs.readdir(tempDir);
    
    for (const file of files) {
      if (file.startsWith('test_') || file.startsWith('onboarding_')) {
        await fs.unlink(path.join(tempDir, file));
      }
    }
    
    log.success('Fichiers de test nettoyés');
  } catch (error) {
    log.error(`Erreur nettoyage: ${error.message}`);
  }
}

// Fonction principale
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--clean')) {
    await cleanupTestFiles();
  } else {
    await runAllTests();
    
    if (args.includes('--cleanup')) {
      await cleanupTestFiles();
    }
  }
  
  process.exit(0);
}

// Gestion des erreurs
process.on('unhandledRejection', (error) => {
  log.error(`Erreur non gérée: ${error.message}`);
  process.exit(1);
});

// Exécuter les tests
main().catch(error => {
  log.error(`Erreur: ${error.message}`);
  process.exit(1);
});
