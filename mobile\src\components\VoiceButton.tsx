/**
 * Composant bouton vocal pour Nowee
 * Permet l'enregistrement et l'envoi de messages vocaux
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Text,
  Alert,
  Vibration,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import * as Animatable from 'react-native-animatable';
import { VoiceService } from '../services/VoiceService';
import { useTheme } from '../services/ThemeService';

interface VoiceButtonProps {
  onVoiceMessage?: (text: string, response: string) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  style?: any;
}

const VoiceButton: React.FC<VoiceButtonProps> = ({
  onVoiceMessage,
  onError,
  disabled = false,
  size = 'medium',
  style
}) => {
  const { colors } = useTheme();
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAvailable, setIsAvailable] = useState(false);

  useEffect(() => {
    initializeVoice();
    return () => {
      VoiceService.cleanup();
    };
  }, []);

  const initializeVoice = async () => {
    try {
      const available = await VoiceService.isRecognitionAvailable();
      setIsAvailable(available);
      
      if (available) {
        await VoiceService.initialize();
      }
    } catch (error) {
      console.error('Erreur initialisation vocal:', error);
      setIsAvailable(false);
    }
  };

  const handlePress = async () => {
    if (disabled || !isAvailable) return;

    if (isListening) {
      await stopListening();
    } else {
      await startListening();
    }
  };

  const startListening = async () => {
    try {
      setIsListening(true);
      Vibration.vibrate(50); // Feedback haptique

      await VoiceService.startListening('fr-FR');
      
      // Arrêt automatique après 30 secondes
      setTimeout(() => {
        if (isListening) {
          stopListening();
        }
      }, 30000);

    } catch (error) {
      console.error('Erreur démarrage écoute:', error);
      setIsListening(false);
      
      Alert.alert(
        'Erreur Vocal',
        'Impossible de démarrer la reconnaissance vocale. Vérifiez vos permissions.',
        [{ text: 'OK' }]
      );
      
      onError?.('Erreur de reconnaissance vocale');
    }
  };

  const stopListening = async () => {
    try {
      setIsListening(false);
      setIsProcessing(true);
      
      await VoiceService.stopListening();
      Vibration.vibrate(100); // Feedback haptique

      // Le traitement se fait automatiquement dans VoiceService.onSpeechResults
      
    } catch (error) {
      console.error('Erreur arrêt écoute:', error);
      setIsProcessing(false);
      onError?.('Erreur lors de l\'arrêt de l\'écoute');
    }
  };

  const handleVoiceResult = (userMessage: string, botResponse: string) => {
    setIsProcessing(false);
    onVoiceMessage?.(userMessage, botResponse);
  };

  const getButtonSize = () => {
    switch (size) {
      case 'small': return 40;
      case 'large': return 80;
      default: return 60;
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'small': return 20;
      case 'large': return 40;
      default: return 30;
    }
  };

  const getButtonStyle = () => {
    const buttonSize = getButtonSize();
    const baseStyle = {
      width: buttonSize,
      height: buttonSize,
      borderRadius: buttonSize / 2,
      backgroundColor: isListening ? colors.error : colors.primary,
      opacity: disabled || !isAvailable ? 0.5 : 1,
    };

    return [styles.button, baseStyle, style];
  };

  const renderButton = () => {
    let iconName = 'mic';
    let animationType = undefined;

    if (isProcessing) {
      iconName = 'hourglass-empty';
      animationType = 'rotate';
    } else if (isListening) {
      iconName = 'mic';
      animationType = 'pulse';
    }

    return (
      <TouchableOpacity
        style={getButtonStyle()}
        onPress={handlePress}
        disabled={disabled || !isAvailable || isProcessing}
        activeOpacity={0.8}
      >
        <Animatable.View
          animation={animationType}
          iterationCount={isListening ? 'infinite' : 1}
          duration={1000}
          style={styles.iconContainer}
        >
          <Icon
            name={iconName}
            size={getIconSize()}
            color="#FFFFFF"
          />
        </Animatable.View>
      </TouchableOpacity>
    );
  };

  const renderStatus = () => {
    if (!isAvailable) {
      return (
        <Text style={[styles.statusText, { color: colors.error }]}>
          Vocal non disponible
        </Text>
      );
    }

    if (isProcessing) {
      return (
        <Text style={[styles.statusText, { color: colors.primary }]}>
          Traitement en cours...
        </Text>
      );
    }

    if (isListening) {
      return (
        <Text style={[styles.statusText, { color: colors.error }]}>
          🎤 Écoute en cours...
        </Text>
      );
    }

    return null;
  };

  return (
    <View style={styles.container}>
      {renderButton()}
      {renderStatus()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default VoiceButton;
