# Script de configuration automatique pour Nowee Mobile sur Windows
# Installe et configure l'environnement React Native

Write-Host "🚀 Configuration automatique de l'environnement Nowee Mobile" -ForegroundColor Blue

# Fonction pour vérifier si un programme est installé
function Test-ProgramInstalled($programName) {
    $installed = Get-Command $programName -ErrorAction SilentlyContinue
    return $installed -ne $null
}

# Fonction pour télécharger un fichier
function Download-File($url, $output) {
    Write-Host "📥 Téléchargement de $output..." -ForegroundColor Yellow
    try {
        Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing
        Write-Host "✅ Téléchargement terminé" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ Erreur de téléchargement: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 1. Vérifier Node.js
Write-Host "`n🔍 Vérification de Node.js..." -ForegroundColor Cyan
if (Test-ProgramInstalled "node") {
    $nodeVersion = node --version
    Write-Host "✅ Node.js installé: $nodeVersion" -ForegroundColor Green
} else {
    Write-Host "❌ Node.js non installé" -ForegroundColor Red
    Write-Host "💡 Veuillez installer Node.js depuis https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# 2. Vérifier/Installer Chocolatey
Write-Host "`n🔍 Vérification de Chocolatey..." -ForegroundColor Cyan
if (Test-ProgramInstalled "choco") {
    Write-Host "✅ Chocolatey installé" -ForegroundColor Green
} else {
    Write-Host "📦 Installation de Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    Write-Host "✅ Chocolatey installé" -ForegroundColor Green
}

# 3. Installer Java JDK
Write-Host "`n🔍 Vérification de Java JDK..." -ForegroundColor Cyan
if (Test-ProgramInstalled "java") {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "✅ Java installé: $javaVersion" -ForegroundColor Green
} else {
    Write-Host "📦 Installation de Java JDK 11..." -ForegroundColor Yellow
    choco install openjdk11 -y
    Write-Host "✅ Java JDK installé" -ForegroundColor Green
}

# 4. Créer le dossier Android SDK
$androidHome = "$env:LOCALAPPDATA\Android\Sdk"
Write-Host "`n🔍 Configuration Android SDK..." -ForegroundColor Cyan
if (!(Test-Path $androidHome)) {
    Write-Host "📁 Création du dossier Android SDK..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $androidHome -Force | Out-Null
}

# 5. Télécharger Android Command Line Tools
$cmdlineToolsUrl = "https://dl.google.com/android/repository/commandlinetools-win-9477386_latest.zip"
$cmdlineToolsZip = "$env:TEMP\commandlinetools.zip"
$cmdlineToolsDir = "$androidHome\cmdline-tools"

Write-Host "`n📥 Téléchargement des Android Command Line Tools..." -ForegroundColor Cyan
if (!(Test-Path "$cmdlineToolsDir\latest")) {
    if (Download-File $cmdlineToolsUrl $cmdlineToolsZip) {
        Write-Host "📦 Extraction des outils..." -ForegroundColor Yellow
        Expand-Archive -Path $cmdlineToolsZip -DestinationPath $cmdlineToolsDir -Force
        
        # Renommer le dossier cmdline-tools en latest
        if (Test-Path "$cmdlineToolsDir\cmdline-tools") {
            Move-Item "$cmdlineToolsDir\cmdline-tools" "$cmdlineToolsDir\latest"
        }
        
        Remove-Item $cmdlineToolsZip -Force
        Write-Host "✅ Android Command Line Tools installés" -ForegroundColor Green
    }
} else {
    Write-Host "✅ Android Command Line Tools déjà installés" -ForegroundColor Green
}

# 6. Configurer les variables d'environnement
Write-Host "`n🔧 Configuration des variables d'environnement..." -ForegroundColor Cyan

# ANDROID_HOME
[Environment]::SetEnvironmentVariable("ANDROID_HOME", $androidHome, "User")
$env:ANDROID_HOME = $androidHome

# Ajouter au PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
$androidPaths = @(
    "$androidHome\platform-tools",
    "$androidHome\cmdline-tools\latest\bin",
    "$androidHome\emulator"
)

foreach ($path in $androidPaths) {
    if ($currentPath -notlike "*$path*") {
        $currentPath += ";$path"
    }
}

[Environment]::SetEnvironmentVariable("PATH", $currentPath, "User")
$env:PATH = $currentPath

Write-Host "✅ Variables d'environnement configurées" -ForegroundColor Green
Write-Host "   ANDROID_HOME = $androidHome" -ForegroundColor Gray

# 7. Installer les packages Android SDK
Write-Host "`n📦 Installation des packages Android SDK..." -ForegroundColor Cyan
$sdkmanager = "$cmdlineToolsDir\latest\bin\sdkmanager.bat"

if (Test-Path $sdkmanager) {
    $packages = @(
        "platform-tools",
        "platforms;android-33",
        "platforms;android-34",
        "build-tools;33.0.0",
        "build-tools;34.0.0",
        "system-images;android-33;google_apis;x86_64",
        "emulator"
    )
    
    foreach ($package in $packages) {
        Write-Host "📦 Installation de $package..." -ForegroundColor Yellow
        & $sdkmanager $package --sdk_root=$androidHome
    }
    
    Write-Host "✅ Packages Android SDK installés" -ForegroundColor Green
} else {
    Write-Host "❌ SDK Manager non trouvé" -ForegroundColor Red
}

# 8. Créer un AVD (Android Virtual Device)
Write-Host "`n📱 Création d'un émulateur Android..." -ForegroundColor Cyan
$avdmanager = "$cmdlineToolsDir\latest\bin\avdmanager.bat"

if (Test-Path $avdmanager) {
    $avdName = "Nowee_Pixel_API_33"
    
    # Vérifier si l'AVD existe déjà
    $existingAvds = & $avdmanager list avd | Select-String "Name:"
    if ($existingAvds -notmatch $avdName) {
        Write-Host "📱 Création de l'émulateur $avdName..." -ForegroundColor Yellow
        & $avdmanager create avd --name $avdName --package "system-images;android-33;google_apis;x86_64" --device "pixel_4"
        Write-Host "✅ Émulateur créé" -ForegroundColor Green
    } else {
        Write-Host "✅ Émulateur déjà existant" -ForegroundColor Green
    }
} else {
    Write-Host "❌ AVD Manager non trouvé" -ForegroundColor Red
}

# 9. Installer React Native CLI
Write-Host "`n🔧 Installation de React Native CLI..." -ForegroundColor Cyan
if (Test-ProgramInstalled "react-native") {
    Write-Host "✅ React Native CLI déjà installé" -ForegroundColor Green
} else {
    npm install -g @react-native-community/cli
    Write-Host "✅ React Native CLI installé" -ForegroundColor Green
}

# 10. Résumé et instructions
Write-Host "`n🎉 Configuration terminée !" -ForegroundColor Green
Write-Host "`n📋 Résumé de l'installation:" -ForegroundColor Cyan
Write-Host "✅ Node.js: Installé" -ForegroundColor Green
Write-Host "✅ Java JDK: Installé" -ForegroundColor Green
Write-Host "✅ Android SDK: $androidHome" -ForegroundColor Green
Write-Host "✅ Variables d'environnement: Configurées" -ForegroundColor Green
Write-Host "✅ Émulateur Android: Créé" -ForegroundColor Green
Write-Host "✅ React Native CLI: Installé" -ForegroundColor Green

Write-Host "`n🚀 Prochaines étapes:" -ForegroundColor Blue
Write-Host "1. Redémarrez PowerShell pour charger les nouvelles variables" -ForegroundColor Yellow
Write-Host "2. cd mobile" -ForegroundColor Yellow
Write-Host "3. npm install --legacy-peer-deps" -ForegroundColor Yellow
Write-Host "4. npm run android" -ForegroundColor Yellow

Write-Host "`n💡 Pour démarrer l'émulateur manuellement:" -ForegroundColor Blue
Write-Host "emulator -avd Nowee_Pixel_API_33" -ForegroundColor Yellow

Write-Host "`n🎯 L'environnement Nowee Mobile est prêt !" -ForegroundColor Green
