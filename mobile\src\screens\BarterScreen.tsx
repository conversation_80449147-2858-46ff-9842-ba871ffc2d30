/**
 * <PERSON><PERSON><PERSON> de Troc Nowee
 * Interface visuelle pour créer et gérer les échanges
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import LinearGradient from 'react-native-linear-gradient';
import { useTheme } from '../services/ThemeService';
import { BarterService } from '../services/BarterService';
import { useAuth } from '../services/AuthService';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface BarterProposal {
  id: string;
  exchange_type: string;
  status: string;
  offered_coins: number;
  offered_time_hours: number;
  requested_coins: number;
  requested_time_hours: number;
  proposal_message: string;
  created_at: string;
  is_proposer: boolean;
}

interface Resource {
  id: string;
  title: string;
  description: string;
  category: string;
  type: 'NEED' | 'OFFER';
}

const BarterScreen: React.FC = () => {
  const { colors } = useTheme();
  const { user } = useAuth();
  const [proposals, setProposals] = useState<BarterProposal[]>([]);
  const [userResources, setUserResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'active' | 'create' | 'history'>('active');
  
  // État pour la création de troc
  const [selectedOffer, setSelectedOffer] = useState<Resource | null>(null);
  const [selectedNeed, setSelectedNeed] = useState<Resource | null>(null);
  const [offeredCoins, setOfferedCoins] = useState('0');
  const [requestedCoins, setRequestedCoins] = useState('0');
  const [offeredTime, setOfferedTime] = useState('0');
  const [requestedTime, setRequestedTime] = useState('0');
  const [proposalMessage, setProposalMessage] = useState('');

  // Animations
  const dragAnim = new Animated.ValueXY();
  const scaleAnim = new Animated.Value(1);

  useEffect(() => {
    loadBarterData();
  }, []);

  const loadBarterData = async () => {
    try {
      setLoading(true);
      
      if (!user?.phone) {
        throw new Error('Utilisateur non connecté');
      }

      // Charger les propositions de troc
      const proposalData = await BarterService.getUserProposals(user.phone);
      setProposals(proposalData);

      // Charger les ressources de l'utilisateur
      const resourceData = await BarterService.getUserResources(user.phone);
      setUserResources(resourceData);

    } catch (error) {
      console.error('Erreur chargement troc:', error);
      Alert.alert('Erreur', 'Impossible de charger les données de troc');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProposal = async () => {
    try {
      if (!selectedOffer || !selectedNeed) {
        Alert.alert('Erreur', 'Veuillez sélectionner une offre et un besoin');
        return;
      }

      const proposalData = {
        offeredResourceId: selectedOffer.id,
        requestedResourceId: selectedNeed.id,
        exchangeType: 'MIXED_EXCHANGE',
        offeredCoins: parseFloat(offeredCoins),
        offeredTimeHours: parseFloat(offeredTime),
        requestedCoins: parseFloat(requestedCoins),
        requestedTimeHours: parseFloat(requestedTime),
        proposalMessage,
      };

      await BarterService.createProposal(user!.phone, proposalData);
      
      Alert.alert('Succès', 'Proposition de troc créée avec succès !');
      setShowCreateModal(false);
      resetCreateForm();
      loadBarterData();

    } catch (error) {
      console.error('Erreur création proposition:', error);
      Alert.alert('Erreur', 'Impossible de créer la proposition de troc');
    }
  };

  const resetCreateForm = () => {
    setSelectedOffer(null);
    setSelectedNeed(null);
    setOfferedCoins('0');
    setRequestedCoins('0');
    setOfferedTime('0');
    setRequestedTime('0');
    setProposalMessage('');
  };

  const handleAcceptProposal = async (proposalId: string) => {
    try {
      await BarterService.acceptProposal(proposalId, user!.phone);
      Alert.alert('Succès', 'Proposition acceptée !');
      loadBarterData();
    } catch (error) {
      console.error('Erreur acceptation:', error);
      Alert.alert('Erreur', 'Impossible d\'accepter la proposition');
    }
  };

  const handleRejectProposal = async (proposalId: string) => {
    try {
      await BarterService.rejectProposal(proposalId, user!.phone);
      Alert.alert('Succès', 'Proposition rejetée');
      loadBarterData();
    } catch (error) {
      console.error('Erreur rejet:', error);
      Alert.alert('Erreur', 'Impossible de rejeter la proposition');
    }
  };

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: dragAnim.x, translationY: dragAnim.y } }],
    { useNativeDriver: false }
  );

  const onHandlerStateChange = (event: any) => {
    if (event.nativeEvent.oldState === State.ACTIVE) {
      // Animation de retour
      Animated.parallel([
        Animated.spring(dragAnim, {
          toValue: { x: 0, y: 0 },
          useNativeDriver: false,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: false,
        }),
      ]).start();
    }
  };

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {[
        { key: 'active', label: 'Actifs', icon: 'swap-horiz' },
        { key: 'create', label: 'Créer', icon: 'add-circle' },
        { key: 'history', label: 'Historique', icon: 'history' },
      ].map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tabItem,
            selectedTab === tab.key && styles.tabItemActive
          ]}
          onPress={() => setSelectedTab(tab.key as any)}
        >
          <Icon 
            name={tab.icon} 
            size={20} 
            color={selectedTab === tab.key ? colors.primary : colors.textSecondary} 
          />
          <Text 
            style={[
              styles.tabLabel,
              { color: selectedTab === tab.key ? colors.primary : colors.textSecondary }
            ]}
          >
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderProposalCard = (proposal: BarterProposal) => {
    const statusColors = {
      PENDING: colors.warning,
      ACCEPTED: colors.success,
      REJECTED: colors.error,
      COMPLETED: colors.primary,
    };

    const statusIcons = {
      PENDING: 'hourglass-empty',
      ACCEPTED: 'check-circle',
      REJECTED: 'cancel',
      COMPLETED: 'done-all',
    };

    return (
      <View key={proposal.id} style={[styles.proposalCard, { backgroundColor: colors.surface }]}>
        <View style={styles.proposalHeader}>
          <View style={[styles.statusBadge, { backgroundColor: statusColors[proposal.status as keyof typeof statusColors] }]}>
            <Icon 
              name={statusIcons[proposal.status as keyof typeof statusIcons]} 
              size={16} 
              color="#FFFFFF" 
            />
            <Text style={styles.statusText}>{proposal.status}</Text>
          </View>
          
          <Text style={[styles.proposalDate, { color: colors.textSecondary }]}>
            {new Date(proposal.created_at).toLocaleDateString('fr-FR')}
          </Text>
        </View>

        <Text style={[styles.proposalType, { color: colors.text }]}>
          {proposal.exchange_type.replace('_', ' ')}
        </Text>

        <View style={styles.exchangeDetails}>
          <View style={styles.exchangeSection}>
            <Text style={[styles.exchangeLabel, { color: colors.textSecondary }]}>
              {proposal.is_proposer ? 'Vous offrez' : 'Ils offrent'}
            </Text>
            {proposal.offered_coins > 0 && (
              <Text style={[styles.exchangeValue, { color: colors.success }]}>
                💰 {proposal.offered_coins} coins
              </Text>
            )}
            {proposal.offered_time_hours > 0 && (
              <Text style={[styles.exchangeValue, { color: colors.primary }]}>
                ⏰ {proposal.offered_time_hours}h
              </Text>
            )}
          </View>

          <Icon name="swap-horiz" size={24} color={colors.textSecondary} />

          <View style={styles.exchangeSection}>
            <Text style={[styles.exchangeLabel, { color: colors.textSecondary }]}>
              {proposal.is_proposer ? 'Vous demandez' : 'Ils demandent'}
            </Text>
            {proposal.requested_coins > 0 && (
              <Text style={[styles.exchangeValue, { color: colors.warning }]}>
                💰 {proposal.requested_coins} coins
              </Text>
            )}
            {proposal.requested_time_hours > 0 && (
              <Text style={[styles.exchangeValue, { color: colors.accent }]}>
                ⏰ {proposal.requested_time_hours}h
              </Text>
            )}
          </View>
        </View>

        {proposal.proposal_message && (
          <Text style={[styles.proposalMessage, { color: colors.text }]}>
            "{proposal.proposal_message}"
          </Text>
        )}

        {proposal.status === 'PENDING' && !proposal.is_proposer && (
          <View style={styles.proposalActions}>
            <TouchableOpacity 
              style={[styles.actionButton, { backgroundColor: colors.success }]}
              onPress={() => handleAcceptProposal(proposal.id)}
            >
              <Icon name="check" size={20} color="#FFFFFF" />
              <Text style={styles.actionButtonText}>Accepter</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.actionButton, { backgroundColor: colors.error }]}
              onPress={() => handleRejectProposal(proposal.id)}
            >
              <Icon name="close" size={20} color="#FFFFFF" />
              <Text style={styles.actionButtonText}>Rejeter</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  const renderActiveProposals = () => {
    const activeProposals = proposals.filter(p => p.status === 'PENDING' || p.status === 'ACCEPTED');
    
    if (activeProposals.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Icon name="swap-horiz" size={48} color={colors.textSecondary} />
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            Aucune proposition active
          </Text>
          <TouchableOpacity 
            style={[styles.createButton, { backgroundColor: colors.primary }]}
            onPress={() => setSelectedTab('create')}
          >
            <Text style={styles.createButtonText}>Créer une proposition</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <ScrollView style={styles.proposalsList}>
        {activeProposals.map(renderProposalCard)}
      </ScrollView>
    );
  };

  const renderCreateInterface = () => (
    <ScrollView style={styles.createContainer}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Créer une Proposition de Troc
      </Text>

      <View style={styles.resourceSelection}>
        <Text style={[styles.selectionLabel, { color: colors.text }]}>
          Sélectionnez vos ressources
        </Text>
        
        <View style={styles.resourceGrid}>
          {userResources.map((resource) => (
            <TouchableOpacity
              key={resource.id}
              style={[
                styles.resourceCard,
                { backgroundColor: colors.surface },
                (selectedOffer?.id === resource.id || selectedNeed?.id === resource.id) && 
                { borderColor: colors.primary, borderWidth: 2 }
              ]}
              onPress={() => {
                if (resource.type === 'OFFER') {
                  setSelectedOffer(resource);
                } else {
                  setSelectedNeed(resource);
                }
              }}
            >
              <Icon 
                name={resource.type === 'OFFER' ? 'local-offer' : 'help-outline'} 
                size={24} 
                color={resource.type === 'OFFER' ? colors.success : colors.warning} 
              />
              <Text style={[styles.resourceTitle, { color: colors.text }]}>
                {resource.title}
              </Text>
              <Text style={[styles.resourceType, { color: colors.textSecondary }]}>
                {resource.type === 'OFFER' ? 'Offre' : 'Besoin'}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.exchangeInputs}>
        <Text style={[styles.inputLabel, { color: colors.text }]}>
          Conditions d'échange
        </Text>
        
        <View style={styles.inputRow}>
          <View style={styles.inputGroup}>
            <Text style={[styles.inputSubLabel, { color: colors.textSecondary }]}>
              Coins offerts
            </Text>
            <TextInput
              style={[styles.input, { backgroundColor: colors.surface, color: colors.text }]}
              value={offeredCoins}
              onChangeText={setOfferedCoins}
              keyboardType="numeric"
              placeholder="0"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.inputSubLabel, { color: colors.textSecondary }]}>
              Temps offert (h)
            </Text>
            <TextInput
              style={[styles.input, { backgroundColor: colors.surface, color: colors.text }]}
              value={offeredTime}
              onChangeText={setOfferedTime}
              keyboardType="numeric"
              placeholder="0"
            />
          </View>
        </View>

        <View style={styles.inputRow}>
          <View style={styles.inputGroup}>
            <Text style={[styles.inputSubLabel, { color: colors.textSecondary }]}>
              Coins demandés
            </Text>
            <TextInput
              style={[styles.input, { backgroundColor: colors.surface, color: colors.text }]}
              value={requestedCoins}
              onChangeText={setRequestedCoins}
              keyboardType="numeric"
              placeholder="0"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.inputSubLabel, { color: colors.textSecondary }]}>
              Temps demandé (h)
            </Text>
            <TextInput
              style={[styles.input, { backgroundColor: colors.surface, color: colors.text }]}
              value={requestedTime}
              onChangeText={setRequestedTime}
              keyboardType="numeric"
              placeholder="0"
            />
          </View>
        </View>

        <TextInput
          style={[styles.messageInput, { backgroundColor: colors.surface, color: colors.text }]}
          value={proposalMessage}
          onChangeText={setProposalMessage}
          placeholder="Message de proposition (optionnel)"
          placeholderTextColor={colors.textSecondary}
          multiline
          numberOfLines={3}
        />

        <TouchableOpacity 
          style={[styles.submitButton, { backgroundColor: colors.primary }]}
          onPress={handleCreateProposal}
        >
          <Icon name="send" size={20} color="#FFFFFF" />
          <Text style={styles.submitButtonText}>Envoyer la Proposition</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderHistory = () => {
    const historyProposals = proposals.filter(p => p.status === 'COMPLETED' || p.status === 'REJECTED');
    
    if (historyProposals.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Icon name="history" size={48} color={colors.textSecondary} />
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            Aucun historique de troc
          </Text>
        </View>
      );
    }

    return (
      <ScrollView style={styles.proposalsList}>
        {historyProposals.map(renderProposalCard)}
      </ScrollView>
    );
  };

  const renderContent = () => {
    switch (selectedTab) {
      case 'active':
        return renderActiveProposals();
      case 'create':
        return renderCreateInterface();
      case 'history':
        return renderHistory();
      default:
        return renderActiveProposals();
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Icon name="swap-horiz" size={48} color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Chargement des trocs...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Système de Troc
        </Text>
        <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
          Échangez objets, temps et services
        </Text>
      </View>

      {renderTabBar()}
      {renderContent()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  tabBar: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 4,
  },
  tabItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  tabItemActive: {
    backgroundColor: '#FFFFFF',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tabLabel: {
    marginLeft: 6,
    fontSize: 12,
    fontWeight: '600',
  },
  proposalsList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  proposalCard: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  proposalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  proposalDate: {
    fontSize: 12,
  },
  proposalType: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  exchangeDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  exchangeSection: {
    flex: 1,
    alignItems: 'center',
  },
  exchangeLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  exchangeValue: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  proposalMessage: {
    fontSize: 14,
    fontStyle: 'italic',
    marginBottom: 12,
  },
  proposalActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  actionButtonText: {
    color: '#FFFFFF',
    marginLeft: 4,
    fontWeight: '600',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  createButton: {
    marginTop: 20,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
  },
  createButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  createContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  resourceSelection: {
    marginBottom: 24,
  },
  selectionLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  resourceGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  resourceCard: {
    width: '48%',
    padding: 12,
    marginBottom: 12,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  resourceTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  resourceType: {
    fontSize: 12,
    marginTop: 4,
  },
  exchangeInputs: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  inputGroup: {
    flex: 1,
    marginHorizontal: 4,
  },
  inputSubLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  input: {
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
  },
  messageInput: {
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
    textAlignVertical: 'top',
    marginBottom: 20,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default BarterScreen;
