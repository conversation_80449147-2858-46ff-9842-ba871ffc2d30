/**
 * <PERSON><PERSON><PERSON> d'accueil de l'application Nowee
 * Affiche les besoins récents, suggestions et actions rapides
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';

import { useTheme } from '../services/ThemeService';
import { useAuth } from '../services/AuthService';
import { useLocation } from '../services/LocationService';
import { ApiService } from '../services/ApiService';
import { EconomyService } from '../services/EconomyService';
import { BarterService } from '../services/BarterService';
import { NeedCard } from '../components/NeedCard';
import { QuickActionButton } from '../components/QuickActionButton';
import { StatsCard } from '../components/StatsCard';
import { WalletCard } from '../components/WalletCard';

const { width } = Dimensions.get('window');

interface HomeData {
  recentNeeds: any[];
  nearbyOffers: any[];
  userStats: {
    helpGiven: number;
    helpReceived: number;
    rating: number;
  };
  communityStats: {
    activeUsers: number;
    totalHelps: number;
    responseTime: string;
  };
  walletData: {
    nowee_coins: number;
    time_credits: number;
    total_earned: number;
    total_spent: number;
  };
  barterProposals: any[];
  economyStats: {
    total_coins_in_circulation: number;
    total_transactions: number;
    average_wallet_balance: number;
  };
}

const HomeScreen: React.FC = () => {
  const { colors } = useTheme();
  const { user } = useAuth();
  const { location } = useLocation();
  const [homeData, setHomeData] = useState<HomeData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadHomeData();
  }, [location]);

  const loadHomeData = async () => {
    try {
      setIsLoading(true);

      // Charger les données de base
      const baseData = await ApiService.getHomeData(location);

      // Charger les données économiques si l'utilisateur est connecté
      let walletData = null;
      let barterProposals = [];
      let economyStats = null;

      if (user?.phone) {
        try {
          [walletData, barterProposals, economyStats] = await Promise.all([
            EconomyService.getWallet(user.phone),
            BarterService.getUserProposals(user.phone),
            EconomyService.getEconomyStats(),
          ]);
        } catch (economyError) {
          console.warn('Erreur données économiques:', economyError);
          // Utiliser des données par défaut
          walletData = {
            nowee_coins: 100,
            time_credits: 0,
            total_earned: 100,
            total_spent: 0,
          };
          economyStats = {
            total_coins_in_circulation: 0,
            total_transactions: 0,
            average_wallet_balance: 0,
          };
        }
      }

      const enhancedData = {
        ...baseData,
        walletData: walletData || {
          nowee_coins: 0,
          time_credits: 0,
          total_earned: 0,
          total_spent: 0,
        },
        barterProposals,
        economyStats: economyStats || {
          total_coins_in_circulation: 0,
          total_transactions: 0,
          average_wallet_balance: 0,
        },
      };

      setHomeData(enhancedData);
    } catch (error) {
      console.error('Erreur lors du chargement des données d\'accueil:', error);
      Alert.alert('Erreur', 'Impossible de charger les données. Vérifiez votre connexion.');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHomeData();
    setRefreshing(false);
  };

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'express_need':
        // Navigation vers l'écran de création de besoin express
        break;
      case 'offer_help':
        // Navigation vers l'écran d'offre d'aide
        break;
      case 'emergency':
        // Gestion des urgences
        Alert.alert(
          'Urgence',
          'Contactez-vous les services d\'urgence ?',
          [
            { text: 'Annuler', style: 'cancel' },
            { text: 'Oui', onPress: () => {/* Logique d'urgence */} }
          ]
        );
        break;
      case 'voice_request':
        // Activation de la reconnaissance vocale
        break;
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Bonjour';
    if (hour < 18) return 'Bon après-midi';
    return 'Bonsoir';
  };

  const getLocationText = () => {
    if (location) {
      return `${location.city}, ${location.country}`;
    }
    return 'Localisation non disponible';
  };

  if (isLoading && !homeData) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Chargement...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* En-tête avec salutation */}
        <Animatable.View animation="fadeInDown" duration={800} style={styles.header}>
          <LinearGradient
            colors={[colors.primary, colors.secondary]}
            style={styles.headerGradient}
          >
            <View style={styles.headerContent}>
              <Text style={styles.greeting}>
                {getGreeting()}, {user?.name || 'Ami'}! 👋
              </Text>
              <View style={styles.locationContainer}>
                <Icon name="location-on" size={16} color="#FFFFFF" />
                <Text style={styles.locationText}>{getLocationText()}</Text>
              </View>
            </View>
          </LinearGradient>
        </Animatable.View>

        {/* Portefeuille */}
        {homeData?.walletData && user && (
          <Animatable.View animation="fadeInLeft" duration={800} delay={300}>
            <WalletCard walletData={homeData.walletData} />
          </Animatable.View>
        )}

        {/* Actions rapides */}
        <Animatable.View animation="fadeInUp" duration={800} delay={400} style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Actions rapides
          </Text>
          <View style={styles.quickActionsContainer}>
            <QuickActionButton
              icon="add-circle"
              title="Besoin express"
              subtitle="En 30 secondes"
              color={colors.primary}
              onPress={() => handleQuickAction('express_need')}
            />
            <QuickActionButton
              icon="volunteer-activism"
              title="Offrir de l'aide"
              subtitle="Aider quelqu'un"
              color={colors.secondary}
              onPress={() => handleQuickAction('offer_help')}
            />
            <QuickActionButton
              icon="emergency"
              title="Urgence"
              subtitle="Aide immédiate"
              color={colors.error}
              onPress={() => handleQuickAction('emergency')}
            />
            <QuickActionButton
              icon="mic"
              title="Vocal"
              subtitle="Parlez votre besoin"
              color={colors.accent}
              onPress={() => handleQuickAction('voice_request')}
            />
          </View>
        </Animatable.View>

        {/* Statistiques personnelles */}
        {homeData?.userStats && (
          <Animatable.View animation="fadeInUp" duration={800} delay={400} style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Votre impact
            </Text>
            <View style={styles.statsContainer}>
              <StatsCard
                icon="favorite"
                value={homeData.userStats.helpGiven}
                label="Aides données"
                color={colors.success}
              />
              <StatsCard
                icon="support"
                value={homeData.userStats.helpReceived}
                label="Aides reçues"
                color={colors.primary}
              />
              <StatsCard
                icon="star"
                value={homeData.userStats.rating.toFixed(1)}
                label="Note moyenne"
                color={colors.accent}
              />
            </View>
          </Animatable.View>
        )}

        {/* Besoins récents à proximité */}
        {homeData?.recentNeeds && homeData.recentNeeds.length > 0 && (
          <Animatable.View animation="fadeInUp" duration={800} delay={600} style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Besoins à proximité
              </Text>
              <TouchableOpacity>
                <Text style={[styles.seeAllText, { color: colors.primary }]}>
                  Voir tout
                </Text>
              </TouchableOpacity>
            </View>
            {homeData.recentNeeds.slice(0, 3).map((need, index) => (
              <NeedCard
                key={need.id}
                need={need}
                onPress={() => {/* Navigation vers détail */}}
                style={{ marginBottom: 12 }}
              />
            ))}
          </Animatable.View>
        )}

        {/* Statistiques de la communauté */}
        {homeData?.communityStats && (
          <Animatable.View animation="fadeInUp" duration={800} delay={800} style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Communauté Nowee
            </Text>
            <View style={[styles.communityStatsCard, { backgroundColor: colors.surface }]}>
              <View style={styles.communityStatItem}>
                <Icon name="people" size={24} color={colors.primary} />
                <Text style={[styles.communityStatValue, { color: colors.text }]}>
                  {homeData.communityStats.activeUsers}
                </Text>
                <Text style={[styles.communityStatLabel, { color: colors.textSecondary }]}>
                  Utilisateurs actifs
                </Text>
              </View>
              <View style={styles.communityStatItem}>
                <Icon name="handshake" size={24} color={colors.success} />
                <Text style={[styles.communityStatValue, { color: colors.text }]}>
                  {homeData.communityStats.totalHelps}
                </Text>
                <Text style={[styles.communityStatLabel, { color: colors.textSecondary }]}>
                  Aides réalisées
                </Text>
              </View>
              <View style={styles.communityStatItem}>
                <Icon name="speed" size={24} color={colors.accent} />
                <Text style={[styles.communityStatValue, { color: colors.text }]}>
                  {homeData.communityStats.responseTime}
                </Text>
                <Text style={[styles.communityStatLabel, { color: colors.textSecondary }]}>
                  Temps de réponse
                </Text>
              </View>
            </View>
          </Animatable.View>
        )}

        {/* Espace en bas pour la navigation */}
        <View style={{ height: 20 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  header: {
    marginBottom: 20,
  },
  headerGradient: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  headerContent: {
    alignItems: 'flex-start',
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 14,
    color: '#FFFFFF',
    marginLeft: 4,
    opacity: 0.9,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  seeAllText: {
    fontSize: 14,
    fontWeight: '600',
  },
  quickActionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  communityStatsCard: {
    borderRadius: 12,
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  communityStatItem: {
    alignItems: 'center',
  },
  communityStatValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  communityStatLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default HomeScreen;
