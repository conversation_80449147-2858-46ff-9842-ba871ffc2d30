/**
 * API d'enregistrement des utilisateurs pour Nowee
 * Gère l'inscription et la mise à jour des profils utilisateurs
 */

import express from 'express';
import { dbService } from '../services/databaseServiceUnified.js';
import { extractLocationFromMessage } from '../utils/locationUtils.js';

const router = express.Router();

/**
 * POST /api/register
 * Enregistre ou met à jour un utilisateur
 */
router.post('/register', async (req, res) => {
  try {
    const {
      phone,
      name,
      email,
      location,
      city,
      country,
      preferences,
      languages
    } = req.body;

    // Validation des données requises
    if (!phone) {
      return res.status(400).json({
        success: false,
        error: 'Le numéro de téléphone est requis'
      });
    }

    // Normaliser le numéro de téléphone
    const normalizedPhone = normalizePhoneNumber(phone);

    // Préparer les données utilisateur
    const userData = {
      name: name?.trim() || null,
      email: email?.trim() || null,
      city: city?.trim() || null,
      country: country?.trim() || 'Sénégal',
      preferences: preferences || {},
      languages: languages || ['fr']
    };

    // Traiter la localisation
    if (location) {
      if (typeof location === 'string') {
        // Si c'est une chaîne, essayer d'extraire la localisation
        const extractedLocation = extractLocationFromMessage(location);
        userData.location = extractedLocation;
        userData.city = extractedLocation?.city || userData.city;
      } else if (location.coordinates) {
        // Si c'est un objet avec coordonnées
        userData.location = location;
        userData.coordinates = location.coordinates;
        userData.city = location.city || userData.city;
      }
    }

    // Créer ou mettre à jour l'utilisateur
    const user = await dbService.getUserProfile(normalizedPhone);
    
    // Si l'utilisateur existe déjà, mettre à jour ses informations
    if (user && (name || email || location)) {
      // En mode Supabase, mettre à jour
      if (dbService.isUsingSupabase()) {
        const { data: updatedUser, error } = await dbService.supabase
          .from('users')
          .update({
            name: userData.name || user.name,
            email: userData.email || user.email,
            location: userData.location || user.location,
            coordinates: userData.coordinates ? 
              `POINT(${userData.coordinates.longitude} ${userData.coordinates.latitude})` : user.coordinates,
            city: userData.city || user.city,
            country: userData.country || user.country,
            preferences: { ...user.preferences, ...userData.preferences },
            languages: userData.languages || user.languages,
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id)
          .select()
          .single();

        if (error) throw error;
        
        return res.json({
          success: true,
          message: 'Profil utilisateur mis à jour avec succès',
          user: updatedUser
        });
      } else {
        // Mode fallback - mettre à jour en mémoire
        Object.assign(user, {
          name: userData.name || user.name,
          email: userData.email || user.email,
          location: userData.location || user.location,
          coordinates: userData.coordinates || user.coordinates,
          city: userData.city || user.city,
          country: userData.country || user.country,
          preferences: { ...user.preferences, ...userData.preferences },
          languages: userData.languages || user.languages,
          updated_at: new Date()
        });
      }
    }

    // Réponse de succès
    res.json({
      success: true,
      message: user.name ? 'Profil utilisateur mis à jour avec succès' : 'Utilisateur enregistré avec succès',
      user: {
        id: user.id,
        phone: user.phone,
        name: user.name,
        email: user.email,
        city: user.city,
        country: user.country,
        rating: user.rating,
        help_given: user.help_given,
        help_received: user.help_received,
        created_at: user.created_at,
        is_new: !user.name // Indique si c'est un nouvel utilisateur
      }
    });

  } catch (error) {
    console.error('Erreur API register:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * GET /api/profile/:phone
 * Récupère le profil d'un utilisateur
 */
router.get('/profile/:phone', async (req, res) => {
  try {
    const { phone } = req.params;
    const normalizedPhone = normalizePhoneNumber(phone);

    const user = await dbService.getUserProfile(normalizedPhone);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'Utilisateur non trouvé'
      });
    }

    res.json({
      success: true,
      user: {
        id: user.id,
        phone: user.phone,
        name: user.name,
        email: user.email,
        city: user.city,
        country: user.country,
        rating: user.rating,
        help_given: user.help_given,
        help_received: user.help_received,
        preferences: user.preferences,
        languages: user.languages,
        created_at: user.created_at,
        last_active_at: user.last_active_at
      }
    });

  } catch (error) {
    console.error('Erreur API profile:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur'
    });
  }
});

/**
 * POST /api/resources
 * Crée une nouvelle ressource (besoin ou offre)
 */
router.post('/resources', async (req, res) => {
  try {
    const {
      phone,
      type, // 'NEED' ou 'OFFER'
      description,
      category,
      urgency,
      location,
      tags,
      conditions,
      availability
    } = req.body;

    // Validation
    if (!phone || !type || !description) {
      return res.status(400).json({
        success: false,
        error: 'Téléphone, type et description sont requis'
      });
    }

    if (!['NEED', 'OFFER'].includes(type)) {
      return res.status(400).json({
        success: false,
        error: 'Le type doit être NEED ou OFFER'
      });
    }

    const normalizedPhone = normalizePhoneNumber(phone);
    const user = await dbService.getUserProfile(normalizedPhone);

    // Préparer les données de la ressource
    const resourceData = {
      type,
      description: description.trim(),
      category: category || 'GENERAL',
      urgency: urgency || 1,
      location: location || null,
      tags: tags || [],
      conditions: conditions || null,
      availability: availability || null
    };

    // Créer la ressource
    let resource;
    if (type === 'NEED') {
      resource = await dbService.recordUserNeed(normalizedPhone, resourceData);
    } else {
      resource = await dbService.recordUserOffer(normalizedPhone, resourceData);
    }

    res.json({
      success: true,
      message: `${type === 'NEED' ? 'Besoin' : 'Offre'} créé(e) avec succès`,
      resource: {
        id: resource.id,
        type: resource.type,
        category: resource.category,
        title: resource.title,
        description: resource.description,
        urgency: resource.urgency,
        status: resource.status,
        created_at: resource.created_at
      }
    });

  } catch (error) {
    console.error('Erreur API resources:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur'
    });
  }
});

/**
 * GET /api/resources/search
 * Recherche des ressources
 */
router.get('/resources/search', async (req, res) => {
  try {
    const {
      q: query,
      type,
      category,
      lat,
      lon,
      radius = 10
    } = req.query;

    let coordinates = null;
    if (lat && lon) {
      coordinates = {
        latitude: parseFloat(lat),
        longitude: parseFloat(lon)
      };
    }

    // Recherche via Supabase si disponible
    if (dbService.isUsingSupabase()) {
      const resources = await dbService.supabase.searchResources(
        query,
        coordinates,
        parseInt(radius),
        type
      );

      return res.json({
        success: true,
        resources: resources.map(r => ({
          id: r.id,
          type: r.type,
          category: r.category,
          title: r.title,
          description: r.description,
          urgency: r.urgency,
          distance_km: r.distance_km,
          user: {
            name: r.users?.name,
            rating: r.users?.rating
          },
          created_at: r.created_at
        }))
      });
    }

    // Fallback en mémoire
    const memoryStorage = dbService.getMemoryStorage();
    let resources = Array.from(memoryStorage.resources.values())
      .filter(r => r.status === 'ACTIVE');

    if (type) {
      resources = resources.filter(r => r.type === type);
    }

    if (category) {
      resources = resources.filter(r => r.category === category);
    }

    if (query) {
      const lowerQuery = query.toLowerCase();
      resources = resources.filter(r => 
        r.title.toLowerCase().includes(lowerQuery) ||
        r.description.toLowerCase().includes(lowerQuery)
      );
    }

    res.json({
      success: true,
      resources: resources.slice(0, 20).map(r => ({
        id: r.id,
        type: r.type,
        category: r.category,
        title: r.title,
        description: r.description,
        urgency: r.urgency,
        created_at: r.created_at
      }))
    });

  } catch (error) {
    console.error('Erreur API search:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur'
    });
  }
});

/**
 * Normalise un numéro de téléphone
 */
function normalizePhoneNumber(phone) {
  // Supprimer tous les caractères non numériques sauf le +
  let normalized = phone.replace(/[^\d+]/g, '');
  
  // Si commence par 00, remplacer par +
  if (normalized.startsWith('00')) {
    normalized = '+' + normalized.substring(2);
  }
  
  // Si commence par 7 ou 3 (Sénégal), ajouter +221
  if (/^[73]/.test(normalized)) {
    normalized = '+221' + normalized;
  }
  
  return normalized;
}

export default router;
