# 🔑 Guide Visuel - Obtenir vos Clés API

## 🤖 OpenAI (Onglet ouvert dans votre navigateur)

### Étapes pour OpenAI :
1. **Créer un compte** sur https://platform.openai.com (si pas déjà fait)
2. **Se connecter** à votre compte
3. **Aller dans "API Keys"** (lien déjà ouvert)
4. **<PERSON><PERSON>r sur "Create new secret key"**
5. **Donner un nom** : "Nowee Bot"
6. **IMPORTANT** : Copier immédiatement la clé (elle commence par `sk-proj-` ou `sk-`)
7. **Ajouter des crédits** : Aller dans "Billing" et ajouter 5-10$ minimum

### Format de la clé OpenAI :
```
sk-proj-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

---

## 📱 Twilio (Onglet ouvert dans votre navigateur)

### Étapes pour Twilio :
1. **C<PERSON>er un compte** sur https://www.twilio.com/try-twilio (si pas déjà fait)
2. **Vérifier votre numéro** de téléphone
3. **Aller dans la Console** : https://console.twilio.com/
4. **Noter votre Account SID** (commence par `AC`)
5. **Cliquer sur "Show"** pour révéler l'Auth Token
6. **Copier l'Auth Token**

### Activer WhatsApp Sandbox :
1. **Dans la console Twilio** : Messaging > Try it out > Send a WhatsApp message
2. **Ou directement** : https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn
3. **Noter le numéro sandbox** (généralement ****** 523 8886)
4. **Noter le code de jointure** (ex: "join xxxxx-xxxxx")

### Format des identifiants Twilio :
```
Account SID: ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
Auth Token:  xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
WhatsApp:    whatsapp:+***********
```

---

## ✅ Une fois que vous avez vos clés...

Revenez ici et nous lancerons le script de configuration automatique !

### Informations à avoir sous la main :
- [ ] Clé API OpenAI (sk-proj-...)
- [ ] Twilio Account SID (AC...)
- [ ] Twilio Auth Token
- [ ] Numéro WhatsApp Twilio

---

## 💡 Conseils

### OpenAI :
- **Coût** : ~0.15$ pour 1000 messages avec GPT-4o-mini
- **Budget recommandé** : 10$ pour commencer
- **Surveillance** : Configurez des alertes de facturation

### Twilio :
- **Sandbox** : Gratuit pour les tests
- **Production** : ~0.005$ par message WhatsApp
- **Numéro dédié** : ~1$ par mois (optionnel)

### Sécurité :
- **Ne jamais** partager vos clés API
- **Ne jamais** les commiter dans git
- **Régénérer** si compromises
