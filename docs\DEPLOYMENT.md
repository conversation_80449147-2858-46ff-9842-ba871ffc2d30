# 🚀 Guide de Déploiement Nowee

Ce guide vous explique comment déployer votre bot Nowee sur différentes plateformes cloud.

## 🎯 Options de Déploiement

### 1. 🚂 Railway (Recommandé)
- ✅ **Gratuit** pour commencer
- ✅ **PostgreSQL inclus**
- ✅ **Déploiement automatique**
- ✅ **Domaine HTTPS gratuit**

### 2. ▲ Vercel + Supabase
- ✅ **Serverless**
- ✅ **Très rapide**
- ✅ **Base de données Supabase**

### 3. 🟣 Heroku
- ✅ **Classique et fiable**
- ⚠️ **Plus cher**
- ✅ **Add-ons disponibles**

### 4. 🐳 Docker (VPS)
- ✅ **Contrôle total**
- ⚠️ **Gestion manuelle**
- ✅ **Économique à long terme**

---

## 🚂 Déploiement sur Railway

### Étape 1: Préparation
```bash
# Assurez-vous que tout fonctionne localement
npm run verify
npm run db:generate
```

### Étape 2: Créer un compte Railway
1. Allez sur [railway.app](https://railway.app)
2. Connectez-vous avec GitHub
3. Créez un nouveau projet

### Étape 3: Déployer depuis GitHub
1. **Connectez votre repo GitHub**
2. **Railway détectera automatiquement** le fichier `railway.json`
3. **Ajoutez une base PostgreSQL** :
   - Dans votre projet Railway
   - Cliquez sur "New" → "Database" → "PostgreSQL"

### Étape 4: Configurer les variables d'environnement
Dans Railway, ajoutez ces variables :
```env
OPENAI_API_KEY=sk-your-openai-key
TWILIO_ACCOUNT_SID=AC-your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
NODE_ENV=production
```

La `DATABASE_URL` sera automatiquement configurée par Railway.

### Étape 5: Configurer le webhook Twilio
1. **Récupérez l'URL de votre app** Railway (ex: `https://nowee-production.up.railway.app`)
2. **Dans Twilio Console** → WhatsApp Sandbox Settings
3. **Webhook URL**: `https://votre-app.railway.app/webhook`

### Étape 6: Tester
```bash
curl https://votre-app.railway.app/health
```

---

## ▲ Déploiement sur Vercel + Supabase

### Étape 1: Créer une base Supabase
1. Allez sur [supabase.com](https://supabase.com)
2. Créez un nouveau projet
3. Notez votre `DATABASE_URL` dans Settings → Database

### Étape 2: Configurer Vercel
```bash
# Installer Vercel CLI
npm i -g vercel

# Déployer
vercel

# Configurer les variables d'environnement
vercel env add OPENAI_API_KEY
vercel env add TWILIO_ACCOUNT_SID
vercel env add TWILIO_AUTH_TOKEN
vercel env add DATABASE_URL
```

### Étape 3: Initialiser la base de données
```bash
# Depuis votre machine locale avec la DATABASE_URL de Supabase
DATABASE_URL="postgresql://..." npx prisma db push
DATABASE_URL="postgresql://..." npm run db:seed
```

---

## 🟣 Déploiement sur Heroku

### Étape 1: Installer Heroku CLI
```bash
# macOS
brew tap heroku/brew && brew install heroku

# Windows
# Téléchargez depuis heroku.com
```

### Étape 2: Créer l'application
```bash
heroku create nowee-bot-votre-nom
heroku addons:create heroku-postgresql:mini
```

### Étape 3: Configurer les variables
```bash
heroku config:set OPENAI_API_KEY=sk-your-key
heroku config:set TWILIO_ACCOUNT_SID=AC-your-sid
heroku config:set TWILIO_AUTH_TOKEN=your-token
heroku config:set TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
heroku config:set NODE_ENV=production
```

### Étape 4: Déployer
```bash
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

### Étape 5: Initialiser la base
```bash
heroku run npm run db:push
heroku run npm run db:seed
```

---

## 🐳 Déploiement Docker (VPS)

### Étape 1: Préparer votre VPS
```bash
# Sur votre serveur Ubuntu/Debian
sudo apt update
sudo apt install docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker
```

### Étape 2: Cloner et configurer
```bash
git clone https://github.com/votre-username/nowee.git
cd nowee
cp .env.example .env
# Éditez .env avec vos vraies clés
```

### Étape 3: Démarrer avec Docker Compose
```bash
docker-compose up -d
```

### Étape 4: Initialiser la base
```bash
docker-compose exec app npm run db:push
docker-compose exec app npm run db:seed
```

### Étape 5: Configurer un reverse proxy (Nginx)
```nginx
server {
    listen 80;
    server_name votre-domaine.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

---

## 🔧 Configuration Post-Déploiement

### 1. Configurer le webhook Twilio
Une fois déployé, configurez votre webhook Twilio :
- **URL**: `https://votre-domaine.com/webhook`
- **Méthode**: POST
- **Content-Type**: application/x-www-form-urlencoded

### 2. Tester le déploiement
```bash
# Test de santé
curl https://votre-domaine.com/health

# Test webhook (remplacez par votre URL)
curl -X POST https://votre-domaine.com/webhook \
  -d "Body=Bonjour" \
  -d "From=whatsapp:+**********"
```

### 3. Monitoring
Configurez des alertes pour :
- ✅ Santé de l'application (`/health`)
- ✅ Erreurs 5xx
- ✅ Temps de réponse
- ✅ Usage de la base de données

---

## 🔒 Sécurité en Production

### Variables d'environnement
```env
# Générez une clé admin sécurisée
ADMIN_API_KEY=your-secure-admin-key-here

# Configurez un JWT secret si vous ajoutez l'auth
JWT_SECRET=your-jwt-secret-here

# Limitez les CORS si nécessaire
ALLOWED_ORIGINS=https://votre-domaine.com
```

### Rate Limiting
Le bot inclut déjà un rate limiting, mais vous pouvez l'ajuster :
```javascript
// Dans src/bot/nowee-whatsapp-bot.js
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // Réduire en production
});
```

### Logs et Monitoring
Ajoutez un service de logging comme :
- **Sentry** pour les erreurs
- **LogRocket** pour les sessions
- **DataDog** pour les métriques

---

## 📊 Maintenance

### Sauvegardes automatiques
```bash
# Script de sauvegarde (à programmer avec cron)
#!/bin/bash
pg_dump $DATABASE_URL > backup-$(date +%Y%m%d).sql
```

### Mise à jour
```bash
# Déploiement continu avec GitHub Actions
git push origin main
# → Déploiement automatique sur Railway/Vercel
```

### Monitoring des coûts
- **OpenAI** : Surveillez l'usage sur platform.openai.com
- **Twilio** : Configurez des alertes de facturation
- **Hébergement** : Surveillez les métriques de performance

---

## 🆘 Dépannage

### Erreurs communes

**"Database connection failed"**
```bash
# Vérifiez la DATABASE_URL
echo $DATABASE_URL
npx prisma db push
```

**"OpenAI API error"**
```bash
# Vérifiez votre clé et vos crédits
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/models
```

**"Webhook not receiving messages"**
- ✅ Vérifiez l'URL webhook dans Twilio
- ✅ Testez avec curl
- ✅ Vérifiez les logs de l'application

### Logs utiles
```bash
# Railway
railway logs

# Heroku
heroku logs --tail

# Docker
docker-compose logs -f app
```

---

## 🎉 Félicitations !

Votre bot Nowee est maintenant déployé en production ! 

**Prochaines étapes :**
1. 📱 Testez avec de vrais utilisateurs
2. 📊 Surveillez les métriques
3. 🔄 Itérez selon les retours
4. 📈 Scalez selon la demande

**Support :**
- 📖 Documentation : `README.md`
- 🐛 Issues : GitHub Issues
- 💬 Communauté : Discord/Slack
