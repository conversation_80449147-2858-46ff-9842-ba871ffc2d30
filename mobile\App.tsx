/**
 * Application mobile Nowee
 * Point d'entrée principal de l'application React Native
 */

import React, { useEffect, useState } from 'react';
import {
  StatusBar,
  StyleSheet,
  useColorScheme,
  Alert,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Geolocation from 'react-native-geolocation-service';
import { request, PERMISSIONS, RESULTS } from 'react-native-permissions';

// Import des écrans
import HomeScreen from './src/screens/HomeScreen';
import ChatScreen from './src/screens/ChatScreen';
import MapScreen from './src/screens/MapScreen';
import ProfileScreen from './src/screens/ProfileScreen';
import NeedsScreen from './src/screens/NeedsScreen';
import OffersScreen from './src/screens/OffersScreen';
import OnboardingScreen from './src/screens/OnboardingScreen';
import LoginScreen from './src/screens/LoginScreen';
import WalletScreen from './src/screens/WalletScreen';
import BarterScreen from './src/screens/BarterScreen';

// Import des services
import { AuthProvider, useAuth } from './src/services/AuthService';
import { LocationProvider } from './src/services/LocationService';
import { NotificationService } from './src/services/NotificationService';
import { ThemeProvider, useTheme } from './src/services/ThemeService';

// Import des types
import { RootStackParamList, TabParamList } from './src/types/navigation';

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<TabParamList>();

// Configuration des couleurs
const Colors = {
  light: {
    primary: '#2E7D32',
    secondary: '#4CAF50',
    background: '#FFFFFF',
    surface: '#F5F5F5',
    text: '#212121',
    textSecondary: '#757575',
    accent: '#FF9800',
    error: '#F44336',
    success: '#4CAF50',
    warning: '#FF9800',
  },
  dark: {
    primary: '#4CAF50',
    secondary: '#66BB6A',
    background: '#121212',
    surface: '#1E1E1E',
    text: '#FFFFFF',
    textSecondary: '#B0B0B0',
    accent: '#FFB74D',
    error: '#EF5350',
    success: '#66BB6A',
    warning: '#FFB74D',
  },
};

// Composant des onglets principaux
function MainTabs() {
  const { colors } = useTheme();
  
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Map':
              iconName = 'map';
              break;
            case 'Wallet':
              iconName = 'account-balance-wallet';
              break;
            case 'Barter':
              iconName = 'swap-horiz';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.surface,
        },
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: '#FFFFFF',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{ title: 'Accueil' }}
      />
      <Tab.Screen
        name="Map"
        component={MapScreen}
        options={{ title: 'Carte' }}
      />
      <Tab.Screen
        name="Wallet"
        component={WalletScreen}
        options={{ title: 'Portefeuille' }}
      />
      <Tab.Screen
        name="Barter"
        component={BarterScreen}
        options={{ title: 'Troc' }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{ title: 'Profil' }}
      />
    </Tab.Navigator>
  );
}

// Composant principal de navigation
function AppNavigator() {
  const { user, isLoading } = useAuth();
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState<boolean | null>(null);

  useEffect(() => {
    checkOnboardingStatus();
  }, []);

  const checkOnboardingStatus = async () => {
    try {
      const onboardingCompleted = await AsyncStorage.getItem('onboarding_completed');
      setHasCompletedOnboarding(onboardingCompleted === 'true');
    } catch (error) {
      console.error('Erreur lors de la vérification de l\'onboarding:', error);
      setHasCompletedOnboarding(false);
    }
  };

  if (isLoading || hasCompletedOnboarding === null) {
    // Écran de chargement
    return null; // TODO: Ajouter un écran de chargement
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!hasCompletedOnboarding ? (
          <Stack.Screen name="Onboarding" component={OnboardingScreen} />
        ) : !user ? (
          <Stack.Screen name="Login" component={LoginScreen} />
        ) : (
          <Stack.Screen name="Main" component={MainTabs} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

// Composant principal de l'application
function App(): JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';
  const [permissionsGranted, setPermissionsGranted] = useState(false);

  useEffect(() => {
    requestPermissions();
    initializeNotifications();
  }, []);

  const requestPermissions = async () => {
    try {
      if (Platform.OS === 'android') {
        const locationPermission = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Permission de localisation',
            message: 'Nowee a besoin d\'accéder à votre localisation pour vous connecter avec des personnes à proximité.',
            buttonNeutral: 'Plus tard',
            buttonNegative: 'Refuser',
            buttonPositive: 'Accepter',
          },
        );

        const notificationPermission = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
          {
            title: 'Permission de notifications',
            message: 'Nowee souhaite vous envoyer des notifications pour vous tenir informé des nouvelles opportunités d\'aide.',
            buttonNeutral: 'Plus tard',
            buttonNegative: 'Refuser',
            buttonPositive: 'Accepter',
          },
        );

        setPermissionsGranted(
          locationPermission === PermissionsAndroid.RESULTS.GRANTED &&
          notificationPermission === PermissionsAndroid.RESULTS.GRANTED
        );
      } else {
        // iOS permissions
        const locationResult = await request(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
        const notificationResult = await request(PERMISSIONS.IOS.NOTIFICATIONS);
        
        setPermissionsGranted(
          locationResult === RESULTS.GRANTED &&
          notificationResult === RESULTS.GRANTED
        );
      }
    } catch (error) {
      console.error('Erreur lors de la demande de permissions:', error);
      Alert.alert(
        'Erreur',
        'Impossible de demander les permissions nécessaires. Certaines fonctionnalités pourraient ne pas fonctionner correctement.'
      );
    }
  };

  const initializeNotifications = async () => {
    try {
      await NotificationService.initialize();
    } catch (error) {
      console.error('Erreur lors de l\'initialisation des notifications:', error);
    }
  };

  return (
    <SafeAreaProvider>
      <ThemeProvider initialTheme={isDarkMode ? 'dark' : 'light'}>
        <AuthProvider>
          <LocationProvider>
            <StatusBar
              barStyle={isDarkMode ? 'light-content' : 'dark-content'}
              backgroundColor={isDarkMode ? Colors.dark.background : Colors.light.background}
            />
            <AppNavigator />
          </LocationProvider>
        </AuthProvider>
      </ThemeProvider>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default App;
