#!/usr/bin/env node

/**
 * Test hors-ligne du système vocal (sans API)
 */

import { VoiceService } from './src/services/voiceService.js';

async function testOffline() {
  console.log('🎤 Test hors-ligne du système vocal Nowee');
  
  try {
    // Test de détection de langue (ne nécessite pas d'API)
    console.log('🌍 Test détection de langue...');
    
    const tests = [
      { text: 'Bonjour, comment allez-vous ?', expected: 'fr' },
      { text: 'Hello, how are you?', expected: 'en' },
      { text: 'Man dëgg Nowee', expected: 'wo' },
      { text: '<PERSON><PERSON>, ¿cómo estás?', expected: 'es' }
    ];
    
    for (const test of tests) {
      const detected = await VoiceService.detectTextLanguage(test.text);
      const status = detected === test.expected ? '✅' : '⚠️';
      console.log(`${status} "${test.text}" → ${detected} (attendu: ${test.expected})`);
    }
    
    // Test d'adaptation de texte (ne nécessite pas d'API)
    console.log('\n🔧 Test adaptation de texte...');
    
    const textTests = [
      '🎯 Aide trouvée ! ✅ Parfait !',
      '**Urgent** : J\'ai besoin d\'aide `immédiatement`.',
      'Voici votre réponse :\n\n• Point 1\n• Point 2'
    ];
    
    for (const text of textTests) {
      const adapted = VoiceService.adaptTextForLanguage(text, 'fr');
      console.log(`📝 "${text}" → "${adapted}"`);
    }
    
    console.log('\n🎉 Tests hors-ligne terminés avec succès !');
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
    console.error(error.stack);
  }
}

testOffline();
