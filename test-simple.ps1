Write-Host "Test IA Nowee" -ForegroundColor Blue

$body = '{"message":"Salut Nowee ! Aide moi pour demenager","phone":"+221701234567","context":"test"}'

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/api/chat/ai" -Method POST -Body $body -ContentType "application/json"
    Write-Host "Reponse recue:" -ForegroundColor Green
    Write-Host $response.Content -ForegroundColor Cyan
} catch {
    Write-Host "Erreur:" $_.Exception.Message -ForegroundColor Red
}
