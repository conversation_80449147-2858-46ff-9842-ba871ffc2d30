// nowee-whatsapp-bot.js
// Bot WhatsApp MVP pour Nowee - Application d'entraide locale
// ----------------------------------------------------
// Prérequis : Node.js >=18, compte Twilio, compte OpenAI
// Déploiement local : `npm install` puis `npm start`
// Variables d'environnement à définir dans un fichier .env
// ----------------------------------------------------

import 'dotenv/config';
import express from 'express';
import bodyParser from 'body-parser';
import twilio from 'twilio';
import OpenAI from 'openai';
import cors from 'cors';
import helmet from 'helmet';

const app = express();

// Middleware de sécurité
app.use(helmet());
app.use(cors());
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

// Init OpenAI avec la nouvelle API
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Stockage temporaire des conversations (en production, utiliser une vraie DB)
const userSessions = new Map();

// Fonction pour analyser le contexte géographique
function extractLocationContext(message) {
  const locationKeywords = {
    'dakar': 'Dakar, Sénégal',
    'paris': 'Paris, France',
    'abidjan': 'Abidjan, Côte d\'Ivoire',
    'casablanca': 'Casablanca, Maroc',
    'tunis': 'Tunis, Tunisie'
  };
  
  const lowerMsg = message.toLowerCase();
  for (const [keyword, location] of Object.entries(locationKeywords)) {
    if (lowerMsg.includes(keyword)) {
      return location;
    }
  }
  return null;
}

// Fonction pour générer une réponse Nowee intelligente
async function generateNoweeResponse(userMessage, userPhone, location = null) {
  const contextualPrompt = `Tu es Nowee, l'IA d'entraide locale qui connecte les gens pour résoudre leurs besoins immédiats.

MISSION: Transformer le besoin exprimé en solution concrète et actionnable.

CONTEXTE:
- Utilisateur: ${userPhone}
- Localisation détectée: ${location || 'Non spécifiée'}
- Message: "${userMessage}"

INSTRUCTIONS:
1. Identifie le type de besoin (matériel, service, conseil, urgence)
2. Propose une solution pratique et immédiate
3. Si possible, suggère une connexion avec la communauté locale
4. Reste bienveillant, concret et optimiste
5. Maximum 3 phrases courtes
6. Utilise des emojis appropriés

EXEMPLES DE RÉPONSES:
- Besoin matériel: "🔧 Je vais chercher qui a une perceuse près de chez toi ! En attendant, as-tu vérifié chez tes voisins directs ?"
- Besoin conseil: "💡 Excellente question ! Je peux te connecter avec quelqu'un d'expérimenté dans ce domaine."
- Urgence: "🚨 Situation comprise. Voici ce que tu peux faire immédiatement..."

Réponds maintenant:`;

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [{ role: 'user', content: contextualPrompt }],
      max_tokens: 150,
      temperature: 0.8,
    });
    
    return completion.choices[0].message.content.trim();
  } catch (error) {
    console.error('Erreur OpenAI:', error);
    return "🤖 Désolé, j'ai un petit problème technique. Peux-tu reformuler ton besoin ?";
  }
}

// Route principale - Webhook WhatsApp
app.post('/webhook', async (req, res) => {
  try {
    const incomingMsg = (req.body.Body || '').trim();
    const from = req.body.From; // ex: "whatsapp:+221771234567"
    const userPhone = from.replace('whatsapp:', '');

    console.log(`📱 Message reçu de ${userPhone}: "${incomingMsg}"`);

    // Message d'accueil pour nouveaux utilisateurs
    if (!incomingMsg || incomingMsg.toLowerCase().includes('bonjour') || incomingMsg.toLowerCase().includes('salut')) {
      const welcomeMsg = `👋 Salut ! Je suis Nowee, ton assistant d'entraide locale.

🎯 Dis-moi simplement ton besoin et je t'aide à le résoudre immédiatement !

Exemples:
• "J'ai besoin d'une perceuse à Dakar"
• "Qui peut m'aider à déménager ?"
• "Je cherche un plombier urgent"

Vas-y, je t'écoute ! 😊`;

      const twiml = new twilio.twiml.MessagingResponse();
      twiml.message(welcomeMsg);
      return res.type('text/xml').send(twiml.toString());
    }

    // Extraction du contexte géographique
    const detectedLocation = extractLocationContext(incomingMsg);
    
    // Sauvegarde de la session utilisateur
    if (!userSessions.has(userPhone)) {
      userSessions.set(userPhone, {
        firstContact: new Date(),
        messageCount: 0,
        location: detectedLocation
      });
    }
    
    const session = userSessions.get(userPhone);
    session.messageCount++;
    if (detectedLocation) session.location = detectedLocation;

    // Génération de la réponse IA
    const aiResponse = await generateNoweeResponse(incomingMsg, userPhone, session.location);

    // Log pour monitoring
    console.log(`🤖 Réponse envoyée à ${userPhone}: "${aiResponse}"`);

    // Réponse WhatsApp via TwiML
    const twiml = new twilio.twiml.MessagingResponse();
    twiml.message(aiResponse);
    res.type('text/xml').send(twiml.toString());

  } catch (error) {
    console.error('Erreur webhook:', error);
    const twiml = new twilio.twiml.MessagingResponse();
    twiml.message("😅 Oups ! J'ai eu un petit problème. Peux-tu réessayer ?");
    res.type('text/xml').send(twiml.toString());
  }
});

// Route de santé pour monitoring
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    service: 'Nowee WhatsApp Bot',
    timestamp: new Date().toISOString(),
    activeUsers: userSessions.size
  });
});

// Route pour statistiques basiques
app.get('/stats', (req, res) => {
  const stats = {
    totalUsers: userSessions.size,
    totalMessages: Array.from(userSessions.values()).reduce((sum, session) => sum + session.messageCount, 0),
    uptime: process.uptime()
  };
  res.json(stats);
});

// Gestion des erreurs
app.use((error, req, res, next) => {
  console.error('Erreur serveur:', error);
  res.status(500).json({ error: 'Erreur interne du serveur' });
});

// Lancement du serveur
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 Nowee Bot démarré sur le port ${PORT}`);
  console.log(`📱 Webhook WhatsApp: http://localhost:${PORT}/webhook`);
  console.log(`💚 Santé du service: http://localhost:${PORT}/health`);
});

export default app;
