// nowee-whatsapp-bot.js
// Bot WhatsApp avancé pour Nowee - Application d'entraide locale
// ----------------------------------------------------
// Prérequis : Node.js >=18, compte Twilio, compte OpenAI
// Déploiement local : `npm install` puis `npm start`
// Variables d'environnement à définir dans un fichier .env
// ----------------------------------------------------

import 'dotenv/config';
import express from 'express';
import bodyParser from 'body-parser';
import twilio from 'twilio';
import OpenAI from 'openai';
import cors from 'cors';
import helmet from 'helmet';
import { rateLimit } from 'express-rate-limit';

// Import des services Nowee
import { analyzeMessage, generateContextualPrompt } from '../ai/promptEngine.js';
import userService from '../services/userService.js';
import { extractLocationFromMessage } from '../utils/locationUtils.js';

const app = express();

// Middleware de sécurité
app.use(helmet());
app.use(cors());
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

// Rate limiting pour éviter les abus
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limite chaque IP à 100 requêtes par fenêtre
  standardHeaders: true,
  legacyHeaders: false,
  message: "Trop de requêtes, veuillez réessayer plus tard"
});
app.use('/webhook', apiLimiter);

// Init OpenAI avec la nouvelle API
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Fonction pour générer une réponse Nowee intelligente
async function generateNoweeResponse(userMessage, userPhone) {
  try {
    // Récupérer ou créer le profil et la session utilisateur
    const userProfile = userService.getUserProfile(userPhone);
    const conversationSession = userService.getConversationSession(userPhone);

    // Analyser le message pour extraire le contexte
    const messageAnalysis = analyzeMessage(userMessage, userProfile);

    // Mettre à jour le profil avec les nouvelles informations
    if (messageAnalysis.location) {
      userService.updateUserProfile(userPhone, { location: messageAnalysis.location });
    }

    // Générer un prompt contextuel pour l'IA
    const contextualPrompt = generateContextualPrompt(messageAnalysis, conversationSession);

    // Appeler l'API OpenAI avec le prompt optimisé
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [{ role: 'user', content: contextualPrompt }],
      max_tokens: 150,
      temperature: 0.8,
    });

    const aiResponse = completion.choices[0].message.content.trim();

    // Enregistrer l'interaction dans l'historique
    userService.addToConversationHistory(userPhone, userMessage, aiResponse, messageAnalysis);

    // Enregistrer le besoin détecté
    if (messageAnalysis.needType) {
      userService.recordUserNeed(userPhone, {
        type: messageAnalysis.needType,
        description: userMessage,
        location: messageAnalysis.location,
        urgency: messageAnalysis.urgency
      });
    }

    return aiResponse;
  } catch (error) {
    console.error('Erreur OpenAI:', error);
    return "🤖 Désolé, j'ai un petit problème technique. Peux-tu reformuler ton besoin ?";
  }
}

// Route principale - Webhook WhatsApp
app.post('/webhook', async (req, res) => {
  try {
    const incomingMsg = (req.body.Body || '').trim();
    const from = req.body.From; // ex: "whatsapp:+221771234567"
    const userPhone = from.replace('whatsapp:', '');

    console.log(`📱 Message reçu de ${userPhone}: "${incomingMsg}"`);

    // Message d'accueil pour nouveaux utilisateurs
    if (!incomingMsg || incomingMsg.toLowerCase().includes('bonjour') || incomingMsg.toLowerCase().includes('salut')) {
      const welcomeMsg = `👋 Salut ! Je suis Nowee, ton assistant d'entraide locale.

🎯 Dis-moi simplement ton besoin et je t'aide à le résoudre immédiatement !

Exemples:
• "J'ai besoin d'une perceuse à Dakar"
• "Qui peut m'aider à déménager ?"
• "Je cherche un plombier urgent"

Vas-y, je t'écoute ! 😊`;

      const twiml = new twilio.twiml.MessagingResponse();
      twiml.message(welcomeMsg);
      return res.type('text/xml').send(twiml.toString());
    }

    // Commandes spéciales
    if (incomingMsg.startsWith('/')) {
      return handleSpecialCommands(incomingMsg, userPhone, res);
    }

    // Génération de la réponse IA
    const aiResponse = await generateNoweeResponse(incomingMsg, userPhone);

    // Log pour monitoring
    console.log(`🤖 Réponse envoyée à ${userPhone}: "${aiResponse}"`);

    // Réponse WhatsApp via TwiML
    const twiml = new twilio.twiml.MessagingResponse();
    twiml.message(aiResponse);
    res.type('text/xml').send(twiml.toString());

  } catch (error) {
    console.error('Erreur webhook:', error);
    const twiml = new twilio.twiml.MessagingResponse();
    twiml.message("😅 Oups ! J'ai eu un petit problème. Peux-tu réessayer ?");
    res.type('text/xml').send(twiml.toString());
  }
});

// Gestion des commandes spéciales
function handleSpecialCommands(command, userPhone, res) {
  const twiml = new twilio.twiml.MessagingResponse();

  switch(command.toLowerCase()) {
    case '/aide':
    case '/help':
      twiml.message(`📚 Commandes disponibles:

/aide - Affiche cette aide
/profil - Voir ton profil
/historique - Voir tes dernières demandes
/stats - Statistiques de la communauté
/effacer - Effacer ton historique

Pour tout besoin, écris simplement ta demande normalement !`);
      break;

    case '/profil':
    case '/profile':
      const profile = userService.getUserProfile(userPhone);
      const location = profile.location ? `${profile.location.city}, ${profile.location.country}` : 'Non définie';

      twiml.message(`👤 Ton profil Nowee:

📱 Téléphone: ${profile.phone}
📍 Localisation: ${location}
📅 Membre depuis: ${profile.createdAt.toLocaleDateString()}
💬 Messages: ${profile.messageCount}
🙋‍♂️ Besoins exprimés: ${profile.needs.length}
🤝 Aide offerte: ${profile.offers.length}
⭐ Réputation: ${profile.reputation.rating}/5`);
      break;

    case '/historique':
    case '/history':
      const history = userService.getConversationHistory(userPhone, 5);

      if (history.length === 0) {
        twiml.message("📝 Tu n'as pas encore d'historique de conversation.");
      } else {
        const historyText = history.map((item, index) =>
          `${index+1}. Toi: "${item.userMessage.substring(0, 30)}${item.userMessage.length > 30 ? '...' : ''}"\n`
        ).join('\n');

        twiml.message(`📝 Tes 5 dernières demandes:\n\n${historyText}`);
      }
      break;

    case '/stats':
      const stats = userService.getGlobalStats();

      twiml.message(`📊 Statistiques Nowee:

👥 Utilisateurs: ${stats.totalUsers}
💬 Messages échangés: ${stats.totalMessages}
🙋‍♂️ Besoins exprimés: ${stats.totalNeeds}
🤝 Offres d'aide: ${stats.totalOffers}
👋 Sessions actives: ${stats.activeSessions}`);
      break;

    case '/effacer':
    case '/clear':
      // Réinitialiser l'historique mais garder le profil
      userService.addToConversationHistory(userPhone, '/effacer', 'Historique effacé', {});

      twiml.message("🧹 Ton historique de conversation a été effacé. Ton profil est toujours conservé.");
      break;

    default:
      twiml.message("❓ Commande non reconnue. Tape /aide pour voir les commandes disponibles.");
  }

  return res.type('text/xml').send(twiml.toString());
}

// Route de santé pour monitoring
app.get('/health', (req, res) => {
  // Nettoyer les sessions inactives périodiquement
  userService.cleanupInactiveSessions(24);

  res.json({
    status: 'OK',
    service: 'Nowee WhatsApp Bot',
    version: '2.0.0',
    timestamp: new Date().toISOString(),
    activeUsers: userService.getGlobalStats().activeSessions,
    uptime: process.uptime()
  });
});

// Route pour statistiques détaillées
app.get('/stats', (req, res) => {
  const stats = userService.getGlobalStats();

  res.json({
    ...stats,
    uptime: process.uptime(),
    version: '2.0.0',
    lastUpdated: new Date().toISOString()
  });
});

// Route pour l'API admin (à protéger en production)
app.get('/admin/users', (req, res) => {
  // Cette route devrait être protégée par authentification
  const apiKey = req.headers['x-api-key'];

  if (apiKey !== process.env.ADMIN_API_KEY) {
    return res.status(401).json({ error: 'Non autorisé' });
  }

  // Retourner la liste des utilisateurs (version simplifiée)
  const users = Array.from(userService.getUserProfiles?.values?.() || [])
    .map(profile => ({
      phone: profile.phone,
      messageCount: profile.messageCount,
      location: profile.location,
      createdAt: profile.createdAt
    }));

  res.json({ users });
});

// Gestion des erreurs
app.use((error, req, res, next) => {
  console.error('Erreur serveur:', error);
  res.status(500).json({ error: 'Erreur interne du serveur' });
});

// Lancement du serveur
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 Nowee Bot v2.0 démarré sur le port ${PORT}`);
  console.log(`📱 Webhook WhatsApp: http://localhost:${PORT}/webhook`);
  console.log(`💚 Santé du service: http://localhost:${PORT}/health`);
  console.log(`📊 Statistiques: http://localhost:${PORT}/stats`);
});

export default app;
