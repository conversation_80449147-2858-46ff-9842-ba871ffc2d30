/**
 * Serveur de production Nowee
 * API complète avec toutes les fonctionnalités économiques
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { createClient } = require('@supabase/supabase-js');
const OpenAI = require('openai');
const twilio = require('twilio');
const winston = require('winston');
require('dotenv').config();

// Configuration
const app = express();
const PORT = process.env.PORT || 3000;

// Logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Clients externes
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

// Middleware de sécurité
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

app.use(compression());
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://nowee-app.vercel.app', 'https://nowee.app']
    : ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_REQUESTS) || 100,
  message: {
    error: 'Trop de requêtes, veuillez réessayer plus tard.',
    retryAfter: '15 minutes'
  }
});

app.use('/api/', limiter);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Middleware de logging
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });
  next();
});

// Routes de santé
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV,
    features: {
      economy: process.env.ENABLE_ECONOMY === 'true',
      barter: process.env.ENABLE_BARTER === 'true',
      voice: process.env.ENABLE_VOICE === 'true',
      maps: process.env.ENABLE_MAPS === 'true',
      notifications: process.env.ENABLE_NOTIFICATIONS === 'true'
    }
  });
});

// API Économique
app.get('/api/economy/wallet/:phone', async (req, res) => {
  try {
    const { phone } = req.params;
    
    const { data, error } = await supabase
      .from('user_wallets')
      .select('*')
      .eq('phone', phone)
      .single();
    
    if (error && error.code !== 'PGRST116') {
      throw error;
    }
    
    const wallet = data || {
      phone,
      nowee_coins: 100,
      time_credits: 0,
      total_earned: 100,
      total_spent: 0,
      reputation_bonus: 0
    };
    
    res.json({ success: true, wallet });
  } catch (error) {
    logger.error('Erreur wallet:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.get('/api/economy/transactions/:phone', async (req, res) => {
  try {
    const { phone } = req.params;
    const limit = parseInt(req.query.limit) || 20;
    
    const { data, error } = await supabase
      .from('transactions')
      .select('*')
      .or(`from_phone.eq.${phone},to_phone.eq.${phone}`)
      .order('created_at', { ascending: false })
      .limit(limit);
    
    if (error) throw error;
    
    const transactions = (data || []).map(tx => ({
      ...tx,
      is_incoming: tx.to_phone === phone
    }));
    
    res.json({ success: true, transactions });
  } catch (error) {
    logger.error('Erreur transactions:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/economy/transfer', async (req, res) => {
  try {
    const { fromPhone, toPhone, amount, description } = req.body;
    
    if (!fromPhone || !toPhone || !amount || amount <= 0) {
      return res.status(400).json({ 
        success: false, 
        error: 'Paramètres invalides' 
      });
    }
    
    // Vérifier le solde
    const { data: fromWallet } = await supabase
      .from('user_wallets')
      .select('nowee_coins')
      .eq('phone', fromPhone)
      .single();
    
    if (!fromWallet || fromWallet.nowee_coins < amount) {
      return res.status(400).json({ 
        success: false, 
        error: 'Solde insuffisant' 
      });
    }
    
    // Effectuer le transfert
    const { error } = await supabase.rpc('transfer_coins', {
      from_phone: fromPhone,
      to_phone: toPhone,
      amount: amount,
      description: description || 'Transfert'
    });
    
    if (error) throw error;
    
    res.json({ success: true, message: 'Transfert effectué avec succès' });
  } catch (error) {
    logger.error('Erreur transfert:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API de Troc
app.get('/api/economy/barter/:phone', async (req, res) => {
  try {
    const { phone } = req.params;
    const type = req.query.type || 'all';
    
    let query = supabase.from('barter_proposals').select('*');
    
    if (type === 'sent') {
      query = query.eq('proposer_phone', phone);
    } else if (type === 'received') {
      query = query.eq('target_phone', phone);
    } else {
      query = query.or(`proposer_phone.eq.${phone},target_phone.eq.${phone}`);
    }
    
    const { data, error } = await query
      .order('created_at', { ascending: false })
      .limit(50);
    
    if (error) throw error;
    
    const proposals = (data || []).map(proposal => ({
      ...proposal,
      is_proposer: proposal.proposer_phone === phone
    }));
    
    res.json({ success: true, proposals });
  } catch (error) {
    logger.error('Erreur barter:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/economy/barter/propose', async (req, res) => {
  try {
    const {
      proposerPhone,
      targetPhone,
      offeredResourceId,
      requestedResourceId,
      exchangeType,
      offeredCoins = 0,
      offeredTimeHours = 0,
      requestedCoins = 0,
      requestedTimeHours = 0,
      proposalMessage
    } = req.body;
    
    const { data, error } = await supabase
      .from('barter_proposals')
      .insert({
        proposer_phone: proposerPhone,
        target_phone: targetPhone,
        offered_resource_id: offeredResourceId,
        requested_resource_id: requestedResourceId,
        exchange_type: exchangeType,
        offered_coins: offeredCoins,
        offered_time_hours: offeredTimeHours,
        requested_coins: requestedCoins,
        requested_time_hours: requestedTimeHours,
        proposal_message: proposalMessage,
        status: 'PENDING',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      })
      .select()
      .single();
    
    if (error) throw error;
    
    res.json({ success: true, proposal: data });
  } catch (error) {
    logger.error('Erreur création proposition:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API WhatsApp
app.post('/api/whatsapp/webhook', async (req, res) => {
  try {
    const { Body, From } = req.body;
    
    if (!Body || !From) {
      return res.status(400).json({ error: 'Message invalide' });
    }
    
    // Traitement IA du message
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "Tu es Nowee, un assistant d'entraide locale au Sénégal. Aide les utilisateurs à exprimer leurs besoins et trouve des solutions locales. Réponds en français, sois chaleureux et utilise des expressions sénégalaises quand approprié."
        },
        {
          role: "user",
          content: Body
        }
      ],
      max_tokens: 300,
      temperature: 0.7
    });
    
    const response = completion.choices[0].message.content;
    
    // Envoyer la réponse via Twilio
    await twilioClient.messages.create({
      body: response,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: From
    });
    
    // Enregistrer l'interaction
    await supabase.from('chat_messages').insert({
      phone: From,
      message: Body,
      response: response,
      platform: 'whatsapp'
    });
    
    res.json({ success: true });
  } catch (error) {
    logger.error('Erreur WhatsApp:', error);
    res.status(500).json({ error: error.message });
  }
});

// API Besoins
app.get('/api/needs', async (req, res) => {
  try {
    const { lat, lng, radius = 10, category } = req.query;
    
    let query = supabase
      .from('needs')
      .select(`
        *,
        user_profiles(name, phone, rating)
      `)
      .eq('status', 'active')
      .order('created_at', { ascending: false });
    
    if (category) {
      query = query.eq('category', category);
    }
    
    const { data, error } = await query.limit(50);
    
    if (error) throw error;
    
    res.json({ success: true, needs: data || [] });
  } catch (error) {
    logger.error('Erreur needs:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/needs', async (req, res) => {
  try {
    const {
      phone,
      title,
      description,
      category,
      urgency,
      latitude,
      longitude,
      address
    } = req.body;
    
    const { data, error } = await supabase
      .from('needs')
      .insert({
        phone,
        title,
        description,
        category,
        urgency: urgency || 'medium',
        latitude,
        longitude,
        address,
        status: 'active'
      })
      .select()
      .single();
    
    if (error) throw error;
    
    res.json({ success: true, need: data });
  } catch (error) {
    logger.error('Erreur création need:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Gestion des erreurs
app.use((error, req, res, next) => {
  logger.error('Erreur serveur:', error);
  res.status(500).json({
    success: false,
    error: process.env.NODE_ENV === 'production' 
      ? 'Erreur interne du serveur' 
      : error.message
  });
});

// Route 404
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route non trouvée',
    path: req.originalUrl
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  logger.info(`🚀 Serveur Nowee démarré sur le port ${PORT}`, {
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString()
  });
});

// Gestion gracieuse de l'arrêt
process.on('SIGTERM', () => {
  logger.info('SIGTERM reçu, arrêt gracieux du serveur');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT reçu, arrêt gracieux du serveur');
  process.exit(0);
});

module.exports = app;
