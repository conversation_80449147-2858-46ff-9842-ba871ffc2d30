# Dockerfile pour Nowee Bot
# Support multi-stage pour optimiser la taille de l'image

# Stage 1: Build
FROM node:18-alpine AS builder

WORKDIR /app

# Copier les fichiers de configuration
COPY package*.json ./
COPY prisma ./prisma/

# Installer les dépendances
RUN npm ci --only=production && npm cache clean --force

# Générer le client Prisma
RUN npx prisma generate

# Stage 2: Production
FROM node:18-alpine AS production

# Installer dumb-init pour une gestion propre des signaux
RUN apk add --no-cache dumb-init

# Créer un utilisateur non-root
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nowee -u 1001

WORKDIR /app

# Copier les dépendances depuis le stage builder
COPY --from=builder --chown=nowee:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nowee:nodejs /app/prisma ./prisma

# Copier le code source
COPY --chown=nowee:nodejs . .

# Exposer le port
EXPOSE 3000

# Changer vers l'utilisateur non-root
USER nowee

# Variables d'environnement par défaut
ENV NODE_ENV=production
ENV PORT=3000

# Healthcheck
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Commande de démarrage
ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "start"]
