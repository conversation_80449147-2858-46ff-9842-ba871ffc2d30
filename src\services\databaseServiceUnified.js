/**
 * Service de base de données unifié pour Nowee
 * Utilise Supabase en priorité avec fallback en mémoire
 */

import { SupabaseService } from './supabaseService.js';

// Stockage en mémoire pour le mode fallback
const memoryStorage = {
  users: new Map(),
  resources: new Map(),
  matches: new Map(),
  events: []
};

/**
 * Service de base de données unifié
 */
export class UnifiedDatabaseService {
  
  /**
   * Obtient ou crée un utilisateur
   */
  static async getUserProfile(phone) {
    try {
      if (SupabaseService.isConfigured()) {
        return await SupabaseService.getOrCreateUser(phone);
      }
      
      // Fallback en mémoire
      return this.getUserProfileFallback(phone);
      
    } catch (error) {
      console.error('Erreur getUserProfile:', error);
      return this.getUserProfileFallback(phone);
    }
  }
  
  /**
   * Fallback pour getUserProfile
   */
  static getUserProfileFallback(phone) {
    let user = memoryStorage.users.get(phone);
    if (!user) {
      user = {
        id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        phone,
        name: null,
        location: null,
        coordinates: null,
        city: null,
        country: 'Sénégal',
        rating: 0,
        help_given: 0,
        help_received: 0,
        trust_score: 100,
        preferences: {},
        languages: ['fr'],
        created_at: new Date(),
        updated_at: new Date(),
        last_active_at: new Date(),
        is_active: true
      };
      memoryStorage.users.set(phone, user);
    } else {
      user.last_active_at = new Date();
    }
    return user;
  }
  
  /**
   * Enregistre un besoin
   */
  static async recordUserNeed(phone, needData) {
    try {
      const user = await this.getUserProfile(phone);
      
      if (SupabaseService.isConfigured()) {
        return await SupabaseService.createResource(user.id, {
          type: 'NEED',
          category: this.mapNeedTypeToCategory(needData.type),
          title: needData.description.substring(0, 200),
          description: needData.description,
          tags: this.extractTags(needData.description),
          location: needData.location,
          coordinates: needData.location?.coordinates,
          urgency: needData.urgency || 1,
          radius_km: needData.urgency >= 3 ? 20 : 10 // Plus large pour urgences
        });
      }
      
      // Fallback en mémoire
      return this.recordUserNeedFallback(user, needData);
      
    } catch (error) {
      console.error('Erreur recordUserNeed:', error);
      const user = this.getUserProfileFallback(phone);
      return this.recordUserNeedFallback(user, needData);
    }
  }
  
  /**
   * Fallback pour recordUserNeed
   */
  static recordUserNeedFallback(user, needData) {
    const need = {
      id: `need_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      user_id: user.id,
      type: 'NEED',
      category: this.mapNeedTypeToCategory(needData.type),
      title: needData.description.substring(0, 200),
      description: needData.description,
      tags: this.extractTags(needData.description),
      location: needData.location,
      coordinates: needData.location?.coordinates,
      radius_km: needData.urgency >= 3 ? 20 : 10,
      urgency: needData.urgency || 1,
      status: 'ACTIVE',
      created_at: new Date(),
      updated_at: new Date()
    };
    memoryStorage.resources.set(need.id, need);
    return need;
  }
  
  /**
   * Enregistre une offre
   */
  static async recordUserOffer(phone, offerData) {
    try {
      const user = await this.getUserProfile(phone);
      
      if (SupabaseService.isConfigured()) {
        return await SupabaseService.createResource(user.id, {
          type: 'OFFER',
          category: this.mapNeedTypeToCategory(offerData.type),
          title: offerData.description.substring(0, 200),
          description: offerData.description,
          tags: this.extractTags(offerData.description),
          location: offerData.location,
          coordinates: offerData.location?.coordinates,
          availability: offerData.availability,
          radius_km: 15
        });
      }
      
      // Fallback en mémoire
      return this.recordUserOfferFallback(user, offerData);
      
    } catch (error) {
      console.error('Erreur recordUserOffer:', error);
      const user = this.getUserProfileFallback(phone);
      return this.recordUserOfferFallback(user, offerData);
    }
  }
  
  /**
   * Fallback pour recordUserOffer
   */
  static recordUserOfferFallback(user, offerData) {
    const offer = {
      id: `offer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      user_id: user.id,
      type: 'OFFER',
      category: this.mapNeedTypeToCategory(offerData.type),
      title: offerData.description.substring(0, 200),
      description: offerData.description,
      tags: this.extractTags(offerData.description),
      location: offerData.location,
      coordinates: offerData.location?.coordinates,
      availability: offerData.availability,
      radius_km: 15,
      status: 'ACTIVE',
      created_at: new Date(),
      updated_at: new Date()
    };
    memoryStorage.resources.set(offer.id, offer);
    return offer;
  }
  
  /**
   * Trouve des correspondances pour une ressource
   */
  static async findMatches(resourceId, maxDistance = 10) {
    try {
      if (SupabaseService.isConfigured()) {
        return await SupabaseService.findMatches(resourceId, maxDistance);
      }
      
      // Fallback en mémoire
      return this.findMatchesFallback(resourceId, maxDistance);
      
    } catch (error) {
      console.error('Erreur findMatches:', error);
      return this.findMatchesFallback(resourceId, maxDistance);
    }
  }
  
  /**
   * Fallback pour findMatches
   */
  static findMatchesFallback(resourceId, maxDistance) {
    const sourceResource = memoryStorage.resources.get(resourceId);
    if (!sourceResource) return [];
    
    const targetType = sourceResource.type === 'NEED' ? 'OFFER' : 'NEED';
    const matches = [];
    
    for (const [id, resource] of memoryStorage.resources) {
      if (resource.type === targetType && 
          resource.category === sourceResource.category &&
          resource.user_id !== sourceResource.user_id &&
          resource.status === 'ACTIVE') {
        
        const distance = this.calculateDistanceFallback(
          sourceResource.coordinates,
          resource.coordinates
        );
        
        if (!distance || distance <= maxDistance) {
          matches.push({
            ...resource,
            match_score: this.calculateMatchScoreFallback(sourceResource, resource),
            distance_km: distance
          });
        }
      }
    }
    
    return matches.sort((a, b) => b.match_score - a.match_score);
  }
  
  /**
   * Obtient les statistiques globales
   */
  static async getGlobalStats() {
    try {
      if (SupabaseService.isConfigured()) {
        return await SupabaseService.getGlobalStats();
      }
      
      // Fallback en mémoire
      const needs = Array.from(memoryStorage.resources.values()).filter(r => r.type === 'NEED');
      const offers = Array.from(memoryStorage.resources.values()).filter(r => r.type === 'OFFER');
      
      return {
        totalUsers: memoryStorage.users.size,
        totalResources: memoryStorage.resources.size,
        totalNeeds: needs.length,
        totalOffers: offers.length,
        totalMatches: memoryStorage.matches.size,
        activeUsers: memoryStorage.users.size
      };
      
    } catch (error) {
      console.error('Erreur getGlobalStats:', error);
      return {
        totalUsers: memoryStorage.users.size,
        totalResources: memoryStorage.resources.size,
        totalMatches: memoryStorage.matches.size,
        activeUsers: memoryStorage.users.size
      };
    }
  }
  
  /**
   * Enregistre un événement système
   */
  static async logEvent(type, userId, data) {
    try {
      const event = {
        id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        event_type: type,
        user_id: userId,
        event_data: data,
        created_at: new Date()
      };
      
      memoryStorage.events.push(event);
      return event;
      
    } catch (error) {
      console.error('Erreur logEvent:', error);
      return null;
    }
  }
  
  /**
   * Mappe les types de besoins aux catégories
   */
  static mapNeedTypeToCategory(needType) {
    const mapping = {
      'MATERIAL': 'MATERIAL',
      'SERVICE': 'SERVICE',
      'TRANSPORT': 'TRANSPORT',
      'EMERGENCY': 'EMERGENCY',
      'EDUCATION': 'EDUCATION',
      'HEALTH': 'HEALTH',
      'FOOD': 'FOOD',
      'HOUSING': 'HOUSING'
    };
    return mapping[needType] || 'GENERAL';
  }
  
  /**
   * Extrait les tags d'une description
   */
  static extractTags(description) {
    const commonWords = ['perceuse', 'voiture', 'aide', 'urgent', 'déménagement', 'plombier', 'électricien'];
    const tags = [];
    const lowerDesc = description.toLowerCase();
    
    commonWords.forEach(word => {
      if (lowerDesc.includes(word)) {
        tags.push(word);
      }
    });
    
    return tags;
  }
  
  /**
   * Calcule le score de matching (fallback)
   */
  static calculateMatchScoreFallback(source, target) {
    let score = 0;
    
    if (source.category === target.category) score += 40;
    if (source.urgency >= 3 || target.urgency >= 3) score += 15;
    
    const commonTags = source.tags?.filter(tag => target.tags?.includes(tag)) || [];
    score += Math.min(commonTags.length * 10, 30);
    
    return Math.min(score, 100);
  }
  
  /**
   * Calcule la distance (fallback)
   */
  static calculateDistanceFallback(coord1, coord2) {
    if (!coord1 || !coord2) return null;
    
    const R = 6371;
    const dLat = (coord2.latitude - coord1.latitude) * Math.PI / 180;
    const dLon = (coord2.longitude - coord1.longitude) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(coord1.latitude * Math.PI / 180) * Math.cos(coord2.latitude * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }
  
  /**
   * Vérifie si Supabase est utilisé
   */
  static isUsingSupabase() {
    return SupabaseService.isConfigured();
  }
  
  /**
   * Obtient les données en mémoire (pour debug)
   */
  static getMemoryStorage() {
    return memoryStorage;
  }
  
  /**
   * Obtient le client Supabase
   */
  static get supabase() {
    return SupabaseService.getClient();
  }
}

// Export par défaut
export const dbService = UnifiedDatabaseService;
export default UnifiedDatabaseService;
