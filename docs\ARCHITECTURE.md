# Architecture de Nowee

## Vue d'ensemble

Nowee est conçu comme une plateforme évolutive qui commence par un bot WhatsApp simple mais qui peut se développer en un écosystème complet d'entraide locale. L'architecture est pensée pour être modulaire, scalable et adaptable à différents contextes culturels et géographiques.

## Architecture actuelle (MVP)

```
                                 ┌─────────────┐
                                 │             │
                                 │  OpenAI API │
                                 │             │
                                 └──────┬──────┘
                                        │
                                        ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│             │    │             │    │             │
│  WhatsApp   │◄───┤   Twilio    │◄───┤  Nowee Bot  │
│             │    │             │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
```

### Composants principaux

1. **Bot WhatsApp (nowee-whatsapp-bot.js)**
   - Point d'entrée principal de l'application
   - Gère les webhooks Twilio
   - Traite les messages entrants
   - Coordonne les réponses

2. **Intégration OpenAI**
   - Analyse contextuelle des messages
   - Génération de réponses intelligentes
   - Extraction d'intentions

3. **Utilitaires**
   - Gestion des localisations
   - Formatage des messages
   - Stockage temporaire des sessions

## Architecture future

```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                     Applications Clients                    │
│                                                             │
│  ┌───────────┐   ┌───────────┐   ┌───────────┐   ┌───────┐  │
│  │ WhatsApp  │   │ App Web   │   │App Mobile │   │  API  │  │
│  └───────────┘   └───────────┘   └───────────┘   └───────┘  │
│                                                             │
└───────────────────────────┬─────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                      API Gateway / BFF                      │
│                                                             │
└───────────────────────────┬─────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                     Services Métiers                        │
│                                                             │
│  ┌───────────┐   ┌───────────┐   ┌───────────┐   ┌───────┐  │
│  │ Service   │   │ Service   │   │ Service   │   │Service│  │
│  │ Besoins   │   │ Matching  │   │ Utilisat. │   │Notifs │  │
│  └───────────┘   └───────────┘   └───────────┘   └───────┘  │
│                                                             │
└───────────────────────────┬─────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                     Services Communs                        │
│                                                             │
│  ┌───────────┐   ┌───────────┐   ┌───────────┐   ┌───────┐  │
│  │ IA        │   │ Géoloc.   │   │ Paiements │   │Sécurité│  │
│  └───────────┘   └───────────┘   └───────────┘   └───────┘  │
│                                                             │
└───────────────────────────┬─────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                     Couche Données                          │
│                                                             │
│  ┌───────────┐   ┌───────────┐   ┌───────────┐   ┌───────┐  │
│  │ Base      │   │ Cache     │   │ File      │   │Message│  │
│  │ Principale│   │ Redis     │   │ Storage   │   │Queue  │  │
│  └───────────┘   └───────────┘   └───────────┘   └───────┘  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Services métiers prévus

1. **Service de Besoins**
   - Gestion des demandes utilisateurs
   - Catégorisation et priorisation
   - Historique et suivi

2. **Service de Matching**
   - Algorithme d'appariement besoins/ressources
   - Scoring de pertinence
   - Filtres contextuels (distance, urgence, etc.)

3. **Service Utilisateurs**
   - Profils et préférences
   - Système de réputation
   - Authentification et autorisations

4. **Service de Notifications**
   - Alertes push
   - Messages programmés
   - Rappels et suivis

### Services communs

1. **Service IA**
   - Analyse contextuelle
   - Prédiction des besoins
   - Traitement du langage naturel

2. **Service Géolocalisation**
   - Localisation précise
   - Calcul de proximité
   - Zones d'intérêt

3. **Service Paiements**
   - Transactions monétaires
   - Système de troc
   - Monnaie alternative

4. **Service Sécurité**
   - Vérification d'identité
   - Protection des données
   - Détection de fraude

## Choix technologiques

### MVP (Phase actuelle)
- **Backend**: Node.js + Express
- **IA**: OpenAI API
- **Messaging**: Twilio WhatsApp API
- **Stockage**: En mémoire (Map)

### Phase 2-3
- **Backend**: Node.js + Express ou NestJS
- **Frontend Mobile**: React Native
- **Frontend Web**: React + Next.js
- **Base de données**: PostgreSQL + Redis
- **API**: GraphQL + REST
- **Infrastructure**: Serverless (AWS Lambda ou Vercel)
- **Conteneurisation**: Docker + Kubernetes

## Considérations techniques

### Scalabilité
- Architecture en microservices pour scaling horizontal
- Séparation claire des responsabilités
- Cache distribué pour les données fréquemment accédées

### Résilience
- Circuit breakers pour les services externes
- Retry policies pour les opérations critiques
- Monitoring et alerting

### Sécurité
- Chiffrement des données sensibles
- Authentification multi-facteurs
- Audit logs pour toutes les actions critiques

### Offline First
- Synchronisation bidirectionnelle
- Stockage local
- Communication mesh pour zones sans internet

## Modèle de données (Schéma simplifié)

```
User {
  id: UUID
  phone: String
  name: String
  location: GeoPoint
  reputation: Float
  joinedAt: DateTime
  preferences: JSON
}

Need {
  id: UUID
  userId: UUID
  type: Enum(MATERIAL, SERVICE, ADVICE, EMERGENCY)
  description: String
  location: GeoPoint
  urgency: Int
  status: Enum(OPEN, MATCHED, FULFILLED, CANCELLED)
  createdAt: DateTime
  expiresAt: DateTime
}

Resource {
  id: UUID
  userId: UUID
  type: Enum(MATERIAL, SERVICE, SKILL)
  description: String
  availability: JSON
  conditions: String
  createdAt: DateTime
}

Match {
  id: UUID
  needId: UUID
  resourceId: UUID
  status: Enum(PROPOSED, ACCEPTED, COMPLETED, CANCELLED)
  rating: Float
  feedback: String
  createdAt: DateTime
  completedAt: DateTime
}
```

## Évolution et extensibilité

L'architecture est conçue pour permettre:

1. **Intégration de nouveaux canaux**
   - Telegram, Messenger, Signal, etc.
   - Applications natives

2. **Extension géographique**
   - Support multi-langues
   - Adaptation culturelle
   - Données géographiques locales

3. **Nouveaux modes d'interaction**
   - Commandes vocales
   - Interface conversationnelle avancée
   - Réalité augmentée

4. **Écosystème d'extensions**
   - API publique
   - Webhooks personnalisés
   - Intégrations tierces
