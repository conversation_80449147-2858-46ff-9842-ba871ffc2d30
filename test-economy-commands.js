#!/usr/bin/env node

/**
 * Test des commandes économiques Nowee
 */

import 'dotenv/config';
import { dbService } from './src/services/databaseServiceUnified.js';
import { EconomyService } from './src/services/economyService.js';
import { BarterService } from './src/services/barterService.js';

async function testEconomyCommands() {
  console.log('💰 Test des Commandes Économiques Nowee\n');
  
  try {
    const userPhone = '+221771234567';
    
    // 1. Test commande /portefeuille
    console.log('1. Test commande /portefeuille...');
    const user = await dbService.getUserProfile(userPhone);
    const wallet = await EconomyService.getWallet(user.id);
    
    const portefeuilleResponse = `💰 *Votre Portefeuille Nowee*

🪙 *NoweeCoins:* ${wallet.nowee_coins.toFixed(2)}
⏰ *Crédits Temps:* ${wallet.time_credits.toFixed(1)}h

📊 *Statistiques:*
• Total gagné: ${wallet.total_earned.toFixed(2)} coins
• Total dépensé: ${wallet.total_spent.toFixed(2)} coins
• Bonus réputation: ${wallet.reputation_bonus.toFixed(2)} coins

💡 *Astuce:* Aidez d'autres personnes pour gagner plus de NoweeCoins !`;

    console.log('✅ Réponse /portefeuille:');
    console.log(portefeuilleResponse);
    console.log();
    
    // 2. Test commande /troc
    console.log('2. Test commande /troc...');
    const proposals = await BarterService.getUserBarterProposals(user.id);
    
    let trocResponse;
    if (proposals.length === 0) {
      trocResponse = `🔄 *Système de Troc Nowee*

Aucune proposition de troc en cours.

💡 *Comment ça marche:*
• Échangez vos objets contre d'autres objets
• Échangez du temps contre des services
• Utilisez les NoweeCoins comme monnaie d'échange

📝 *Pour proposer un troc:*
Décrivez ce que vous offrez et ce que vous cherchez !`;
    } else {
      trocResponse = `🔄 *Vos Trocs en Cours (${proposals.length})*\n\n`;
      
      proposals.slice(0, 5).forEach((proposal, index) => {
        const statusEmoji = {
          'PENDING': '⏳',
          'ACCEPTED': '✅',
          'REJECTED': '❌',
          'COMPLETED': '🎉',
          'COUNTER': '🔄'
        }[proposal.status] || '❓';
        
        const isProposer = proposal.proposer_id === user.id;
        const direction = isProposer ? 'Proposé à' : 'Reçu de';
        
        trocResponse += `${index + 1}. ${statusEmoji} ${proposal.exchange_type}\n`;
        trocResponse += `   ${direction} quelqu'un\n`;
        if (proposal.offered_coins > 0) {
          trocResponse += `   💰 ${proposal.offered_coins} coins offerts\n`;
        }
        if (proposal.requested_coins > 0) {
          trocResponse += `   💰 ${proposal.requested_coins} coins demandés\n`;
        }
        trocResponse += `\n`;
      });
    }
    
    console.log('✅ Réponse /troc:');
    console.log(trocResponse);
    console.log();
    
    // 3. Test commande /economie
    console.log('3. Test commande /economie...');
    const stats = await EconomyService.getEconomyStats();
    
    const economieResponse = `📊 *Économie Nowee*

🪙 *Monnaie en Circulation:*
${stats.totalCoinsInCirculation.toFixed(0)} NoweeCoins

⏰ *Crédits Temps:*
${stats.totalTimeCredits.toFixed(1)} heures

📈 *Activité:*
• ${stats.totalTransactions} transactions
• ${stats.averageWalletBalance.toFixed(0)} coins/portefeuille en moyenne

💡 *Principe:* Plus vous aidez, plus vous gagnez !`;

    console.log('✅ Réponse /economie:');
    console.log(economieResponse);
    console.log();
    
    // 4. Test de transfert
    console.log('4. Test de transfert de coins...');
    try {
      const user2 = await dbService.getUserProfile('+221772345678');
      const transfer = await EconomyService.transferCoins(
        user.id,
        user2.id,
        10,
        'Test de transfert via commande'
      );
      
      console.log(`✅ Transfert réussi: ${transfer.nowee_coins} coins`);
      
      // Vérifier les nouveaux soldes
      const walletAfter = await EconomyService.getWallet(user.id);
      console.log(`💰 Nouveau solde: ${walletAfter.nowee_coins} coins`);
      
    } catch (error) {
      console.log(`⚠️ Erreur transfert: ${error.message}`);
    }
    
    console.log('\n🎉 Toutes les commandes économiques testées avec succès !');
    
    console.log('\n📱 Commandes WhatsApp disponibles:');
    console.log('• /portefeuille - Voir son portefeuille');
    console.log('• /troc - Gérer ses trocs');
    console.log('• /economie - Statistiques économiques');
    console.log('• /aide - Aide générale');
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

testEconomyCommands();
