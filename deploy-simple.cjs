#!/usr/bin/env node

/**
 * Script de déploiement simplifié pour Nowee en production
 * Version CommonJS qui fonctionne immédiatement
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.bold}${colors.cyan}\n🚀 ${msg}${colors.reset}`),
  step: (msg) => console.log(`${colors.magenta}📋 ${msg}${colors.reset}`)
};

function createProductionConfig() {
  log.title('Création de la configuration de production');
  
  // Configuration .env de production
  const envContent = `# Configuration de production Nowee
# Généré automatiquement le ${new Date().toISOString()}

# Base de données Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key

# API OpenAI
OPENAI_API_KEY=sk-your-openai-key

# Twilio WhatsApp
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
TWILIO_PHONE_NUMBER=+**********

# Configuration serveur
NODE_ENV=production
PORT=3000
JWT_SECRET=nowee-super-secret-key-production-2025

# URLs de production
FRONTEND_URL=https://nowee-app.vercel.app
API_URL=https://nowee-api-prod.herokuapp.com

# Fonctionnalités
ENABLE_ECONOMY=true
ENABLE_BARTER=true
ENABLE_VOICE=true
ENABLE_MAPS=true
ENABLE_NOTIFICATIONS=true

# Monitoring
LOG_LEVEL=info
ENABLE_ANALYTICS=true

# Limites de production
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=900000
MAX_FILE_SIZE=********
`;

  fs.writeFileSync('.env.production', envContent);
  log.success('Fichier .env.production créé');
  
  return true;
}

function createHerokuConfig() {
  log.title('Configuration Heroku');
  
  // Créer le dossier backend s'il n'existe pas
  if (!fs.existsSync('backend')) {
    fs.mkdirSync('backend');
  }
  
  // Package.json optimisé pour Heroku
  const packageJson = {
    "name": "nowee-api-production",
    "version": "1.0.0",
    "description": "API de production pour Nowee",
    "main": "server.js",
    "scripts": {
      "start": "node server.js",
      "dev": "nodemon server.js",
      "build": "echo 'Build completed'",
      "test": "jest"
    },
    "engines": {
      "node": "18.x",
      "npm": "9.x"
    },
    "dependencies": {
      "express": "^4.18.2",
      "cors": "^2.8.5",
      "helmet": "^7.1.0",
      "compression": "^1.7.4",
      "express-rate-limit": "^7.1.5",
      "@supabase/supabase-js": "^2.38.4",
      "openai": "^4.20.1",
      "twilio": "^4.19.0",
      "axios": "^1.6.2",
      "bcryptjs": "^2.4.3",
      "jsonwebtoken": "^9.0.2",
      "socket.io": "^4.7.4",
      "winston": "^3.11.0",
      "dotenv": "^16.3.1"
    },
    "keywords": ["nowee", "entraide", "api", "production"],
    "author": "Nowee Team",
    "license": "MIT"
  };
  
  fs.writeFileSync('backend/package.json', JSON.stringify(packageJson, null, 2));
  log.success('Package.json Heroku créé');
  
  // Procfile pour Heroku
  const procfile = 'web: node server.js';
  fs.writeFileSync('backend/Procfile', procfile);
  log.success('Procfile créé');
  
  // app.json pour Heroku
  const appJson = {
    "name": "nowee-api-production",
    "description": "API de production pour Nowee",
    "keywords": ["nowee", "api", "entraide"],
    "env": {
      "NODE_ENV": { "value": "production" },
      "SUPABASE_URL": { "description": "URL Supabase" },
      "SUPABASE_ANON_KEY": { "description": "Clé Supabase" },
      "OPENAI_API_KEY": { "description": "Clé OpenAI" }
    },
    "formation": { "web": { "quantity": 1, "size": "basic" } },
    "buildpacks": [{ "url": "heroku/nodejs" }]
  };
  
  fs.writeFileSync('backend/app.json', JSON.stringify(appJson, null, 2));
  log.success('Configuration Heroku créée');
  
  return true;
}

function createVercelConfig() {
  log.title('Configuration Vercel');
  
  // Créer le dossier frontend s'il n'existe pas
  if (!fs.existsSync('frontend')) {
    fs.mkdirSync('frontend');
  }
  
  // vercel.json
  const vercelConfig = {
    "version": 2,
    "name": "nowee-frontend",
    "builds": [
      {
        "src": "package.json",
        "use": "@vercel/static-build",
        "config": {
          "distDir": "dist"
        }
      }
    ],
    "routes": [
      {
        "src": "/(.*)",
        "dest": "/index.html"
      }
    ],
    "env": {
      "NODE_ENV": "production",
      "NEXT_PUBLIC_API_URL": "https://nowee-api-prod.herokuapp.com",
      "NEXT_PUBLIC_SUPABASE_URL": "@supabase_url",
      "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key"
    }
  };
  
  fs.writeFileSync('frontend/vercel.json', JSON.stringify(vercelConfig, null, 2));
  log.success('Configuration Vercel créée');
  
  return true;
}

function createDeploymentScripts() {
  log.title('Création des scripts de déploiement');
  
  // Script PowerShell pour Windows
  const deployScript = `# Script de déploiement Nowee pour Windows
Write-Host "🚀 Déploiement Nowee en production" -ForegroundColor Blue

# Vérifier Git
if (!(Get-Command git -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Git non installé" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Git disponible" -ForegroundColor Green

# Instructions pour l'utilisateur
Write-Host ""
Write-Host "📋 ÉTAPES DE DÉPLOIEMENT:" -ForegroundColor Cyan
Write-Host "1. Configurer Supabase (voir database/deploy-supabase.sql)" -ForegroundColor Yellow
Write-Host "2. Installer Heroku CLI: https://devcenter.heroku.com/articles/heroku-cli" -ForegroundColor Yellow
Write-Host "3. Installer Vercel CLI: npm install -g vercel" -ForegroundColor Yellow
Write-Host "4. Exécuter: heroku create nowee-api-prod" -ForegroundColor Yellow
Write-Host "5. Configurer les variables d'environnement" -ForegroundColor Yellow
Write-Host "6. Déployer: git push heroku main" -ForegroundColor Yellow
Write-Host ""
Write-Host "🎯 Configuration terminée ! Suivez les étapes ci-dessus." -ForegroundColor Green
`;
  
  fs.writeFileSync('deploy-windows.ps1', deployScript);
  log.success('Script PowerShell créé');
  
  // Script bash pour Unix
  const bashScript = `#!/bin/bash
echo "🚀 Déploiement Nowee en production"

# Vérifier les outils
command -v git >/dev/null 2>&1 || { echo "❌ Git requis"; exit 1; }
command -v heroku >/dev/null 2>&1 || { echo "❌ Heroku CLI requis"; exit 1; }
command -v vercel >/dev/null 2>&1 || { echo "❌ Vercel CLI requis"; exit 1; }

echo "✅ Outils disponibles"

# Déploiement automatique
echo "📦 Déploiement backend..."
cd backend
heroku create nowee-api-prod
git push heroku main

echo "🌐 Déploiement frontend..."
cd ../frontend
vercel --prod

echo "✅ Déploiement terminé !"
`;
  
  fs.writeFileSync('deploy-unix.sh', bashScript);
  log.success('Script Unix créé');
  
  return true;
}

function createProductionGuide() {
  log.title('Génération du guide de production');
  
  const guide = `# 🚀 Guide de Déploiement Production Nowee

## 📋 Vue d'ensemble

Nowee est maintenant prêt pour le déploiement en production avec :
- ✅ API Backend (Heroku)
- ✅ Frontend Web (Vercel)
- ✅ Base de données (Supabase)
- ✅ Application Mobile (React Native)

## 🏗️ Architecture de Production

\`\`\`
🌐 Frontend (Vercel)
├── Interface Web Responsive
├── PWA Mobile
└── Dashboard Admin

🔗 API Backend (Heroku)
├── Node.js + Express
├── APIs Économiques
├── WebSocket Real-time
└── Authentification JWT

💾 Base de Données (Supabase)
├── PostgreSQL
├── Authentification
├── Storage Fichiers
└── Real-time

🤖 Services Externes
├── OpenAI (IA)
├── Twilio (WhatsApp)
└── Google Maps
\`\`\`

## 🔧 Étapes de Déploiement

### 1. Configuration Supabase
1. Créer un compte sur https://supabase.com
2. Créer un nouveau projet "nowee-production"
3. Aller dans SQL Editor
4. Exécuter le script \`database/deploy-supabase.sql\`
5. Noter l'URL et les clés du projet

### 2. Déploiement Backend (Heroku)
\`\`\`bash
# Installer Heroku CLI
# Windows: https://devcenter.heroku.com/articles/heroku-cli
# macOS: brew install heroku/brew/heroku
# Ubuntu: sudo snap install heroku --classic

# Créer l'application
heroku create nowee-api-prod

# Configurer les variables
heroku config:set NODE_ENV=production
heroku config:set SUPABASE_URL=your-supabase-url
heroku config:set SUPABASE_ANON_KEY=your-anon-key
heroku config:set OPENAI_API_KEY=your-openai-key
heroku config:set TWILIO_ACCOUNT_SID=your-twilio-sid
heroku config:set TWILIO_AUTH_TOKEN=your-twilio-token

# Déployer
cd backend
git init
git add .
git commit -m "Initial deployment"
heroku git:remote -a nowee-api-prod
git push heroku main
\`\`\`

### 3. Déploiement Frontend (Vercel)
\`\`\`bash
# Installer Vercel CLI
npm install -g vercel

# Déployer
cd frontend
vercel

# Configurer les variables d'environnement sur vercel.com
# NEXT_PUBLIC_API_URL=https://nowee-api-prod.herokuapp.com
# NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
\`\`\`

### 4. Configuration Mobile
L'application mobile est prête et peut être :
- Testée via l'interface web
- Buildée pour Android/iOS
- Distribuée via les stores

## 🧪 Tests de Production

### URLs de Test
- **API:** https://nowee-api-prod.herokuapp.com/health
- **Frontend:** https://nowee-app.vercel.app
- **Admin:** https://nowee-app.vercel.app/admin

### Tests Critiques
- [ ] Authentification utilisateur
- [ ] Création de besoins
- [ ] Système économique (NoweeCoins)
- [ ] Troc et échanges
- [ ] Géolocalisation
- [ ] Notifications WhatsApp
- [ ] Interface mobile

## 📊 Monitoring

### Métriques à Surveiller
- Temps de réponse API
- Utilisation base de données
- Erreurs applicatives
- Satisfaction utilisateur

### Logs
\`\`\`bash
# Logs Heroku
heroku logs --tail -a nowee-api-prod

# Logs Vercel
vercel logs
\`\`\`

## 🔒 Sécurité

### Mesures Implémentées
- HTTPS obligatoire
- Rate limiting
- Validation des entrées
- Authentification JWT
- CORS configuré

## 💰 Fonctionnalités Économiques

### NoweeCoins
- Monnaie locale virtuelle
- Transferts entre utilisateurs
- Historique des transactions
- Statistiques économiques

### Système de Troc
- Échanges objets/services/temps
- Propositions intelligentes
- Négociation en temps réel
- Matching automatique

## 📱 Application Mobile

### Fonctionnalités
- Portefeuille visuel
- Carte interactive
- Interface de troc
- Notifications push

### Build Mobile
\`\`\`bash
cd mobile
npm install
npm run android  # ou npm run ios
\`\`\`

## 🎯 Prochaines Étapes

1. **Tests utilisateurs** au Sénégal
2. **Optimisation** des performances
3. **Expansion** vers d'autres pays
4. **Nouvelles fonctionnalités** (blockchain, IA avancée)

## 📞 Support

### Contacts
- **Technique:** <EMAIL>
- **Support:** <EMAIL>

---

**🎉 Nowee est prêt à révolutionner l'entraide locale ! 🎉**

## 🚀 Commandes Rapides

\`\`\`bash
# Déploiement complet
./deploy-windows.ps1  # Windows
./deploy-unix.sh      # macOS/Linux

# Tests
curl https://nowee-api-prod.herokuapp.com/health
open https://nowee-app.vercel.app

# Monitoring
heroku logs --tail -a nowee-api-prod
\`\`\`
`;

  fs.writeFileSync('PRODUCTION-DEPLOYMENT-GUIDE.md', guide);
  log.success('Guide de déploiement créé');
  
  return true;
}

function main() {
  try {
    log.title('🚀 PRÉPARATION DÉPLOIEMENT PRODUCTION NOWEE 🚀');
    
    // Créer tous les fichiers de configuration
    createProductionConfig();
    createHerokuConfig();
    createVercelConfig();
    createDeploymentScripts();
    createProductionGuide();
    
    // Résumé final
    log.title('🎉 Préparation terminée avec succès !');
    
    console.log(`
${colors.bold}${colors.green}✅ NOWEE PRÊT POUR LA PRODUCTION !${colors.reset}

${colors.cyan}📁 FICHIERS CRÉÉS:${colors.reset}
✅ .env.production - Configuration environnement
✅ backend/package.json - Configuration Heroku
✅ backend/Procfile - Processus Heroku
✅ backend/app.json - Métadonnées Heroku
✅ frontend/vercel.json - Configuration Vercel
✅ deploy-windows.ps1 - Script Windows
✅ deploy-unix.sh - Script Unix/macOS
✅ PRODUCTION-DEPLOYMENT-GUIDE.md - Guide complet

${colors.cyan}🚀 PROCHAINES ÉTAPES:${colors.reset}
1. 💾 Configurer Supabase (database/deploy-supabase.sql)
2. 🔧 Installer Heroku CLI
3. 🌐 Installer Vercel CLI
4. 🚀 Exécuter deploy-windows.ps1
5. 🧪 Tester en production

${colors.yellow}📋 URLS DE PRODUCTION:${colors.reset}
• API: https://nowee-api-prod.herokuapp.com
• Frontend: https://nowee-app.vercel.app
• Mobile: Interface web + Apps natives

${colors.green}🎯 Le système Nowee est prêt à révolutionner l'entraide locale !${colors.reset}
`);
    
  } catch (error) {
    log.error(`Erreur: ${error.message}`);
    process.exit(1);
  }
}

main();
