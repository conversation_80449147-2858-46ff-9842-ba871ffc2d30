#!/usr/bin/env node

/**
 * Script de configuration pour l'application mobile Nowee
 * Installation optimisée des dépendances
 */

const { execSync } = require('child_process');
const fs = require('fs');

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.bold}${colors.blue}\n📱 ${msg}${colors.reset}`)
};

function checkNodeVersion() {
  log.title('Vérification de l\'environnement');
  
  try {
    const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
    const majorVersion = parseInt(nodeVersion.replace('v', '').split('.')[0]);
    
    if (majorVersion >= 16) {
      log.success(`Node.js ${nodeVersion} (compatible)`);
      return true;
    } else {
      log.error(`Node.js ${nodeVersion} trop ancien (requis: 16+)`);
      return false;
    }
  } catch (error) {
    log.error('Node.js non installé');
    return false;
  }
}

function installDependencies() {
  log.title('Installation des dépendances');
  
  try {
    // Nettoyer d'abord
    if (fs.existsSync('node_modules')) {
      log.info('Nettoyage des anciennes dépendances...');
      fs.rmSync('node_modules', { recursive: true, force: true });
    }
    
    if (fs.existsSync('package-lock.json')) {
      fs.unlinkSync('package-lock.json');
    }
    
    // Installer avec options optimisées
    log.info('Installation des dépendances React Native...');
    execSync('npm install --legacy-peer-deps --no-audit --no-fund', { 
      stdio: 'inherit',
      timeout: 300000 // 5 minutes
    });
    
    log.success('Dépendances installées avec succès');
    return true;
    
  } catch (error) {
    log.error(`Erreur installation: ${error.message}`);
    
    // Essayer avec yarn si npm échoue
    try {
      log.warning('Tentative avec Yarn...');
      execSync('yarn install --ignore-engines', { stdio: 'inherit' });
      log.success('Dépendances installées avec Yarn');
      return true;
    } catch (yarnError) {
      log.error('Échec avec Yarn également');
      return false;
    }
  }
}

function checkReactNativeCLI() {
  log.title('Vérification React Native CLI');
  
  try {
    execSync('npx react-native --version', { encoding: 'utf8' });
    log.success('React Native CLI disponible');
    return true;
  } catch (error) {
    log.warning('React Native CLI non trouvé');
    
    try {
      log.info('Installation de React Native CLI...');
      execSync('npm install -g @react-native-community/cli', { stdio: 'inherit' });
      log.success('React Native CLI installé');
      return true;
    } catch (installError) {
      log.error('Impossible d\'installer React Native CLI');
      return false;
    }
  }
}

function createBasicConfig() {
  log.title('Création des fichiers de configuration');
  
  // Metro config
  if (!fs.existsSync('metro.config.js')) {
    const metroConfig = `
const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

const defaultConfig = getDefaultConfig(__dirname);

const config = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
};

module.exports = mergeConfig(defaultConfig, config);
`;
    fs.writeFileSync('metro.config.js', metroConfig);
    log.success('metro.config.js créé');
  }
  
  // Babel config
  if (!fs.existsSync('babel.config.js')) {
    const babelConfig = `
module.exports = {
  presets: ['module:metro-react-native-babel-preset'],
  plugins: [
    'react-native-reanimated/plugin',
  ],
};
`;
    fs.writeFileSync('babel.config.js', babelConfig);
    log.success('babel.config.js créé');
  }
  
  // TypeScript config
  if (!fs.existsSync('tsconfig.json')) {
    const tsConfig = {
      "extends": "@react-native/typescript-config/tsconfig.json",
      "compilerOptions": {
        "allowSyntheticDefaultImports": true,
        "esModuleInterop": true,
        "isolatedModules": true,
        "lib": ["es2017"],
        "moduleResolution": "node",
        "noEmit": true,
        "strict": true,
        "target": "esnext",
        "skipLibCheck": true
      },
      "exclude": [
        "node_modules",
        "babel.config.js",
        "metro.config.js",
        "jest.config.js"
      ]
    };
    
    fs.writeFileSync('tsconfig.json', JSON.stringify(tsConfig, null, 2));
    log.success('tsconfig.json créé');
  }
}

function generateStartScript() {
  log.title('Génération du script de démarrage');
  
  const startScript = `#!/usr/bin/env node

/**
 * Script de démarrage rapide pour Nowee Mobile
 */

const { execSync } = require('child_process');

console.log('🚀 Démarrage de Nowee Mobile...');

try {
  // Démarrer Metro bundler
  console.log('📦 Démarrage du Metro bundler...');
  execSync('npx react-native start', { stdio: 'inherit' });
} catch (error) {
  console.error('❌ Erreur démarrage:', error.message);
  console.log('💡 Essayez: npm run android (dans un autre terminal)');
}
`;
  
  fs.writeFileSync('start-app.js', startScript);
  log.success('Script de démarrage créé: start-app.js');
}

function showInstructions() {
  log.title('Instructions de test');
  
  console.log(`
📋 ${colors.bold}COMMENT TESTER L'APPLICATION${colors.reset}

${colors.blue}🔧 Option 1 - Émulateur Android:${colors.reset}
1. Installer Android Studio
2. Créer un AVD (Android Virtual Device)
3. Démarrer l'émulateur
4. npm run android

${colors.blue}📱 Option 2 - Appareil physique:${colors.reset}
1. Activer le mode développeur
2. Activer le débogage USB
3. Connecter l'appareil
4. npm run android

${colors.blue}🧪 Option 3 - Test rapide:${colors.reset}
1. npm start (Metro bundler)
2. Ouvrir l'app sur l'appareil
3. Tester les écrans économiques

${colors.blue}🎯 Fonctionnalités à tester:${colors.reset}
• 💰 Écran Portefeuille (onglet Wallet)
• 🔄 Écran Troc (onglet Barter)  
• 🗺️ Carte interactive (onglet Map)
• 🏠 Accueil avec WalletCard

${colors.green}✅ L'application est prête pour les tests !${colors.reset}
`);
}

async function main() {
  try {
    log.title('Configuration de l\'Application Mobile Nowee');
    
    // Vérifications
    if (!checkNodeVersion()) {
      process.exit(1);
    }
    
    // Installation
    if (!installDependencies()) {
      log.error('Échec de l\'installation des dépendances');
      process.exit(1);
    }
    
    // CLI React Native
    checkReactNativeCLI();
    
    // Configuration
    createBasicConfig();
    
    // Script de démarrage
    generateStartScript();
    
    // Instructions
    showInstructions();
    
    log.success('🎉 Configuration terminée avec succès !');
    log.info('Prochaine étape: Configurer un émulateur Android ou connecter un appareil');
    
  } catch (error) {
    log.error(`Erreur: ${error.message}`);
    process.exit(1);
  }
}

main();
