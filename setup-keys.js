#!/usr/bin/env node

/**
 * Script interactif pour configurer les clés API de Nowee
 * Exécutez avec: node setup-keys.js
 */

import fs from 'fs/promises';
import readline from 'readline';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import { fileURLToPath } from 'url';

const execAsync = promisify(exec);
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Création de l'interface readline
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Fonction pour poser une question et obtenir une réponse
const question = (query) => new Promise((resolve) => rl.question(query, resolve));

// Fonction pour vérifier si un fichier existe
const fileExists = async (filePath) => {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
};

// Fonction pour lire le contenu d'un fichier .env
const readEnvFile = async (filePath) => {
  try {
    return await fs.readFile(filePath, 'utf8');
  } catch (error) {
    console.error(`Erreur lors de la lecture du fichier ${filePath}:`, error.message);
    return '';
  }
};

// Fonction pour écrire dans le fichier .env
const writeEnvFile = async (filePath, content) => {
  try {
    await fs.writeFile(filePath, content, 'utf8');
    return true;
  } catch (error) {
    console.error(`Erreur lors de l'écriture dans le fichier ${filePath}:`, error.message);
    return false;
  }
};

// Fonction pour mettre à jour une variable dans le contenu .env
const updateEnvVar = (content, varName, varValue) => {
  const regex = new RegExp(`^${varName}=.*$`, 'm');
  if (content.match(regex)) {
    return content.replace(regex, `${varName}=${varValue}`);
  } else {
    return `${content}\n${varName}=${varValue}`;
  }
};

// Fonction pour ouvrir un URL dans le navigateur
const openBrowser = async (url) => {
  const platform = process.platform;
  const cmd = platform === 'win32' ? 'start' : platform === 'darwin' ? 'open' : 'xdg-open';
  
  try {
    await execAsync(`${cmd} ${url}`);
    return true;
  } catch (error) {
    console.error(`Erreur lors de l'ouverture du navigateur:`, error.message);
    return false;
  }
};

// Fonction principale
const main = async () => {
  console.log('\n🌟 Assistant de configuration des clés API Nowee 🌟\n');
  
  // Vérifier si le fichier .env existe
  const envPath = path.join(__dirname, '.env');
  const envExamplePath = path.join(__dirname, '.env.example');
  
  if (!await fileExists(envPath)) {
    console.log('⚠️  Fichier .env non trouvé. Création à partir de .env.example...');
    
    if (await fileExists(envExamplePath)) {
      const exampleContent = await readEnvFile(envExamplePath);
      await writeEnvFile(envPath, exampleContent);
      console.log('✅ Fichier .env créé avec succès !');
    } else {
      console.error('❌ Fichier .env.example non trouvé. Impossible de continuer.');
      process.exit(1);
    }
  }
  
  // Lire le contenu actuel du fichier .env
  let envContent = await readEnvFile(envPath);
  
  // Configuration OpenAI
  console.log('\n🤖 Configuration de l\'API OpenAI\n');
  console.log('Pour obtenir une clé API OpenAI, vous devez créer un compte sur https://platform.openai.com');
  
  const openBrowserOpenAI = await question('Voulez-vous ouvrir le site OpenAI dans votre navigateur ? (o/n): ');
  if (openBrowserOpenAI.toLowerCase() === 'o') {
    await openBrowser('https://platform.openai.com/api-keys');
    console.log('⏳ Prenez le temps de créer votre compte et d\'obtenir votre clé API...');
    await question('Appuyez sur Entrée une fois que vous avez votre clé API...');
  }
  
  const openaiKey = await question('Entrez votre clé API OpenAI (commence par sk-): ');
  if (openaiKey) {
    envContent = updateEnvVar(envContent, 'OPENAI_API_KEY', openaiKey);
    console.log('✅ Clé API OpenAI enregistrée !');
  } else {
    console.log('⚠️ Aucune clé API OpenAI fournie. Vous pourrez la configurer plus tard dans le fichier .env');
  }
  
  // Configuration Twilio
  console.log('\n📱 Configuration de l\'API Twilio\n');
  console.log('Pour utiliser WhatsApp, vous avez besoin d\'un compte Twilio et d\'activer le Sandbox WhatsApp.');
  
  const openBrowserTwilio = await question('Voulez-vous ouvrir le site Twilio dans votre navigateur ? (o/n): ');
  if (openBrowserTwilio.toLowerCase() === 'o') {
    await openBrowser('https://www.twilio.com/try-twilio');
    console.log('⏳ Prenez le temps de créer votre compte et d\'obtenir vos identifiants...');
    await question('Appuyez sur Entrée une fois que vous avez vos identifiants Twilio...');
  }
  
  const twilioSid = await question('Entrez votre Twilio Account SID (commence par AC): ');
  if (twilioSid) {
    envContent = updateEnvVar(envContent, 'TWILIO_ACCOUNT_SID', twilioSid);
    console.log('✅ Twilio Account SID enregistré !');
  } else {
    console.log('⚠️ Aucun Twilio Account SID fourni. Vous pourrez le configurer plus tard dans le fichier .env');
  }
  
  const twilioToken = await question('Entrez votre Twilio Auth Token: ');
  if (twilioToken) {
    envContent = updateEnvVar(envContent, 'TWILIO_AUTH_TOKEN', twilioToken);
    console.log('✅ Twilio Auth Token enregistré !');
  } else {
    console.log('⚠️ Aucun Twilio Auth Token fourni. Vous pourrez le configurer plus tard dans le fichier .env');
  }
  
  const twilioNumber = await question('Entrez votre numéro WhatsApp Twilio (par défaut: whatsapp:+***********): ');
  if (twilioNumber) {
    envContent = updateEnvVar(envContent, 'TWILIO_WHATSAPP_NUMBER', twilioNumber.startsWith('whatsapp:') ? twilioNumber : `whatsapp:${twilioNumber}`);
    console.log('✅ Numéro WhatsApp Twilio enregistré !');
  } else {
    console.log('ℹ️ Utilisation du numéro WhatsApp Sandbox par défaut (whatsapp:+***********)');
  }
  
  // Enregistrer les modifications
  if (await writeEnvFile(envPath, envContent)) {
    console.log('\n🎉 Configuration terminée avec succès !\n');
    console.log('Vos clés API ont été enregistrées dans le fichier .env');
    console.log('\nPour démarrer le bot Nowee, exécutez:');
    console.log('npm start');
    
    console.log('\nPour exposer votre webhook en local, exécutez dans un autre terminal:');
    console.log('npx ngrok http 3000');
    
    console.log('\nPour plus d\'informations sur la configuration, consultez:');
    console.log('docs/SETUP_API_KEYS.md');
  } else {
    console.error('\n❌ Erreur lors de l\'enregistrement de la configuration.');
  }
  
  rl.close();
};

// Exécuter la fonction principale
main().catch(error => {
  console.error('Erreur:', error);
  rl.close();
});
