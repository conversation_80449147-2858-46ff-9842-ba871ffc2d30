-- Correction de l'erreur timestamp dans Supabase
-- À exécuter dans la console SQL Supabase

-- 1. Corriger la fonction update_wallet_balance
CREATE OR REPLACE FUNCTION update_wallet_balance(
  p_user_id UUID,
  p_coins_delta DECIMAL,
  p_time_delta DECIMAL DEFAULT 0
)
RETURNS BOOLEAN AS $$
DECLARE
  current_coins DECIMAL;
  current_time DECIMAL;
BEGIN
  -- Récupérer les soldes actuels
  SELECT nowee_coins, time_credits 
  INTO current_coins, current_time
  FROM wallets 
  WHERE user_id = p_user_id;
  
  -- Si le portefeuille n'existe pas, le créer
  IF NOT FOUND THEN
    INSERT INTO wallets (user_id, nowee_coins, time_credits)
    VALUES (p_user_id, GREATEST(0, p_coins_delta), GREATEST(0, p_time_delta));
    RETURN TRUE;
  END IF;
  
  -- Vérifier si l'utilisateur a suffisamment de fonds
  IF current_coins + p_coins_delta < 0 THEN
    RETURN FALSE;
  END IF;
  
  IF current_time + p_time_delta < 0 THEN
    RETURN FALSE;
  END IF;
  
  -- Mettre à jour le portefeuille (CORRECTION: utiliser NOW() au lieu d'addition)
  UPDATE wallets 
  SET 
    nowee_coins = nowee_coins + p_coins_delta,
    time_credits = time_credits + p_time_delta,
    total_earned = total_earned + GREATEST(0, p_coins_delta),
    total_spent = total_spent + GREATEST(0, -p_coins_delta),
    updated_at = NOW()  -- CORRECTION: Utiliser NOW() directement
  WHERE user_id = p_user_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 2. Corriger les triggers qui pourraient causer des problèmes
DROP TRIGGER IF EXISTS update_wallets_updated_at ON wallets;
DROP TRIGGER IF EXISTS update_transactions_updated_at ON transactions;
DROP TRIGGER IF EXISTS update_barter_proposals_updated_at ON barter_proposals;

-- Recréer les triggers avec la bonne fonction
CREATE TRIGGER update_wallets_updated_at BEFORE UPDATE ON wallets
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_barter_proposals_updated_at BEFORE UPDATE ON barter_proposals
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 3. Vérifier que la fonction update_updated_at_column existe et fonctionne
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 4. Test de la fonction corrigée
DO $$
DECLARE
  test_user_id UUID;
  result BOOLEAN;
BEGIN
  -- Prendre un utilisateur existant pour le test
  SELECT id INTO test_user_id FROM users LIMIT 1;
  
  IF test_user_id IS NOT NULL THEN
    -- Tester la fonction
    SELECT update_wallet_balance(test_user_id, 1.0, 0.0) INTO result;
    
    IF result THEN
      RAISE NOTICE '✅ Fonction update_wallet_balance corrigée et testée avec succès';
    ELSE
      RAISE NOTICE '❌ Erreur lors du test de la fonction';
    END IF;
  ELSE
    RAISE NOTICE '⚠️ Aucun utilisateur trouvé pour le test';
  END IF;
END $$;

-- 5. Optimisations pour la production
-- Index supplémentaires pour les performances
CREATE INDEX IF NOT EXISTS idx_wallets_updated_at ON wallets(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_barter_proposals_created_at ON barter_proposals(created_at DESC);

-- 6. Fonction de nettoyage des données expirées
CREATE OR REPLACE FUNCTION cleanup_expired_data()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER := 0;
BEGIN
  -- Supprimer les propositions de troc expirées depuis plus de 30 jours
  DELETE FROM barter_proposals 
  WHERE status = 'REJECTED' 
    AND updated_at < NOW() - INTERVAL '30 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Message de confirmation
DO $$
BEGIN
  RAISE NOTICE '🎉 Corrections timestamp appliquées avec succès !';
  RAISE NOTICE '✅ Fonction update_wallet_balance corrigée';
  RAISE NOTICE '✅ Triggers recréés';
  RAISE NOTICE '✅ Index de performance ajoutés';
  RAISE NOTICE '✅ Fonction de nettoyage créée';
  RAISE NOTICE '🚀 Système prêt pour la production !';
END $$;
