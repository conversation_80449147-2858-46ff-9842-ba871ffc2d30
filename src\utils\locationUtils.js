/**
 * Utilitaires pour la gestion des localisations dans Nowee
 */

// Base de données simplifiée des villes principales (à remplacer par une vraie API géographique)
const CITIES_DATABASE = {
  // Afrique
  'dakar': { country: 'Sénégal', continent: 'Afrique', lat: 14.7167, lng: -17.4677 },
  'abidjan': { country: 'Côte d\'Ivoire', continent: 'Afrique', lat: 5.3600, lng: -4.0083 },
  'casablanca': { country: 'Maroc', continent: 'Afrique', lat: 33.5731, lng: -7.5898 },
  'tunis': { country: 'Tunisie', continent: 'Afrique', lat: 36.8065, lng: 10.1815 },
  'alger': { country: 'Algérie', continent: 'Afrique', lat: 36.7538, lng: 3.0588 },
  'lagos': { country: 'Nigeria', continent: 'Afrique', lat: 6.5244, lng: 3.3792 },
  'nairobi': { country: 'Kenya', continent: 'Afrique', lat: -1.2921, lng: 36.8219 },
  
  // Europe
  'paris': { country: 'France', continent: 'Europe', lat: 48.8566, lng: 2.3522 },
  'marseille': { country: 'France', continent: 'Europe', lat: 43.2965, lng: 5.3698 },
  'lyon': { country: 'France', continent: 'Europe', lat: 45.7640, lng: 4.8357 },
  'bruxelles': { country: 'Belgique', continent: 'Europe', lat: 50.8503, lng: 4.3517 },
  'geneve': { country: 'Suisse', continent: 'Europe', lat: 46.2044, lng: 6.1432 },
  
  // Amérique
  'montreal': { country: 'Canada', continent: 'Amérique', lat: 45.5017, lng: -73.5673 },
  'quebec': { country: 'Canada', continent: 'Amérique', lat: 46.8139, lng: -71.2080 },
  'new york': { country: 'États-Unis', continent: 'Amérique', lat: 40.7128, lng: -74.0060 },
  
  // Asie
  'tokyo': { country: 'Japon', continent: 'Asie', lat: 35.6762, lng: 139.6503 },
  'shanghai': { country: 'Chine', continent: 'Asie', lat: 31.2304, lng: 121.4737 },
  'dubai': { country: 'Émirats arabes unis', continent: 'Asie', lat: 25.2048, lng: 55.2708 },
};

/**
 * Extrait les informations de localisation d'un message
 * @param {string} message - Le message de l'utilisateur
 * @returns {Object|null} - Informations de localisation ou null si non trouvé
 */
export function extractLocationFromMessage(message) {
  if (!message) return null;
  
  const lowerMsg = message.toLowerCase();
  
  // Recherche des noms de villes dans le message
  for (const [cityName, cityInfo] of Object.entries(CITIES_DATABASE)) {
    if (lowerMsg.includes(cityName)) {
      return {
        city: cityName.charAt(0).toUpperCase() + cityName.slice(1),
        country: cityInfo.country,
        continent: cityInfo.continent,
        coordinates: {
          latitude: cityInfo.lat,
          longitude: cityInfo.lng
        }
      };
    }
  }
  
  return null;
}

/**
 * Formate une localisation pour affichage
 * @param {Object} location - Objet de localisation
 * @returns {string} - Chaîne formatée
 */
export function formatLocation(location) {
  if (!location) return 'Localisation inconnue';
  
  return `${location.city}, ${location.country}`;
}

/**
 * Calcule la distance approximative entre deux points géographiques (formule de Haversine)
 * @param {number} lat1 - Latitude du point 1
 * @param {number} lon1 - Longitude du point 1
 * @param {number} lat2 - Latitude du point 2
 * @param {number} lon2 - Longitude du point 2
 * @returns {number} - Distance en kilomètres
 */
export function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Rayon de la Terre en km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance en km
  
  return Math.round(distance * 10) / 10; // Arrondi à 1 décimale
}

export default {
  extractLocationFromMessage,
  formatLocation,
  calculateDistance,
  CITIES_DATABASE
};
