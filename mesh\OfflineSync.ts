/**
 * Système de Synchronisation Offline Nowee
 * Gestion du cache et synchronisation différée pour le mesh networking
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { EventEmitter } from 'events';

// Types pour la synchronisation
export interface SyncData {
  id: string;
  type: 'TRANSACTION' | 'HELP_REQUEST' | 'HELP_OFFER' | 'USER_PROFILE' | 'REPUTATION';
  data: any;
  timestamp: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  attempts: number;
  lastAttempt?: number;
  status: 'pending' | 'syncing' | 'synced' | 'failed';
  dependencies?: string[]; // IDs d'autres éléments requis
}

export interface ConflictResolution {
  id: string;
  localData: any;
  remoteData: any;
  resolution: 'local' | 'remote' | 'merge' | 'manual';
  mergedData?: any;
}

export interface SyncStats {
  totalPending: number;
  totalSynced: number;
  totalFailed: number;
  lastSyncTime: number;
  networkStatus: 'online' | 'mesh' | 'offline';
  syncInProgress: boolean;
}

export class OfflineSyncManager extends EventEmitter {
  private syncQueue: Map<string, SyncData> = new Map();
  private conflictQueue: Map<string, ConflictResolution> = new Map();
  private isOnline: boolean = false;
  private isMeshConnected: boolean = false;
  private syncInProgress: boolean = false;
  private retryIntervals: Map<string, NodeJS.Timeout> = new Map();
  
  // Configuration
  private readonly MAX_RETRY_ATTEMPTS = 5;
  private readonly RETRY_DELAYS = [1000, 5000, 15000, 60000, 300000]; // 1s, 5s, 15s, 1m, 5m
  private readonly SYNC_BATCH_SIZE = 10;
  private readonly STORAGE_KEY_PREFIX = 'nowee_offline_';

  constructor() {
    super();
    this.loadFromStorage();
    this.startPeriodicSync();
  }

  // Ajouter des données à synchroniser
  async addToSyncQueue(data: Omit<SyncData, 'id' | 'attempts' | 'status'>): Promise<string> {
    const syncItem: SyncData = {
      ...data,
      id: this.generateSyncId(),
      attempts: 0,
      status: 'pending'
    };

    this.syncQueue.set(syncItem.id, syncItem);
    await this.saveToStorage();

    this.emit('itemQueued', syncItem);

    // Essayer de synchroniser immédiatement si possible
    if (this.canSync()) {
      this.processSyncQueue();
    }

    return syncItem.id;
  }

  // Traitement de la queue de synchronisation
  private async processSyncQueue(): Promise<void> {
    if (this.syncInProgress || !this.canSync()) {
      return;
    }

    this.syncInProgress = true;
    this.emit('syncStarted');

    try {
      const pendingItems = Array.from(this.syncQueue.values())
        .filter(item => item.status === 'pending')
        .sort((a, b) => {
          // Priorité par urgence puis par timestamp
          const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
          const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
          return priorityDiff !== 0 ? priorityDiff : a.timestamp - b.timestamp;
        })
        .slice(0, this.SYNC_BATCH_SIZE);

      for (const item of pendingItems) {
        await this.syncItem(item);
      }

    } catch (error) {
      console.error('Erreur lors de la synchronisation:', error);
      this.emit('syncError', error);
    } finally {
      this.syncInProgress = false;
      this.emit('syncCompleted');
      await this.saveToStorage();
    }
  }

  // Synchroniser un élément individuel
  private async syncItem(item: SyncData): Promise<void> {
    try {
      item.status = 'syncing';
      item.attempts++;
      item.lastAttempt = Date.now();

      this.emit('itemSyncing', item);

      // Vérifier les dépendances
      if (item.dependencies && !this.areDependenciesSynced(item.dependencies)) {
        throw new Error('Dépendances non synchronisées');
      }

      // Synchroniser selon le type
      let result: any;
      switch (item.type) {
        case 'TRANSACTION':
          result = await this.syncTransaction(item.data);
          break;
        case 'HELP_REQUEST':
          result = await this.syncHelpRequest(item.data);
          break;
        case 'HELP_OFFER':
          result = await this.syncHelpOffer(item.data);
          break;
        case 'USER_PROFILE':
          result = await this.syncUserProfile(item.data);
          break;
        case 'REPUTATION':
          result = await this.syncReputation(item.data);
          break;
        default:
          throw new Error(`Type de synchronisation non supporté: ${item.type}`);
      }

      // Vérifier les conflits
      if (result.conflict) {
        await this.handleConflict(item, result.remoteData);
        return;
      }

      // Succès
      item.status = 'synced';
      this.syncQueue.delete(item.id);
      this.emit('itemSynced', item, result);

    } catch (error) {
      console.error(`Erreur sync item ${item.id}:`, error);
      
      if (item.attempts >= this.MAX_RETRY_ATTEMPTS) {
        item.status = 'failed';
        this.emit('itemFailed', item, error);
      } else {
        item.status = 'pending';
        this.scheduleRetry(item);
      }
    }
  }

  // Synchronisation des transactions
  private async syncTransaction(transaction: any): Promise<any> {
    if (this.isOnline) {
      // Synchronisation avec le serveur principal
      return this.syncWithServer('/api/economy/sync-transaction', transaction);
    } else if (this.isMeshConnected) {
      // Synchronisation via mesh network
      return this.syncWithMesh('TRANSACTION', transaction);
    } else {
      throw new Error('Aucune connectivité disponible');
    }
  }

  // Synchronisation des demandes d'aide
  private async syncHelpRequest(request: any): Promise<any> {
    if (this.isOnline) {
      return this.syncWithServer('/api/needs/sync', request);
    } else if (this.isMeshConnected) {
      return this.syncWithMesh('HELP_REQUEST', request);
    } else {
      throw new Error('Aucune connectivité disponible');
    }
  }

  // Synchronisation des offres d'aide
  private async syncHelpOffer(offer: any): Promise<any> {
    if (this.isOnline) {
      return this.syncWithServer('/api/offers/sync', offer);
    } else if (this.isMeshConnected) {
      return this.syncWithMesh('HELP_OFFER', offer);
    } else {
      throw new Error('Aucune connectivité disponible');
    }
  }

  // Synchronisation des profils utilisateur
  private async syncUserProfile(profile: any): Promise<any> {
    if (this.isOnline) {
      return this.syncWithServer('/api/users/sync', profile);
    } else if (this.isMeshConnected) {
      return this.syncWithMesh('USER_PROFILE', profile);
    } else {
      throw new Error('Aucune connectivité disponible');
    }
  }

  // Synchronisation de la réputation
  private async syncReputation(reputation: any): Promise<any> {
    if (this.isOnline) {
      return this.syncWithServer('/api/reputation/sync', reputation);
    } else if (this.isMeshConnected) {
      return this.syncWithMesh('REPUTATION', reputation);
    } else {
      throw new Error('Aucune connectivité disponible');
    }
  }

  // Synchronisation avec le serveur
  private async syncWithServer(endpoint: string, data: any): Promise<any> {
    const response = await fetch(`${process.env.API_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${await this.getAuthToken()}`
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`Erreur serveur: ${response.status}`);
    }

    return response.json();
  }

  // Synchronisation via mesh network
  private async syncWithMesh(type: string, data: any): Promise<any> {
    // Utiliser le protocole mesh pour synchroniser
    return new Promise((resolve, reject) => {
      this.emit('meshSync', { type, data }, (result: any, error: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(result);
        }
      });
    });
  }

  // Gestion des conflits
  private async handleConflict(localItem: SyncData, remoteData: any): Promise<void> {
    const conflict: ConflictResolution = {
      id: this.generateConflictId(),
      localData: localItem.data,
      remoteData: remoteData,
      resolution: await this.resolveConflict(localItem.data, remoteData)
    };

    if (conflict.resolution === 'merge') {
      conflict.mergedData = await this.mergeData(localItem.data, remoteData);
    }

    this.conflictQueue.set(conflict.id, conflict);
    this.emit('conflictDetected', conflict);

    // Appliquer la résolution automatiquement si possible
    if (conflict.resolution !== 'manual') {
      await this.applyConflictResolution(conflict);
    }
  }

  // Résolution automatique des conflits
  private async resolveConflict(localData: any, remoteData: any): Promise<'local' | 'remote' | 'merge' | 'manual'> {
    // Règles de résolution automatique
    
    // 1. Priorité au timestamp le plus récent pour les données simples
    if (localData.timestamp && remoteData.timestamp) {
      return localData.timestamp > remoteData.timestamp ? 'local' : 'remote';
    }

    // 2. Priorité à la réputation la plus élevée pour les transactions
    if (localData.reputation && remoteData.reputation) {
      return localData.reputation >= remoteData.reputation ? 'local' : 'remote';
    }

    // 3. Fusion pour les données compatibles
    if (this.canMerge(localData, remoteData)) {
      return 'merge';
    }

    // 4. Résolution manuelle pour les cas complexes
    return 'manual';
  }

  // Fusion de données
  private async mergeData(localData: any, remoteData: any): Promise<any> {
    // Stratégies de fusion selon le type de données
    const merged = { ...localData };

    // Fusionner les champs non conflictuels
    for (const key in remoteData) {
      if (!(key in localData)) {
        merged[key] = remoteData[key];
      } else if (Array.isArray(localData[key]) && Array.isArray(remoteData[key])) {
        // Fusionner les tableaux en évitant les doublons
        merged[key] = [...new Set([...localData[key], ...remoteData[key]])];
      } else if (typeof localData[key] === 'object' && typeof remoteData[key] === 'object') {
        // Fusionner récursivement les objets
        merged[key] = await this.mergeData(localData[key], remoteData[key]);
      }
    }

    // Mettre à jour le timestamp
    merged.lastModified = Math.max(
      localData.lastModified || 0,
      remoteData.lastModified || 0,
      Date.now()
    );

    return merged;
  }

  // Vérifier si les données peuvent être fusionnées
  private canMerge(localData: any, remoteData: any): boolean {
    // Critères pour déterminer si une fusion est possible
    const conflictingFields = ['amount', 'status', 'urgency'];
    
    for (const field of conflictingFields) {
      if (localData[field] !== undefined && 
          remoteData[field] !== undefined && 
          localData[field] !== remoteData[field]) {
        return false;
      }
    }
    
    return true;
  }

  // Appliquer la résolution de conflit
  private async applyConflictResolution(conflict: ConflictResolution): Promise<void> {
    let finalData: any;

    switch (conflict.resolution) {
      case 'local':
        finalData = conflict.localData;
        break;
      case 'remote':
        finalData = conflict.remoteData;
        break;
      case 'merge':
        finalData = conflict.mergedData;
        break;
      default:
        return; // Résolution manuelle requise
    }

    // Mettre à jour les données locales
    await this.updateLocalData(conflict.id, finalData);
    
    // Supprimer le conflit de la queue
    this.conflictQueue.delete(conflict.id);
    
    this.emit('conflictResolved', conflict, finalData);
  }

  // Planifier une nouvelle tentative
  private scheduleRetry(item: SyncData): void {
    const delay = this.RETRY_DELAYS[Math.min(item.attempts - 1, this.RETRY_DELAYS.length - 1)];
    
    const timeoutId = setTimeout(() => {
      this.retryIntervals.delete(item.id);
      if (this.canSync()) {
        this.processSyncQueue();
      }
    }, delay);

    this.retryIntervals.set(item.id, timeoutId);
  }

  // Vérifier si la synchronisation est possible
  private canSync(): boolean {
    return this.isOnline || this.isMeshConnected;
  }

  // Vérifier si les dépendances sont synchronisées
  private areDependenciesSynced(dependencies: string[]): boolean {
    return dependencies.every(depId => {
      const item = this.syncQueue.get(depId);
      return !item || item.status === 'synced';
    });
  }

  // Synchronisation périodique
  private startPeriodicSync(): void {
    setInterval(() => {
      if (this.canSync() && !this.syncInProgress) {
        this.processSyncQueue();
      }
    }, 30000); // Toutes les 30 secondes
  }

  // Sauvegarde dans le stockage local
  private async saveToStorage(): Promise<void> {
    try {
      const syncData = {
        queue: Array.from(this.syncQueue.entries()),
        conflicts: Array.from(this.conflictQueue.entries()),
        timestamp: Date.now()
      };

      await AsyncStorage.setItem(
        `${this.STORAGE_KEY_PREFIX}sync_data`,
        JSON.stringify(syncData)
      );
    } catch (error) {
      console.error('Erreur sauvegarde storage:', error);
    }
  }

  // Chargement depuis le stockage local
  private async loadFromStorage(): Promise<void> {
    try {
      const data = await AsyncStorage.getItem(`${this.STORAGE_KEY_PREFIX}sync_data`);
      
      if (data) {
        const syncData = JSON.parse(data);
        this.syncQueue = new Map(syncData.queue);
        this.conflictQueue = new Map(syncData.conflicts);
      }
    } catch (error) {
      console.error('Erreur chargement storage:', error);
    }
  }

  // Utilitaires
  private generateSyncId(): string {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateConflictId(): string {
    return `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async getAuthToken(): Promise<string> {
    // Récupérer le token d'authentification
    return AsyncStorage.getItem('auth_token') || '';
  }

  private async updateLocalData(id: string, data: any): Promise<void> {
    // Mettre à jour les données dans la base locale
    await AsyncStorage.setItem(`${this.STORAGE_KEY_PREFIX}data_${id}`, JSON.stringify(data));
  }

  // API publique
  setNetworkStatus(isOnline: boolean, isMeshConnected: boolean): void {
    const wasConnected = this.canSync();
    this.isOnline = isOnline;
    this.isMeshConnected = isMeshConnected;

    this.emit('networkStatusChanged', { isOnline, isMeshConnected });

    // Démarrer la synchronisation si la connectivité est rétablie
    if (!wasConnected && this.canSync()) {
      this.processSyncQueue();
    }
  }

  getSyncStats(): SyncStats {
    const pending = Array.from(this.syncQueue.values()).filter(item => item.status === 'pending').length;
    const synced = Array.from(this.syncQueue.values()).filter(item => item.status === 'synced').length;
    const failed = Array.from(this.syncQueue.values()).filter(item => item.status === 'failed').length;

    return {
      totalPending: pending,
      totalSynced: synced,
      totalFailed: failed,
      lastSyncTime: Math.max(...Array.from(this.syncQueue.values()).map(item => item.lastAttempt || 0)),
      networkStatus: this.isOnline ? 'online' : this.isMeshConnected ? 'mesh' : 'offline',
      syncInProgress: this.syncInProgress
    };
  }

  getPendingItems(): SyncData[] {
    return Array.from(this.syncQueue.values()).filter(item => item.status === 'pending');
  }

  getConflicts(): ConflictResolution[] {
    return Array.from(this.conflictQueue.values());
  }

  async clearSyncedItems(): Promise<void> {
    const syncedItems = Array.from(this.syncQueue.entries())
      .filter(([_, item]) => item.status === 'synced')
      .map(([id, _]) => id);

    syncedItems.forEach(id => this.syncQueue.delete(id));
    await this.saveToStorage();
  }

  async retryFailedItems(): Promise<void> {
    Array.from(this.syncQueue.values())
      .filter(item => item.status === 'failed')
      .forEach(item => {
        item.status = 'pending';
        item.attempts = 0;
      });

    await this.saveToStorage();
    
    if (this.canSync()) {
      this.processSyncQueue();
    }
  }
}

export default OfflineSyncManager;
