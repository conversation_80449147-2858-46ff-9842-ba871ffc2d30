// Schéma de base de données Prisma pour Nowee
// Génère le client avec: npx prisma generate
// Applique les migrations avec: npx prisma db push

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Modèle utilisateur principal
model User {
  id                String   @id @default(cuid())
  phone             String   @unique
  name              String?
  email             String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  lastActiveAt      DateTime @default(now())
  
  // Profil utilisateur
  preferredLanguage String   @default("fr")
  location          Json?    // Stockage flexible de la géolocalisation
  preferences       Json     @default("{}")
  
  // Statistiques
  messageCount      Int      @default(0)
  helpGiven         Int      @default(0)
  helpReceived      Int      @default(0)
  rating            Float    @default(5.0)
  badges            String[] @default([])
  
  // Relations
  needs             Need[]
  offers            Offer[]
  conversations     Conversation[]
  sentMessages      Message[] @relation("SentMessages")
  receivedMessages  Message[] @relation("ReceivedMessages")
  givenRatings      Rating[]  @relation("GivenRatings")
  receivedRatings   Rating[]  @relation("ReceivedRatings")
  matches           Match[]
  
  @@map("users")
}

// Modèle des besoins exprimés
model Need {
  id          String     @id @default(cuid())
  userId      String
  type        NeedType
  title       String
  description String
  location    Json?
  urgency     Int        @default(2) // 1-4 scale
  status      NeedStatus @default(OPEN)
  expiresAt   DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  
  // Relations
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  matches     Match[]
  messages    Message[]
  
  @@map("needs")
}

// Modèle des offres d'aide
model Offer {
  id           String      @id @default(cuid())
  userId       String
  type         NeedType
  title        String
  description  String
  location     Json?
  availability Json        @default("{}")
  conditions   String?
  status       OfferStatus @default(AVAILABLE)
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  
  // Relations
  user         User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  matches      Match[]
  
  @@map("offers")
}

// Modèle des correspondances entre besoins et offres
model Match {
  id          String      @id @default(cuid())
  needId      String
  offerId     String
  userId      String      // Utilisateur qui a fait le match
  status      MatchStatus @default(PROPOSED)
  rating      Float?
  feedback    String?
  createdAt   DateTime    @default(now())
  completedAt DateTime?
  
  // Relations
  need        Need        @relation(fields: [needId], references: [id], onDelete: Cascade)
  offer       Offer       @relation(fields: [offerId], references: [id], onDelete: Cascade)
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([needId, offerId])
  @@map("matches")
}

// Modèle des conversations
model Conversation {
  id        String   @id @default(cuid())
  userId    String
  context   Json     @default("{}")
  startedAt DateTime @default(now())
  endedAt   DateTime?
  
  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages  Message[]
  
  @@map("conversations")
}

// Modèle des messages
model Message {
  id             String       @id @default(cuid())
  conversationId String
  senderId       String?
  receiverId     String?
  needId         String?
  content        String
  messageType    MessageType  @default(TEXT)
  analysis       Json?        // Analyse IA du message
  createdAt      DateTime     @default(now())
  
  // Relations
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  sender         User?        @relation("SentMessages", fields: [senderId], references: [id])
  receiver       User?        @relation("ReceivedMessages", fields: [receiverId], references: [id])
  need           Need?        @relation(fields: [needId], references: [id])
  
  @@map("messages")
}

// Modèle des évaluations
model Rating {
  id         String   @id @default(cuid())
  giverId    String
  receiverId String
  rating     Float
  comment    String?
  context    String?  // "help", "service", "general"
  createdAt  DateTime @default(now())
  
  // Relations
  giver      User     @relation("GivenRatings", fields: [giverId], references: [id], onDelete: Cascade)
  receiver   User     @relation("ReceivedRatings", fields: [receiverId], references: [id], onDelete: Cascade)
  
  @@unique([giverId, receiverId, context])
  @@map("ratings")
}

// Modèle des événements système (logs)
model SystemEvent {
  id        String    @id @default(cuid())
  type      String
  userId    String?
  data      Json      @default("{}")
  createdAt DateTime  @default(now())
  
  @@map("system_events")
}

// Énumérations
enum NeedType {
  MATERIAL
  SERVICE
  ADVICE
  EMERGENCY
  SOCIAL
  LEARNING
  TRANSPORT
  FOOD
  HOUSING
  HEALTH
}

enum NeedStatus {
  OPEN
  MATCHED
  FULFILLED
  CANCELLED
  EXPIRED
}

enum OfferStatus {
  AVAILABLE
  MATCHED
  COMPLETED
  WITHDRAWN
}

enum MatchStatus {
  PROPOSED
  ACCEPTED
  COMPLETED
  CANCELLED
  REJECTED
}

enum MessageType {
  TEXT
  IMAGE
  AUDIO
  LOCATION
  SYSTEM
}
