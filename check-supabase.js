#!/usr/bin/env node

/**
 * Vérification de l'état des tables Supabase
 */

import 'dotenv/config';
import { SupabaseService } from './src/services/supabaseService.js';

async function checkSupabase() {
  console.log('🔍 Vérification des tables Supabase...\n');
  
  const client = SupabaseService.getClient();
  if (!client) {
    console.log('❌ Supabase non configuré');
    return;
  }
  
  console.log('✅ Supabase configuré');
  console.log(`🔗 URL: ${process.env.SUPABASE_URL}\n`);
  
  // Tester les tables une par une
  const tables = ['users', 'resources', 'matches', 'wallets', 'transactions', 'barter_proposals'];
  
  for (const table of tables) {
    try {
      const { data, error } = await client.from(table).select('*').limit(1);
      if (error) {
        console.log(`❌ Table ${table}: ${error.message}`);
      } else {
        console.log(`✅ Table ${table}: OK (${data.length} lignes testées)`);
      }
    } catch (err) {
      console.log(`❌ Table ${table}: ${err.message}`);
    }
  }
  
  console.log('\n📋 Instructions:');
  console.log('1. Ouvrez votre console SQL Supabase');
  console.log('2. Copiez le contenu de supabase-complete-setup.sql');
  console.log('3. Exécutez le script');
  console.log('4. Relancez ce test');
}

checkSupabase().catch(console.error);
