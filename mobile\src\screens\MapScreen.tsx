/**
 * Écran de Carte Interactive Nowee
 * Visualisation géographique des besoins et offres d'aide
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  Alert,
  Modal,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import MapView, { Marker, Circle, Callout, PROVIDER_GOOGLE } from 'react-native-maps';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Geolocation from 'react-native-geolocation-service';
import { useTheme } from '../services/ThemeService';
import { LocationService } from '../services/LocationService';
import { useAuth } from '../services/AuthService';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface MapResource {
  id: string;
  title: string;
  description: string;
  category: string;
  type: 'NEED' | 'OFFER';
  latitude: number;
  longitude: number;
  urgency: number;
  distance?: number;
  user: {
    name: string;
    rating: number;
  };
}

interface UserLocation {
  latitude: number;
  longitude: number;
  accuracy?: number;
}

const MapScreen: React.FC = () => {
  const { colors } = useTheme();
  const { user } = useAuth();
  const mapRef = useRef<MapView>(null);
  
  const [userLocation, setUserLocation] = useState<UserLocation | null>(null);
  const [resources, setResources] = useState<MapResource[]>([]);
  const [selectedResource, setSelectedResource] = useState<MapResource | null>(null);
  const [showResourceModal, setShowResourceModal] = useState(false);
  const [mapType, setMapType] = useState<'standard' | 'satellite' | 'hybrid'>('standard');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    showNeeds: true,
    showOffers: true,
    categories: ['ALL'],
    maxDistance: 10, // km
    urgencyLevel: 0, // 0 = all, 1-5 = specific urgency
  });
  const [loading, setLoading] = useState(true);

  // Animations
  const filterAnim = new Animated.Value(0);
  const pulseAnim = new Animated.Value(1);

  useEffect(() => {
    getCurrentLocation();
    loadMapResources();
    startPulseAnimation();
  }, []);

  useEffect(() => {
    if (userLocation) {
      loadMapResources();
    }
  }, [userLocation, filters]);

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const getCurrentLocation = () => {
    Geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        setUserLocation({ latitude, longitude, accuracy });
        
        // Centrer la carte sur la position de l'utilisateur
        if (mapRef.current) {
          mapRef.current.animateToRegion({
            latitude,
            longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          }, 1000);
        }
      },
      (error) => {
        console.error('Erreur géolocalisation:', error);
        Alert.alert(
          'Géolocalisation',
          'Impossible d\'obtenir votre position. Veuillez vérifier vos paramètres de localisation.'
        );
        // Position par défaut (Dakar)
        setUserLocation({ latitude: 14.7167, longitude: -17.4677 });
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000,
      }
    );
  };

  const loadMapResources = async () => {
    try {
      setLoading(true);
      
      if (!userLocation) return;

      // Simuler le chargement des ressources depuis l'API
      const mockResources: MapResource[] = [
        {
          id: '1',
          title: 'Besoin d\'une perceuse',
          description: 'J\'ai besoin d\'une perceuse pour des travaux de rénovation',
          category: 'MATERIAL',
          type: 'NEED',
          latitude: 14.7167,
          longitude: -17.4677,
          urgency: 3,
          distance: 0.5,
          user: { name: 'Alice Diop', rating: 4.5 }
        },
        {
          id: '2',
          title: 'Perceuse disponible',
          description: 'Je peux prêter ma perceuse électrique',
          category: 'MATERIAL',
          type: 'OFFER',
          latitude: 14.7200,
          longitude: -17.4700,
          urgency: 1,
          distance: 0.8,
          user: { name: 'Bob Ndiaye', rating: 4.8 }
        },
        {
          id: '3',
          title: 'Aide déménagement',
          description: 'Je cherche de l\'aide pour déménager ce weekend',
          category: 'SERVICE',
          type: 'NEED',
          latitude: 14.7100,
          longitude: -17.4600,
          urgency: 4,
          distance: 1.2,
          user: { name: 'Charlie Fall', rating: 4.2 }
        },
        {
          id: '4',
          title: 'Cours de français',
          description: 'J\'offre des cours de français pour débutants',
          category: 'EDUCATION',
          type: 'OFFER',
          latitude: 14.7250,
          longitude: -17.4750,
          urgency: 1,
          distance: 2.1,
          user: { name: 'Fatou Sall', rating: 4.9 }
        },
      ];

      // Filtrer selon les critères
      const filteredResources = mockResources.filter(resource => {
        if (!filters.showNeeds && resource.type === 'NEED') return false;
        if (!filters.showOffers && resource.type === 'OFFER') return false;
        if (filters.categories[0] !== 'ALL' && !filters.categories.includes(resource.category)) return false;
        if (resource.distance && resource.distance > filters.maxDistance) return false;
        if (filters.urgencyLevel > 0 && resource.urgency !== filters.urgencyLevel) return false;
        
        return true;
      });

      setResources(filteredResources);
    } catch (error) {
      console.error('Erreur chargement ressources carte:', error);
      Alert.alert('Erreur', 'Impossible de charger les ressources sur la carte');
    } finally {
      setLoading(false);
    }
  };

  const getMarkerColor = (resource: MapResource) => {
    if (resource.type === 'NEED') {
      switch (resource.urgency) {
        case 5: return '#F44336'; // Rouge - Très urgent
        case 4: return '#FF9800'; // Orange - Urgent
        case 3: return '#FFC107'; // Jaune - Modéré
        default: return '#2196F3'; // Bleu - Normal
      }
    } else {
      return '#4CAF50'; // Vert pour les offres
    }
  };

  const getMarkerIcon = (resource: MapResource) => {
    const iconMap = {
      MATERIAL: 'build',
      SERVICE: 'handyman',
      EDUCATION: 'school',
      TRANSPORT: 'directions-car',
      CARE: 'favorite',
      FOOD: 'restaurant',
    };
    
    return iconMap[resource.category as keyof typeof iconMap] || 'help';
  };

  const handleMarkerPress = (resource: MapResource) => {
    setSelectedResource(resource);
    setShowResourceModal(true);
  };

  const handleContactUser = () => {
    if (selectedResource) {
      Alert.alert(
        'Contacter',
        `Voulez-vous contacter ${selectedResource.user.name} ?`,
        [
          { text: 'Annuler', style: 'cancel' },
          { text: 'WhatsApp', onPress: () => console.log('Contact WhatsApp') },
          { text: 'Appeler', onPress: () => console.log('Contact téléphone') },
        ]
      );
    }
  };

  const toggleFilters = () => {
    setShowFilters(!showFilters);
    Animated.timing(filterAnim, {
      toValue: showFilters ? 0 : 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const renderFilterPanel = () => (
    <Animated.View 
      style={[
        styles.filterPanel,
        { 
          backgroundColor: colors.surface,
          transform: [{ translateY: filterAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [-200, 0]
          })}]
        }
      ]}
    >
      <View style={styles.filterHeader}>
        <Text style={[styles.filterTitle, { color: colors.text }]}>Filtres</Text>
        <TouchableOpacity onPress={toggleFilters}>
          <Icon name="close" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <View style={styles.filterSection}>
        <Text style={[styles.filterSectionTitle, { color: colors.text }]}>Type</Text>
        <View style={styles.filterRow}>
          <TouchableOpacity
            style={[
              styles.filterChip,
              { backgroundColor: filters.showNeeds ? colors.primary : colors.surface },
              { borderColor: colors.primary }
            ]}
            onPress={() => setFilters(prev => ({ ...prev, showNeeds: !prev.showNeeds }))}
          >
            <Text style={[
              styles.filterChipText,
              { color: filters.showNeeds ? '#FFFFFF' : colors.text }
            ]}>
              Besoins
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.filterChip,
              { backgroundColor: filters.showOffers ? colors.primary : colors.surface },
              { borderColor: colors.primary }
            ]}
            onPress={() => setFilters(prev => ({ ...prev, showOffers: !prev.showOffers }))}
          >
            <Text style={[
              styles.filterChipText,
              { color: filters.showOffers ? '#FFFFFF' : colors.text }
            ]}>
              Offres
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.filterSection}>
        <Text style={[styles.filterSectionTitle, { color: colors.text }]}>
          Distance max: {filters.maxDistance} km
        </Text>
        <View style={styles.distanceSlider}>
          {[1, 5, 10, 20].map(distance => (
            <TouchableOpacity
              key={distance}
              style={[
                styles.distanceOption,
                { backgroundColor: filters.maxDistance === distance ? colors.primary : colors.surface }
              ]}
              onPress={() => setFilters(prev => ({ ...prev, maxDistance: distance }))}
            >
              <Text style={[
                styles.distanceText,
                { color: filters.maxDistance === distance ? '#FFFFFF' : colors.text }
              ]}>
                {distance}km
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Animated.View>
  );

  const renderResourceModal = () => (
    <Modal
      visible={showResourceModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowResourceModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
          {selectedResource && (
            <>
              <View style={styles.modalHeader}>
                <View style={styles.resourceTypeIndicator}>
                  <Icon 
                    name={getMarkerIcon(selectedResource)} 
                    size={24} 
                    color={getMarkerColor(selectedResource)} 
                  />
                  <Text style={[
                    styles.resourceTypeText,
                    { color: getMarkerColor(selectedResource) }
                  ]}>
                    {selectedResource.type === 'NEED' ? 'Besoin' : 'Offre'}
                  </Text>
                </View>
                
                <TouchableOpacity onPress={() => setShowResourceModal(false)}>
                  <Icon name="close" size={24} color={colors.text} />
                </TouchableOpacity>
              </View>

              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {selectedResource.title}
              </Text>

              <Text style={[styles.modalDescription, { color: colors.textSecondary }]}>
                {selectedResource.description}
              </Text>

              <View style={styles.resourceInfo}>
                <View style={styles.infoRow}>
                  <Icon name="person" size={20} color={colors.textSecondary} />
                  <Text style={[styles.infoText, { color: colors.text }]}>
                    {selectedResource.user.name}
                  </Text>
                  <View style={styles.rating}>
                    <Icon name="star" size={16} color="#FFD700" />
                    <Text style={[styles.ratingText, { color: colors.text }]}>
                      {selectedResource.user.rating}
                    </Text>
                  </View>
                </View>

                <View style={styles.infoRow}>
                  <Icon name="location-on" size={20} color={colors.textSecondary} />
                  <Text style={[styles.infoText, { color: colors.text }]}>
                    À {selectedResource.distance?.toFixed(1)} km
                  </Text>
                </View>

                {selectedResource.type === 'NEED' && (
                  <View style={styles.infoRow}>
                    <Icon name="priority-high" size={20} color={getMarkerColor(selectedResource)} />
                    <Text style={[styles.infoText, { color: colors.text }]}>
                      Urgence: {selectedResource.urgency}/5
                    </Text>
                  </View>
                )}
              </View>

              <View style={styles.modalActions}>
                <TouchableOpacity 
                  style={[styles.actionButton, { backgroundColor: colors.primary }]}
                  onPress={handleContactUser}
                >
                  <Icon name="message" size={20} color="#FFFFFF" />
                  <Text style={styles.actionButtonText}>Contacter</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[styles.actionButton, { backgroundColor: colors.secondary }]}
                  onPress={() => {
                    // Centrer la carte sur la ressource
                    if (mapRef.current) {
                      mapRef.current.animateToRegion({
                        latitude: selectedResource.latitude,
                        longitude: selectedResource.longitude,
                        latitudeDelta: 0.005,
                        longitudeDelta: 0.005,
                      }, 1000);
                    }
                    setShowResourceModal(false);
                  }}
                >
                  <Icon name="my-location" size={20} color="#FFFFFF" />
                  <Text style={styles.actionButtonText}>Localiser</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.mapContainer}>
        <MapView
          ref={mapRef}
          style={styles.map}
          provider={PROVIDER_GOOGLE}
          mapType={mapType}
          showsUserLocation={true}
          showsMyLocationButton={false}
          initialRegion={{
            latitude: userLocation?.latitude || 14.7167,
            longitude: userLocation?.longitude || -17.4677,
            latitudeDelta: 0.02,
            longitudeDelta: 0.02,
          }}
        >
          {/* Marqueurs des ressources */}
          {resources.map((resource) => (
            <Marker
              key={resource.id}
              coordinate={{
                latitude: resource.latitude,
                longitude: resource.longitude,
              }}
              onPress={() => handleMarkerPress(resource)}
            >
              <View style={[
                styles.customMarker,
                { backgroundColor: getMarkerColor(resource) }
              ]}>
                <Icon 
                  name={getMarkerIcon(resource)} 
                  size={20} 
                  color="#FFFFFF" 
                />
              </View>
            </Marker>
          ))}

          {/* Cercle de rayon autour de l'utilisateur */}
          {userLocation && (
            <Circle
              center={userLocation}
              radius={filters.maxDistance * 1000} // Convertir km en mètres
              strokeColor={colors.primary}
              strokeWidth={2}
              fillColor={`${colors.primary}20`}
            />
          )}
        </MapView>

        {/* Position de l'utilisateur avec animation */}
        {userLocation && (
          <Animated.View 
            style={[
              styles.userLocationPulse,
              {
                transform: [{ scale: pulseAnim }],
                left: screenWidth / 2 - 15,
                top: screenHeight / 2 - 15,
              }
            ]}
          >
            <View style={[styles.userLocationDot, { backgroundColor: colors.primary }]} />
          </Animated.View>
        )}
      </View>

      {/* Contrôles de la carte */}
      <View style={styles.mapControls}>
        <TouchableOpacity 
          style={[styles.controlButton, { backgroundColor: colors.surface }]}
          onPress={toggleFilters}
        >
          <Icon name="filter-list" size={24} color={colors.text} />
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.controlButton, { backgroundColor: colors.surface }]}
          onPress={() => {
            const types = ['standard', 'satellite', 'hybrid'] as const;
            const currentIndex = types.indexOf(mapType);
            const nextIndex = (currentIndex + 1) % types.length;
            setMapType(types[nextIndex]);
          }}
        >
          <Icon name="layers" size={24} color={colors.text} />
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.controlButton, { backgroundColor: colors.surface }]}
          onPress={getCurrentLocation}
        >
          <Icon name="my-location" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Légende */}
      <View style={[styles.legend, { backgroundColor: colors.surface }]}>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: '#4CAF50' }]} />
          <Text style={[styles.legendText, { color: colors.text }]}>Offres</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: '#2196F3' }]} />
          <Text style={[styles.legendText, { color: colors.text }]}>Besoins</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: '#F44336' }]} />
          <Text style={[styles.legendText, { color: colors.text }]}>Urgent</Text>
        </View>
      </View>

      {/* Panneau de filtres */}
      {showFilters && renderFilterPanel()}

      {/* Modal de détails de ressource */}
      {renderResourceModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  customMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  userLocationPulse: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userLocationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  mapControls: {
    position: 'absolute',
    right: 16,
    top: 60,
  },
  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  legend: {
    position: 'absolute',
    bottom: 100,
    left: 16,
    flexDirection: 'row',
    padding: 12,
    borderRadius: 8,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    fontWeight: '600',
  },
  filterPanel: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    padding: 16,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  filterTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  filterSection: {
    marginBottom: 16,
  },
  filterSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  filterRow: {
    flexDirection: 'row',
  },
  filterChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
  },
  filterChipText: {
    fontSize: 14,
    fontWeight: '600',
  },
  distanceSlider: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  distanceOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  distanceText: {
    fontSize: 12,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: screenHeight * 0.7,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  resourceTypeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resourceTypeText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '600',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  modalDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 16,
  },
  resourceInfo: {
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoText: {
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '600',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
  },
  actionButtonText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontWeight: '600',
  },
});

export default MapScreen;
