#!/usr/bin/env node

/**
 * Test simple du système vocal
 */

import 'dotenv/config';
import { VoiceService } from './src/services/voiceService.js';

async function testSimple() {
  console.log('🎤 Test simple du système vocal Nowee');
  
  try {
    // Test de synthèse vocale
    console.log('📝 Test synthèse vocale...');
    const result = await VoiceService.textToSpeech('Bonjour ! Je suis Nowee.');
    
    if (result.success) {
      console.log('✅ Synthèse vocale réussie !');
      console.log(`📊 Taille audio: ${result.audioBuffer.length} bytes`);
    } else {
      console.log('❌ Erreur synthèse:', result.error);
    }
    
    // Test de détection de langue
    console.log('🌍 Test détection de langue...');
    const lang = await VoiceService.detectTextLanguage('Bonjour, comment allez-vous ?');
    console.log(`🔍 Langue détectée: ${lang}`);
    
    // Test d'adaptation de texte
    console.log('🔧 Test adaptation de texte...');
    const adapted = VoiceService.adaptTextForLanguage('🎯 Aide trouvée ! ✅ Parfait !', 'fr');
    console.log(`📝 Texte adapté: "${adapted}"`);
    
    console.log('🎉 Tests terminés avec succès !');
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

testSimple();
