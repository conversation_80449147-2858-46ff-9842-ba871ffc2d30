/**
 * Serveur Nowee Complet avec Mesh Networking
 * Version intégrée avec toutes les fonctionnalités
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const OpenAI = require('openai');
const twilio = require('twilio');
const winston = require('winston');
require('dotenv').config();

// Configuration
const app = express();
const PORT = process.env.PORT || 3000;

// Logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Clients externes
let openai, twilioClient;

try {
  if (process.env.OPENAI_API_KEY) {
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    logger.info('✅ OpenAI configuré');
  }
} catch (error) {
  logger.warn('⚠️ OpenAI non configuré:', error.message);
}

try {
  if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
    twilioClient = twilio(
      process.env.TWILIO_ACCOUNT_SID,
      process.env.TWILIO_AUTH_TOKEN
    );
    logger.info('✅ Twilio configuré');
  }
} catch (error) {
  logger.warn('⚠️ Twilio non configuré:', error.message);
}

// Middleware
app.use(helmet());
app.use(compression());
app.use(cors({
  origin: process.env.FRONTEND_URL || '*',
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limite chaque IP à 100 requêtes par windowMs
  message: 'Trop de requêtes depuis cette IP, réessayez plus tard.'
});
app.use(limiter);

// Données de test en mémoire
const mockData = {
  users: new Map([
    ['+************', {
      phone: '+************',
      name: 'Aminata Diallo',
      location: { latitude: 14.6928, longitude: -17.4467, address: 'Dakar, Sénégal' },
      reputation: 4.8,
      noweeCoins: 150,
      timeCredits: 2.5,
      totalEarned: 200,
      totalSpent: 50,
      joinedAt: '2024-01-15',
      skills: ['aide ménagère', 'cuisine', 'garde enfants'],
      languages: ['français', 'wolof']
    }],
    ['+221701234568', {
      phone: '+221701234568',
      name: 'Moussa Sow',
      location: { latitude: 14.6937, longitude: -17.4441, address: 'Médina, Dakar' },
      reputation: 4.9,
      noweeCoins: 200,
      timeCredits: 1.0,
      totalEarned: 300,
      totalSpent: 100,
      joinedAt: '2024-02-01',
      skills: ['réparation', 'électricité', 'plomberie'],
      languages: ['français', 'wolof', 'anglais']
    }]
  ]),
  
  needs: [
    {
      id: '1',
      phone: '+************',
      title: 'Aide pour déménagement',
      description: 'Je cherche 2-3 personnes pour m\'aider à déménager ce weekend',
      category: 'SERVICE',
      urgency: 'medium',
      latitude: 14.6928,
      longitude: -17.4467,
      address: 'Dakar, Sénégal',
      status: 'active',
      createdAt: new Date().toISOString(),
      reward: { noweeCoins: 50, timeCredits: 2 }
    },
    {
      id: '2',
      phone: '+221701234568',
      title: 'Cours de français',
      description: 'J\'offre des cours de français pour débutants et intermédiaires',
      category: 'EDUCATION',
      urgency: 'low',
      latitude: 14.6937,
      longitude: -17.4441,
      address: 'Médina, Dakar',
      status: 'active',
      createdAt: new Date().toISOString(),
      reward: { noweeCoins: 30, timeCredits: 1 }
    }
  ],
  
  transactions: [],
  chatHistory: [],
  meshNodes: new Map(),
  meshMessages: []
};

// Routes de base
app.get('/', (req, res) => {
  res.json({
    message: '🚀 Nowee API - Réseau d\'entraide locale avec Mesh Networking',
    version: '2.0.0',
    features: ['WhatsApp Bot', 'IA Contextuelle', 'Économie NoweeCoins', 'Mesh Networking'],
    status: 'active',
    timestamp: new Date().toISOString()
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    environment: process.env.NODE_ENV || 'development',
    apis: {
      openai: !!openai,
      twilio: !!twilioClient
    },
    features: {
      economy: true,
      barter: true,
      voice: true,
      maps: true,
      notifications: true,
      mesh: true
    },
    stats: {
      users: mockData.users.size,
      needs: mockData.needs.length,
      transactions: mockData.transactions.length,
      meshNodes: mockData.meshNodes.size
    }
  });
});

// API Chat IA
app.post('/api/chat/ai', async (req, res) => {
  try {
    const { message, phone, context } = req.body;
    
    if (!message || !phone) {
      return res.status(400).json({
        success: false,
        error: 'Message et numéro de téléphone requis'
      });
    }

    if (!openai) {
      return res.status(503).json({
        success: false,
        error: 'Service IA temporairement indisponible'
      });
    }

    // Obtenir le profil utilisateur
    const user = mockData.users.get(phone) || {
      name: 'Utilisateur',
      location: { address: 'Dakar, Sénégal' }
    };

    // Prompt contextualisé pour le Sénégal
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: `Tu es Nowee, l'assistant IA d'entraide locale au Sénégal. Tu aides les gens à se connecter et s'entraider dans leur communauté.

Contexte utilisateur:
- Nom: ${user.name}
- Localisation: ${user.location.address}
- Réputation: ${user.reputation || 'Nouveau'}
- NoweeCoins: ${user.noweeCoins || 0}

Ton rôle:
- Être chaleureux et encourageant
- Proposer des solutions d'entraide locale
- Expliquer le système NoweeCoins et de troc
- Utiliser des expressions sénégalaises appropriées
- Encourager la solidarité communautaire

Réponds en français avec un ton amical et local.`
        },
        {
          role: "user",
          content: message
        }
      ],
      max_tokens: 200,
      temperature: 0.8
    });

    const response = completion.choices[0].message.content;

    // Enregistrer la conversation
    mockData.chatHistory.push({
      id: Date.now().toString(),
      phone,
      message,
      response,
      timestamp: new Date().toISOString(),
      context
    });

    res.json({
      success: true,
      response,
      tokensUsed: completion.usage.total_tokens,
      model: 'gpt-3.5-turbo'
    });

  } catch (error) {
    logger.error('Erreur Chat IA:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors du traitement de votre message'
    });
  }
});

// API WhatsApp Webhook
app.post('/api/whatsapp/webhook', async (req, res) => {
  try {
    const { Body, From, MessageSid } = req.body;
    
    logger.info('📱 Message WhatsApp reçu:', { From, Body });

    if (!Body || !From) {
      return res.status(400).json({
        success: false,
        error: 'Message ou expéditeur manquant'
      });
    }

    // Traiter le message avec l'IA
    const aiResponse = await fetch(`http://localhost:${PORT}/api/chat/ai`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: Body,
        phone: From.replace('whatsapp:', ''),
        context: 'whatsapp'
      })
    });

    const aiData = await aiResponse.json();

    if (aiData.success && twilioClient) {
      // Envoyer la réponse via WhatsApp
      await twilioClient.messages.create({
        body: aiData.response,
        from: process.env.TWILIO_WHATSAPP_NUMBER,
        to: From
      });

      logger.info('✅ Réponse envoyée via WhatsApp');
    }

    res.json({
      success: true,
      message: 'Message traité avec succès',
      response: aiData.response || 'Réponse générée'
    });

  } catch (error) {
    logger.error('Erreur WhatsApp webhook:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors du traitement du message WhatsApp'
    });
  }
});

// API Économie
app.get('/api/economy/wallet/:phone', (req, res) => {
  try {
    const { phone } = req.params;
    const user = mockData.users.get(phone);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'Utilisateur non trouvé'
      });
    }

    const stats = {
      netBalance: user.totalEarned - user.totalSpent,
      reputationLevel: user.reputation >= 4.5 ? 'Expert' : 
                      user.reputation >= 4.0 ? 'Avancé' :
                      user.reputation >= 3.0 ? 'Intermédiaire' : 'Débutant',
      totalValue: user.noweeCoins + (user.timeCredits * 50),
      rank: Math.floor(Math.random() * 100) + 1
    };

    res.json({
      success: true,
      wallet: { ...user, stats }
    });
  } catch (error) {
    logger.error('Erreur wallet:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API Besoins
app.get('/api/needs', (req, res) => {
  try {
    const { category, urgency } = req.query;
    let needs = [...mockData.needs];
    
    if (category) {
      needs = needs.filter(need => need.category === category);
    }
    
    if (urgency) {
      needs = needs.filter(need => need.urgency === urgency);
    }
    
    res.json({ success: true, needs });
  } catch (error) {
    logger.error('Erreur besoins:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/needs', (req, res) => {
  try {
    const need = {
      id: Date.now().toString(),
      ...req.body,
      status: 'active',
      createdAt: new Date().toISOString()
    };
    
    mockData.needs.push(need);
    
    res.json({ success: true, need });
  } catch (error) {
    logger.error('Erreur création besoin:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API Mesh Networking
app.post('/api/mesh/register', (req, res) => {
  try {
    const { nodeId, publicKey, capabilities, location } = req.body;
    
    const node = {
      id: nodeId,
      publicKey,
      capabilities: capabilities || [],
      location,
      reputation: 4.0,
      lastSeen: Date.now(),
      status: 'active'
    };
    
    mockData.meshNodes.set(nodeId, node);
    
    logger.info(`🕸️ Nœud mesh enregistré: ${nodeId}`);
    
    res.json({
      success: true,
      message: 'Nœud enregistré dans le réseau mesh',
      node
    });
  } catch (error) {
    logger.error('Erreur enregistrement mesh:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.get('/api/mesh/nodes', (req, res) => {
  try {
    const nodes = Array.from(mockData.meshNodes.values());
    
    res.json({
      success: true,
      nodes,
      count: nodes.length
    });
  } catch (error) {
    logger.error('Erreur récupération nœuds mesh:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/mesh/message', (req, res) => {
  try {
    const message = {
      id: Date.now().toString(),
      ...req.body,
      timestamp: Date.now(),
      status: 'pending'
    };
    
    mockData.meshMessages.push(message);
    
    logger.info(`🕸️ Message mesh reçu: ${message.type} de ${message.source}`);
    
    res.json({
      success: true,
      message: 'Message ajouté au réseau mesh',
      messageId: message.id
    });
  } catch (error) {
    logger.error('Erreur message mesh:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API Statistiques
app.get('/api/stats/advanced', (req, res) => {
  try {
    const stats = {
      timestamp: new Date().toISOString(),
      community: {
        totalUsers: mockData.users.size,
        activeUsers24h: Math.floor(mockData.users.size * 0.3),
        totalNeeds: mockData.needs.length,
        needsToday: Math.floor(mockData.needs.length * 0.1),
        communities: 45
      },
      economy: {
        totalCoins: 125000,
        coinsInCirculation: 106250,
        averageWallet: 100,
        totalTransactions: mockData.transactions.length,
        transactionsToday: Math.floor(mockData.transactions.length * 0.1)
      },
      mesh: {
        totalNodes: mockData.meshNodes.size,
        activeNodes: mockData.meshNodes.size,
        totalMessages: mockData.meshMessages.length,
        messagesPerSecond: 2.5,
        networkHealth: 95
      },
      ai: {
        totalConversations: mockData.chatHistory.length,
        conversationsToday: Math.floor(mockData.chatHistory.length * 0.2),
        averageResponseTime: '1.2s',
        satisfactionRate: 0.92
      }
    };
    
    res.json({ success: true, stats });
  } catch (error) {
    logger.error('Erreur stats:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  logger.error('Erreur serveur:', err);
  res.status(500).json({
    success: false,
    error: 'Erreur interne du serveur'
  });
});

// Route 404
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route non trouvée',
    availableRoutes: [
      'GET /',
      'GET /health',
      'POST /api/chat/ai',
      'POST /api/whatsapp/webhook',
      'GET /api/economy/wallet/:phone',
      'GET /api/needs',
      'POST /api/needs',
      'POST /api/mesh/register',
      'GET /api/mesh/nodes',
      'POST /api/mesh/message',
      'GET /api/stats/advanced'
    ]
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  logger.info(`🚀 Serveur Nowee démarré sur le port ${PORT}`);
  logger.info(`📊 Santé: http://localhost:${PORT}/health`);
  logger.info(`🤖 Chat IA: POST http://localhost:${PORT}/api/chat/ai`);
  logger.info(`📱 WhatsApp: POST http://localhost:${PORT}/api/whatsapp/webhook`);
  logger.info(`💰 Économie: http://localhost:${PORT}/api/economy/wallet/+************`);
  logger.info(`🕸️ Mesh: http://localhost:${PORT}/api/mesh/nodes`);
  logger.info(`📈 Stats: http://localhost:${PORT}/api/stats/advanced`);
  
  if (openai) logger.info('✅ OpenAI connecté');
  if (twilioClient) logger.info('✅ Twilio connecté');
  
  logger.info('🎊 Nowee prêt à révolutionner l\'entraide locale ! 🎊');
});

module.exports = app;
