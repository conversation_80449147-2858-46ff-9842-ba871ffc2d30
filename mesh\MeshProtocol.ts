/**
 * Protocole de Communication Mesh Nowee
 * Système de réseau maillé pour communication sans internet
 */

import { EventEmitter } from 'events';
import CryptoJS from 'crypto-js';

// Types de base
export interface MeshNode {
  id: string;
  publicKey: string;
  reputation: number;
  location?: {
    latitude: number;
    longitude: number;
    accuracy: number;
  };
  lastSeen: number;
  capabilities: string[];
}

export interface MeshMessage {
  id: string;
  type: 'HELP_REQUEST' | 'HELP_OFFER' | 'TRANSACTION' | 'SYNC' | 'HEARTBEAT';
  source: string;
  destination?: string;
  ttl: number;
  timestamp: number;
  signature: string;
  payload: any;
  route: string[];
  priority: 'low' | 'medium' | 'high' | 'emergency';
}

export interface RouteInfo {
  destination: string;
  nextHop: string;
  hopCount: number;
  reliability: number;
  lastUpdate: number;
}

export class NoweeeMeshProtocol extends EventEmitter {
  private nodeId: string;
  private privateKey: string;
  private publicKey: string;
  private neighbors: Map<string, MeshNode> = new Map();
  private routingTable: Map<string, RouteInfo> = new Map();
  private messageCache: Map<string, number> = new Map();
  private pendingMessages: Map<string, MeshMessage> = new Map();
  
  // Configuration
  private readonly MAX_TTL = 10;
  private readonly HEARTBEAT_INTERVAL = 30000; // 30 secondes
  private readonly CACHE_CLEANUP_INTERVAL = 300000; // 5 minutes
  private readonly MESSAGE_TIMEOUT = 60000; // 1 minute

  constructor(nodeId: string) {
    super();
    this.nodeId = nodeId;
    this.generateKeyPair();
    this.startHeartbeat();
    this.startCacheCleanup();
  }

  // Génération des clés cryptographiques
  private generateKeyPair(): void {
    // Simulation de génération de clés (en production, utiliser crypto.subtle)
    this.privateKey = CryptoJS.lib.WordArray.random(256/8).toString();
    this.publicKey = CryptoJS.SHA256(this.privateKey).toString();
  }

  // Découverte de voisins
  async discoverNeighbors(): Promise<MeshNode[]> {
    const discoveredNodes: MeshNode[] = [];
    
    try {
      // WebRTC peer discovery
      const webrtcPeers = await this.discoverWebRTCPeers();
      discoveredNodes.push(...webrtcPeers);
      
      // Bluetooth discovery
      const bluetoothPeers = await this.discoverBluetoothPeers();
      discoveredNodes.push(...bluetoothPeers);
      
      // WiFi Direct discovery
      const wifiPeers = await this.discoverWiFiDirectPeers();
      discoveredNodes.push(...wifiPeers);
      
    } catch (error) {
      console.error('Erreur découverte voisins:', error);
    }
    
    return discoveredNodes;
  }

  private async discoverWebRTCPeers(): Promise<MeshNode[]> {
    // Implémentation WebRTC peer discovery
    return [
      {
        id: 'webrtc-peer-1',
        publicKey: 'webrtc-key-1',
        reputation: 4.5,
        lastSeen: Date.now(),
        capabilities: ['webrtc', 'relay']
      }
    ];
  }

  private async discoverBluetoothPeers(): Promise<MeshNode[]> {
    // Implémentation Bluetooth LE discovery
    return [
      {
        id: 'ble-peer-1',
        publicKey: 'ble-key-1',
        reputation: 4.2,
        lastSeen: Date.now(),
        capabilities: ['bluetooth', 'proximity']
      }
    ];
  }

  private async discoverWiFiDirectPeers(): Promise<MeshNode[]> {
    // Implémentation WiFi Direct discovery
    return [
      {
        id: 'wifi-peer-1',
        publicKey: 'wifi-key-1',
        reputation: 4.8,
        lastSeen: Date.now(),
        capabilities: ['wifi-direct', 'high-bandwidth']
      }
    ];
  }

  // Envoi de message
  async sendMessage(message: Omit<MeshMessage, 'id' | 'signature' | 'route'>): Promise<boolean> {
    const fullMessage: MeshMessage = {
      ...message,
      id: this.generateMessageId(),
      signature: this.signMessage(message),
      route: [this.nodeId]
    };

    // Ajouter à la cache pour éviter les boucles
    this.messageCache.set(fullMessage.id, Date.now());

    if (message.destination) {
      // Message unicast
      return this.routeMessage(fullMessage);
    } else {
      // Message broadcast
      return this.broadcastMessage(fullMessage);
    }
  }

  // Routage intelligent
  private async routeMessage(message: MeshMessage): Promise<boolean> {
    const route = this.findOptimalRoute(message.destination!);
    
    if (route.length === 0) {
      console.warn(`Aucune route trouvée vers ${message.destination}`);
      return false;
    }

    const nextHop = route[0];
    return this.forwardMessage(message, nextHop);
  }

  // Algorithme de routage optimisé
  private findOptimalRoute(destination: string): string[] {
    // 1. Route directe si voisin
    if (this.neighbors.has(destination)) {
      return [destination];
    }

    // 2. Routage par table de routage
    const routeInfo = this.routingTable.get(destination);
    if (routeInfo && this.neighbors.has(routeInfo.nextHop)) {
      return [routeInfo.nextHop];
    }

    // 3. Routage par réputation
    const reputationRoute = this.findReputationBasedRoute(destination);
    if (reputationRoute.length > 0) {
      return reputationRoute;
    }

    // 4. Routage par proximité géographique
    const proximityRoute = this.findProximityBasedRoute(destination);
    if (proximityRoute.length > 0) {
      return proximityRoute;
    }

    return [];
  }

  private findReputationBasedRoute(destination: string): string[] {
    const highRepNeighbors = Array.from(this.neighbors.values())
      .filter(node => node.reputation > 4.0)
      .sort((a, b) => b.reputation - a.reputation);

    return highRepNeighbors.length > 0 ? [highRepNeighbors[0].id] : [];
  }

  private findProximityBasedRoute(destination: string): string[] {
    // Implémentation du routage géographique
    const neighborsWithLocation = Array.from(this.neighbors.values())
      .filter(node => node.location);

    if (neighborsWithLocation.length === 0) return [];

    // Sélectionner le voisin le plus proche géographiquement
    const closest = neighborsWithLocation.reduce((prev, current) => {
      const prevDistance = this.calculateDistance(prev.location!, { latitude: 0, longitude: 0 });
      const currentDistance = this.calculateDistance(current.location!, { latitude: 0, longitude: 0 });
      return currentDistance < prevDistance ? current : prev;
    });

    return [closest.id];
  }

  // Diffusion de message
  private async broadcastMessage(message: MeshMessage): Promise<boolean> {
    let successCount = 0;
    const neighbors = Array.from(this.neighbors.keys());

    for (const neighborId of neighbors) {
      try {
        const success = await this.forwardMessage(message, neighborId);
        if (success) successCount++;
      } catch (error) {
        console.error(`Erreur envoi vers ${neighborId}:`, error);
      }
    }

    return successCount > 0;
  }

  // Transmission vers un voisin
  private async forwardMessage(message: MeshMessage, nextHop: string): Promise<boolean> {
    // Vérifier TTL
    if (message.ttl <= 0) {
      console.warn('Message TTL expiré');
      return false;
    }

    // Décrémenter TTL et ajouter à la route
    const forwardedMessage: MeshMessage = {
      ...message,
      ttl: message.ttl - 1,
      route: [...message.route, this.nodeId]
    };

    try {
      // Simulation d'envoi (en production, utiliser WebRTC/Bluetooth/WiFi)
      await this.simulateTransmission(forwardedMessage, nextHop);
      
      this.emit('messageSent', {
        messageId: message.id,
        destination: nextHop,
        success: true
      });
      
      return true;
    } catch (error) {
      console.error(`Erreur transmission vers ${nextHop}:`, error);
      
      this.emit('messageSent', {
        messageId: message.id,
        destination: nextHop,
        success: false,
        error: error.message
      });
      
      return false;
    }
  }

  // Réception de message
  async receiveMessage(message: MeshMessage): Promise<void> {
    // Vérifier si déjà traité
    if (this.messageCache.has(message.id)) {
      return;
    }

    // Ajouter à la cache
    this.messageCache.set(message.id, Date.now());

    // Vérifier signature
    if (!this.verifySignature(message)) {
      console.warn('Signature invalide pour message:', message.id);
      return;
    }

    // Traiter selon le type
    switch (message.type) {
      case 'HELP_REQUEST':
        await this.handleHelpRequest(message);
        break;
      case 'HELP_OFFER':
        await this.handleHelpOffer(message);
        break;
      case 'TRANSACTION':
        await this.handleTransaction(message);
        break;
      case 'SYNC':
        await this.handleSync(message);
        break;
      case 'HEARTBEAT':
        await this.handleHeartbeat(message);
        break;
    }

    // Retransmettre si nécessaire
    if (message.destination && message.destination !== this.nodeId) {
      await this.routeMessage(message);
    } else if (!message.destination) {
      // Broadcast - retransmettre aux autres voisins
      await this.broadcastMessage(message);
    }
  }

  // Gestionnaires de messages
  private async handleHelpRequest(message: MeshMessage): Promise<void> {
    this.emit('helpRequest', message.payload);
  }

  private async handleHelpOffer(message: MeshMessage): Promise<void> {
    this.emit('helpOffer', message.payload);
  }

  private async handleTransaction(message: MeshMessage): Promise<void> {
    this.emit('transaction', message.payload);
  }

  private async handleSync(message: MeshMessage): Promise<void> {
    // Synchronisation des données
    this.emit('dataSync', message.payload);
  }

  private async handleHeartbeat(message: MeshMessage): Promise<void> {
    // Mettre à jour les informations du nœud
    const nodeInfo = message.payload as MeshNode;
    this.neighbors.set(message.source, {
      ...nodeInfo,
      lastSeen: Date.now()
    });
    
    this.updateRoutingTable(message.source, message.route);
  }

  // Mise à jour de la table de routage
  private updateRoutingTable(destination: string, route: string[]): void {
    const hopCount = route.length;
    const nextHop = route[1] || destination;
    
    const existing = this.routingTable.get(destination);
    if (!existing || hopCount < existing.hopCount) {
      this.routingTable.set(destination, {
        destination,
        nextHop,
        hopCount,
        reliability: 1.0,
        lastUpdate: Date.now()
      });
    }
  }

  // Heartbeat périodique
  private startHeartbeat(): void {
    setInterval(() => {
      this.sendHeartbeat();
    }, this.HEARTBEAT_INTERVAL);
  }

  private async sendHeartbeat(): Promise<void> {
    const heartbeat: Omit<MeshMessage, 'id' | 'signature' | 'route'> = {
      type: 'HEARTBEAT',
      source: this.nodeId,
      ttl: 3, // Heartbeat local seulement
      timestamp: Date.now(),
      priority: 'low',
      payload: {
        id: this.nodeId,
        publicKey: this.publicKey,
        reputation: await this.getMyReputation(),
        location: await this.getCurrentLocation(),
        capabilities: this.getCapabilities()
      }
    };

    await this.sendMessage(heartbeat);
  }

  // Utilitaires
  private generateMessageId(): string {
    return `${this.nodeId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private signMessage(message: any): string {
    const messageString = JSON.stringify(message);
    return CryptoJS.HmacSHA256(messageString, this.privateKey).toString();
  }

  private verifySignature(message: MeshMessage): boolean {
    const { signature, ...messageWithoutSignature } = message;
    const expectedSignature = this.signMessage(messageWithoutSignature);
    return signature === expectedSignature;
  }

  private calculateDistance(pos1: { latitude: number; longitude: number }, pos2: { latitude: number; longitude: number }): number {
    const R = 6371; // Rayon de la Terre en km
    const dLat = (pos2.latitude - pos1.latitude) * Math.PI / 180;
    const dLon = (pos2.longitude - pos1.longitude) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(pos1.latitude * Math.PI / 180) * Math.cos(pos2.latitude * Math.PI / 180) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private async simulateTransmission(message: MeshMessage, destination: string): Promise<void> {
    // Simulation de transmission (remplacer par vraie implémentation)
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.1) { // 90% de succès
          resolve();
        } else {
          reject(new Error('Transmission failed'));
        }
      }, Math.random() * 100 + 50); // 50-150ms de latence
    });
  }

  private async getMyReputation(): Promise<number> {
    // Récupérer la réputation depuis le système Nowee
    return 4.5;
  }

  private async getCurrentLocation(): Promise<{ latitude: number; longitude: number; accuracy: number } | undefined> {
    // Récupérer la position GPS
    return {
      latitude: 14.6928,
      longitude: -17.4467,
      accuracy: 10
    };
  }

  private getCapabilities(): string[] {
    return ['webrtc', 'bluetooth', 'wifi-direct', 'nowee-economy'];
  }

  // Nettoyage périodique
  private startCacheCleanup(): void {
    setInterval(() => {
      this.cleanupCache();
    }, this.CACHE_CLEANUP_INTERVAL);
  }

  private cleanupCache(): void {
    const now = Date.now();
    
    // Nettoyer cache des messages
    for (const [messageId, timestamp] of this.messageCache.entries()) {
      if (now - timestamp > this.MESSAGE_TIMEOUT) {
        this.messageCache.delete(messageId);
      }
    }
    
    // Nettoyer voisins inactifs
    for (const [nodeId, node] of this.neighbors.entries()) {
      if (now - node.lastSeen > this.HEARTBEAT_INTERVAL * 3) {
        this.neighbors.delete(nodeId);
        this.routingTable.delete(nodeId);
      }
    }
  }

  // API publique
  getNeighbors(): MeshNode[] {
    return Array.from(this.neighbors.values());
  }

  getRoutingTable(): RouteInfo[] {
    return Array.from(this.routingTable.values());
  }

  getNetworkStats() {
    return {
      nodeId: this.nodeId,
      neighbors: this.neighbors.size,
      routes: this.routingTable.size,
      cachedMessages: this.messageCache.size,
      uptime: Date.now() - this.startTime
    };
  }

  private startTime = Date.now();
}

export default NoweeeMeshProtocol;
