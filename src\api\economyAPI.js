/**
 * API économique pour Nowee
 * Gère les portefeuilles, transactions, trocs et échanges
 */

import express from 'express';
import { dbService } from '../services/databaseServiceUnified.js';
import { EconomyService } from '../services/economyService.js';
import { BarterService } from '../services/barterService.js';

const router = express.Router();

/**
 * GET /api/economy/wallet/:phone
 * Récupère le portefeuille d'un utilisateur
 */
router.get('/wallet/:phone', async (req, res) => {
  try {
    const { phone } = req.params;
    const user = await dbService.getUserProfile(phone);
    const wallet = await EconomyService.getWallet(user.id);
    
    res.json({
      success: true,
      wallet: {
        nowee_coins: wallet.nowee_coins,
        time_credits: wallet.time_credits,
        total_earned: wallet.total_earned,
        total_spent: wallet.total_spent,
        reputation_bonus: wallet.reputation_bonus
      }
    });
    
  } catch (error) {
    console.error('Erreur API wallet:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur récupération portefeuille'
    });
  }
});

/**
 * POST /api/economy/transfer
 * Effectue un transfert de NoweeCoins
 */
router.post('/transfer', async (req, res) => {
  try {
    const { fromPhone, toPhone, amount, description } = req.body;
    
    if (!fromPhone || !toPhone || !amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Paramètres invalides'
      });
    }
    
    const fromUser = await dbService.getUserProfile(fromPhone);
    const toUser = await dbService.getUserProfile(toPhone);
    
    const transaction = await EconomyService.transferCoins(
      fromUser.id,
      toUser.id,
      parseFloat(amount),
      description || 'Transfert NoweeCoins'
    );
    
    res.json({
      success: true,
      message: 'Transfert effectué avec succès',
      transaction: {
        id: transaction.id,
        amount: transaction.nowee_coins,
        description: transaction.description,
        created_at: transaction.created_at
      }
    });
    
  } catch (error) {
    console.error('Erreur API transfer:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/economy/transactions/:phone
 * Récupère l'historique des transactions
 */
router.get('/transactions/:phone', async (req, res) => {
  try {
    const { phone } = req.params;
    const { limit = 20 } = req.query;
    
    const user = await dbService.getUserProfile(phone);
    const transactions = await EconomyService.getTransactionHistory(user.id, parseInt(limit));
    
    res.json({
      success: true,
      transactions: transactions.map(tx => ({
        id: tx.id,
        type: tx.transaction_type,
        amount: tx.nowee_coins,
        time_credits: tx.time_credits,
        description: tx.description,
        status: tx.status,
        created_at: tx.created_at,
        is_incoming: tx.to_user_id === user.id
      }))
    });
    
  } catch (error) {
    console.error('Erreur API transactions:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur récupération transactions'
    });
  }
});

/**
 * POST /api/economy/barter/propose
 * Crée une proposition de troc
 */
router.post('/barter/propose', async (req, res) => {
  try {
    const {
      proposerPhone,
      targetPhone,
      offeredResourceId,
      requestedResourceId,
      exchangeType,
      offeredCoins = 0,
      offeredTimeHours = 0,
      requestedCoins = 0,
      requestedTimeHours = 0,
      conditions = '',
      proposalMessage = ''
    } = req.body;
    
    if (!proposerPhone || !targetPhone || !offeredResourceId || !requestedResourceId) {
      return res.status(400).json({
        success: false,
        error: 'Paramètres requis manquants'
      });
    }
    
    const proposerUser = await dbService.getUserProfile(proposerPhone);
    const targetUser = await dbService.getUserProfile(targetPhone);
    
    const proposal = await BarterService.createBarterProposal(
      proposerUser.id,
      targetUser.id,
      {
        offeredResourceId,
        requestedResourceId,
        exchangeType: exchangeType || 'DIRECT_BARTER',
        offeredCoins: parseFloat(offeredCoins),
        offeredTimeHours: parseFloat(offeredTimeHours),
        requestedCoins: parseFloat(requestedCoins),
        requestedTimeHours: parseFloat(requestedTimeHours),
        conditions,
        proposalMessage
      }
    );
    
    res.json({
      success: true,
      message: 'Proposition de troc créée avec succès',
      proposal: {
        id: proposal.id,
        exchange_type: proposal.exchange_type,
        status: proposal.status,
        created_at: proposal.created_at,
        expires_at: proposal.expires_at
      }
    });
    
  } catch (error) {
    console.error('Erreur API barter propose:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/economy/barter/:proposalId/accept
 * Accepte une proposition de troc
 */
router.post('/barter/:proposalId/accept', async (req, res) => {
  try {
    const { proposalId } = req.params;
    const { acceptingPhone } = req.body;
    
    if (!acceptingPhone) {
      return res.status(400).json({
        success: false,
        error: 'Téléphone de l\'acceptant requis'
      });
    }
    
    const acceptingUser = await dbService.getUserProfile(acceptingPhone);
    const proposal = await BarterService.acceptBarterProposal(proposalId, acceptingUser.id);
    
    res.json({
      success: true,
      message: 'Proposition de troc acceptée avec succès',
      proposal: {
        id: proposal.id,
        status: proposal.status,
        completed_at: proposal.completed_at
      }
    });
    
  } catch (error) {
    console.error('Erreur API barter accept:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/economy/barter/:proposalId/reject
 * Rejette une proposition de troc
 */
router.post('/barter/:proposalId/reject', async (req, res) => {
  try {
    const { proposalId } = req.params;
    const { rejectingPhone, reason = '' } = req.body;
    
    if (!rejectingPhone) {
      return res.status(400).json({
        success: false,
        error: 'Téléphone du rejetant requis'
      });
    }
    
    const rejectingUser = await dbService.getUserProfile(rejectingPhone);
    const proposal = await BarterService.rejectBarterProposal(proposalId, rejectingUser.id, reason);
    
    res.json({
      success: true,
      message: 'Proposition de troc rejetée',
      proposal: {
        id: proposal.id,
        status: proposal.status
      }
    });
    
  } catch (error) {
    console.error('Erreur API barter reject:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/economy/barter/:phone
 * Récupère les propositions de troc d'un utilisateur
 */
router.get('/barter/:phone', async (req, res) => {
  try {
    const { phone } = req.params;
    const { type = 'all' } = req.query; // 'all', 'sent', 'received'
    
    const user = await dbService.getUserProfile(phone);
    const proposals = await BarterService.getUserBarterProposals(user.id, type);
    
    res.json({
      success: true,
      proposals: proposals.map(p => ({
        id: p.id,
        exchange_type: p.exchange_type,
        status: p.status,
        offered_coins: p.offered_coins,
        offered_time_hours: p.offered_time_hours,
        requested_coins: p.requested_coins,
        requested_time_hours: p.requested_time_hours,
        proposal_message: p.proposal_message,
        created_at: p.created_at,
        expires_at: p.expires_at,
        is_proposer: p.proposer_id === user.id
      }))
    });
    
  } catch (error) {
    console.error('Erreur API barter list:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur récupération propositions'
    });
  }
});

/**
 * GET /api/economy/barter/:phone/suggestions
 * Obtient des suggestions de troc pour un utilisateur
 */
router.get('/barter/:phone/suggestions', async (req, res) => {
  try {
    const { phone } = req.params;
    
    const user = await dbService.getUserProfile(phone);
    const suggestions = await BarterService.suggestBarterMatches(user.id);
    
    res.json({
      success: true,
      suggestions: suggestions.map(s => ({
        type: s.type,
        match_score: s.matchScore,
        user_need: {
          id: s.userNeed.id,
          title: s.userNeed.title,
          category: s.userNeed.category
        },
        offered_resource: {
          id: s.offeredResource.id,
          title: s.offeredResource.title,
          category: s.offeredResource.category
        },
        offerer_need: {
          id: s.offererNeed.id,
          title: s.offererNeed.title,
          category: s.offererNeed.category
        }
      }))
    });
    
  } catch (error) {
    console.error('Erreur API barter suggestions:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur récupération suggestions'
    });
  }
});

/**
 * POST /api/economy/service-cost
 * Calcule le coût d'un service
 */
router.post('/service-cost', async (req, res) => {
  try {
    const {
      baseRate = 50,
      durationHours = 1,
      urgency = 1,
      providerPhone,
      skillCategory
    } = req.body;
    
    let providerRating = 0;
    if (providerPhone) {
      const provider = await dbService.getUserProfile(providerPhone);
      providerRating = provider.rating || 0;
    }
    
    const cost = EconomyService.calculateServiceCost(
      parseFloat(baseRate),
      parseFloat(durationHours),
      parseInt(urgency),
      parseFloat(providerRating),
      skillCategory
    );
    
    res.json({
      success: true,
      cost: {
        total_coins: cost,
        base_rate: parseFloat(baseRate),
        duration_hours: parseFloat(durationHours),
        urgency_multiplier: urgency,
        provider_rating: providerRating,
        skill_category: skillCategory
      }
    });
    
  } catch (error) {
    console.error('Erreur API service cost:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur calcul coût service'
    });
  }
});

/**
 * GET /api/economy/stats
 * Récupère les statistiques économiques globales
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = await EconomyService.getEconomyStats();
    
    res.json({
      success: true,
      stats: {
        total_coins_in_circulation: stats.totalCoinsInCirculation,
        total_time_credits: stats.totalTimeCredits,
        total_transactions: stats.totalTransactions,
        average_wallet_balance: stats.averageWalletBalance
      }
    });
    
  } catch (error) {
    console.error('Erreur API economy stats:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur récupération statistiques'
    });
  }
});

/**
 * POST /api/economy/reward
 * Récompense un utilisateur (admin uniquement)
 */
router.post('/reward', async (req, res) => {
  try {
    const { phone, actionType, amount, description } = req.body;
    
    if (!phone || !actionType || !amount || !description) {
      return res.status(400).json({
        success: false,
        error: 'Paramètres requis manquants'
      });
    }
    
    const user = await dbService.getUserProfile(phone);
    await EconomyService.rewardUser(user.id, actionType, parseFloat(amount), description);
    
    res.json({
      success: true,
      message: `Récompense de ${amount} NoweeCoins accordée`
    });
    
  } catch (error) {
    console.error('Erreur API reward:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur attribution récompense'
    });
  }
});

export default router;
