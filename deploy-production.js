#!/usr/bin/env node

/**
 * Script de déploiement automatisé pour Nowee en production
 * Configure Supabase, déploie l'API et le frontend
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.bold}${colors.cyan}\n🚀 ${msg}${colors.reset}`),
  step: (msg) => console.log(`${colors.magenta}📋 ${msg}${colors.reset}`)
};

// Configuration de production
const PRODUCTION_CONFIG = {
  supabase: {
    url: process.env.SUPABASE_URL || 'https://your-project.supabase.co',
    anonKey: process.env.SUPABASE_ANON_KEY || 'your-anon-key',
    serviceKey: process.env.SUPABASE_SERVICE_KEY || 'your-service-key'
  },
  heroku: {
    appName: process.env.HEROKU_APP_NAME || 'nowee-api-prod',
    region: 'us'
  },
  vercel: {
    projectName: 'nowee-frontend',
    framework: 'nextjs'
  },
  openai: {
    apiKey: process.env.OPENAI_API_KEY || 'your-openai-key'
  },
  twilio: {
    accountSid: process.env.TWILIO_ACCOUNT_SID || 'your-twilio-sid',
    authToken: process.env.TWILIO_AUTH_TOKEN || 'your-twilio-token',
    phoneNumber: process.env.TWILIO_PHONE_NUMBER || '+**********'
  }
};

function checkPrerequisites() {
  log.title('Vérification des prérequis de déploiement');
  
  const tools = [
    { name: 'Node.js', command: 'node --version' },
    { name: 'NPM', command: 'npm --version' },
    { name: 'Git', command: 'git --version' }
  ];
  
  let allPresent = true;
  
  for (const tool of tools) {
    try {
      const version = execSync(tool.command, { encoding: 'utf8' }).trim();
      log.success(`${tool.name}: ${version}`);
    } catch (error) {
      log.error(`${tool.name} non installé`);
      allPresent = false;
    }
  }
  
  // Vérifier les variables d'environnement critiques
  const requiredEnvVars = [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'OPENAI_API_KEY'
  ];
  
  log.step('Vérification des variables d\'environnement...');
  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      log.success(`${envVar}: Configuré`);
    } else {
      log.warning(`${envVar}: Non configuré (utilisation valeur par défaut)`);
    }
  }
  
  return allPresent;
}

function createProductionEnvFile() {
  log.title('Création du fichier .env de production');
  
  const envContent = `# Configuration de production Nowee
# Généré automatiquement le ${new Date().toISOString()}

# Base de données Supabase
SUPABASE_URL=${PRODUCTION_CONFIG.supabase.url}
SUPABASE_ANON_KEY=${PRODUCTION_CONFIG.supabase.anonKey}
SUPABASE_SERVICE_KEY=${PRODUCTION_CONFIG.supabase.serviceKey}

# API OpenAI
OPENAI_API_KEY=${PRODUCTION_CONFIG.openai.apiKey}

# Twilio WhatsApp
TWILIO_ACCOUNT_SID=${PRODUCTION_CONFIG.twilio.accountSid}
TWILIO_AUTH_TOKEN=${PRODUCTION_CONFIG.twilio.authToken}
TWILIO_PHONE_NUMBER=${PRODUCTION_CONFIG.twilio.phoneNumber}

# Configuration serveur
NODE_ENV=production
PORT=3000
JWT_SECRET=nowee-super-secret-key-production-2025

# URLs de production
FRONTEND_URL=https://nowee-app.vercel.app
API_URL=https://${PRODUCTION_CONFIG.heroku.appName}.herokuapp.com

# Fonctionnalités
ENABLE_ECONOMY=true
ENABLE_BARTER=true
ENABLE_VOICE=true
ENABLE_MAPS=true
ENABLE_NOTIFICATIONS=true

# Monitoring
LOG_LEVEL=info
ENABLE_ANALYTICS=true
SENTRY_DSN=your-sentry-dsn

# Limites de production
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=900000
MAX_FILE_SIZE=********
`;

  fs.writeFileSync('.env.production', envContent);
  log.success('Fichier .env.production créé');
  
  // Créer aussi pour le backend
  if (!fs.existsSync('backend')) {
    fs.mkdirSync('backend');
  }
  fs.writeFileSync('backend/.env.production', envContent);
  log.success('Fichier backend/.env.production créé');
}

function setupSupabaseDatabase() {
  log.title('Configuration de la base de données Supabase');
  
  try {
    // Lire le script SQL de configuration
    const sqlScript = fs.readFileSync('database/supabase-setup.sql', 'utf8');
    
    log.step('Script SQL lu avec succès');
    log.info(`Taille du script: ${(sqlScript.length / 1024).toFixed(2)} KB`);
    
    // Créer un script d'exécution pour Supabase
    const supabaseScript = `
-- Script de déploiement Supabase pour Nowee
-- Exécuté le ${new Date().toISOString()}

${sqlScript}

-- Vérification finale
SELECT 'Configuration Supabase terminée' as status;
`;
    
    fs.writeFileSync('database/deploy-supabase.sql', supabaseScript);
    log.success('Script de déploiement Supabase créé');
    
    // Instructions pour l'utilisateur
    log.step('Instructions pour configurer Supabase:');
    console.log(`
${colors.cyan}📋 ÉTAPES SUPABASE:${colors.reset}
1. Aller sur https://supabase.com/dashboard
2. Créer un nouveau projet "nowee-production"
3. Aller dans SQL Editor
4. Copier/coller le contenu de database/deploy-supabase.sql
5. Exécuter le script
6. Configurer les variables d'environnement avec les clés du projet
`);
    
    return true;
  } catch (error) {
    log.error(`Erreur configuration Supabase: ${error.message}`);
    return false;
  }
}

function prepareBackendDeployment() {
  log.title('Préparation du déploiement backend');
  
  try {
    // Créer package.json optimisé pour production
    const productionPackage = {
      "name": "nowee-api-production",
      "version": "1.0.0",
      "description": "API de production pour Nowee - Entraide locale révolutionnaire",
      "main": "server.js",
      "scripts": {
        "start": "node server.js",
        "dev": "nodemon server.js",
        "build": "echo 'Build completed'",
        "test": "jest",
        "deploy": "git push heroku main"
      },
      "engines": {
        "node": "18.x",
        "npm": "9.x"
      },
      "dependencies": {
        "express": "^4.18.2",
        "cors": "^2.8.5",
        "helmet": "^7.1.0",
        "compression": "^1.7.4",
        "express-rate-limit": "^7.1.5",
        "@supabase/supabase-js": "^2.38.4",
        "openai": "^4.20.1",
        "twilio": "^4.19.0",
        "axios": "^1.6.2",
        "bcryptjs": "^2.4.3",
        "jsonwebtoken": "^9.0.2",
        "multer": "^1.4.5-lts.1",
        "socket.io": "^4.7.4",
        "winston": "^3.11.0",
        "dotenv": "^16.3.1"
      },
      "devDependencies": {
        "nodemon": "^3.0.2",
        "jest": "^29.7.0"
      },
      "keywords": [
        "nowee",
        "entraide",
        "local",
        "api",
        "production",
        "economie",
        "troc"
      ],
      "author": "Nowee Team",
      "license": "MIT"
    };
    
    fs.writeFileSync('backend/package.json', JSON.stringify(productionPackage, null, 2));
    log.success('Package.json de production créé');
    
    // Créer Procfile pour Heroku
    const procfile = `web: node server.js
worker: node workers/background-jobs.js`;
    
    fs.writeFileSync('backend/Procfile', procfile);
    log.success('Procfile Heroku créé');
    
    // Créer app.json pour Heroku
    const appJson = {
      "name": "nowee-api-production",
      "description": "API de production pour Nowee",
      "repository": "https://github.com/your-username/nowee",
      "logo": "https://nowee-app.vercel.app/logo.png",
      "keywords": ["nowee", "api", "entraide", "local"],
      "env": {
        "NODE_ENV": {
          "description": "Environment",
          "value": "production"
        },
        "SUPABASE_URL": {
          "description": "URL de la base de données Supabase"
        },
        "SUPABASE_ANON_KEY": {
          "description": "Clé anonyme Supabase"
        },
        "OPENAI_API_KEY": {
          "description": "Clé API OpenAI"
        },
        "TWILIO_ACCOUNT_SID": {
          "description": "SID du compte Twilio"
        },
        "TWILIO_AUTH_TOKEN": {
          "description": "Token d'authentification Twilio"
        }
      },
      "formation": {
        "web": {
          "quantity": 1,
          "size": "basic"
        }
      },
      "addons": [
        "heroku-redis:mini"
      ],
      "buildpacks": [
        {
          "url": "heroku/nodejs"
        }
      ]
    };
    
    fs.writeFileSync('backend/app.json', JSON.stringify(appJson, null, 2));
    log.success('Configuration Heroku créée');
    
    return true;
  } catch (error) {
    log.error(`Erreur préparation backend: ${error.message}`);
    return false;
  }
}

function createDeploymentScripts() {
  log.title('Création des scripts de déploiement');
  
  // Script de déploiement Heroku
  const herokuDeploy = `#!/bin/bash

# Script de déploiement Heroku pour Nowee API
echo "🚀 Déploiement Nowee API sur Heroku..."

# Vérifier Heroku CLI
if ! command -v heroku &> /dev/null; then
    echo "❌ Heroku CLI non installé"
    echo "💡 Installer depuis: https://devcenter.heroku.com/articles/heroku-cli"
    exit 1
fi

# Créer l'application Heroku
echo "📱 Création de l'application Heroku..."
heroku create ${PRODUCTION_CONFIG.heroku.appName} --region ${PRODUCTION_CONFIG.heroku.region}

# Configurer les variables d'environnement
echo "🔧 Configuration des variables d'environnement..."
heroku config:set NODE_ENV=production --app ${PRODUCTION_CONFIG.heroku.appName}
heroku config:set SUPABASE_URL=\${SUPABASE_URL} --app ${PRODUCTION_CONFIG.heroku.appName}
heroku config:set SUPABASE_ANON_KEY=\${SUPABASE_ANON_KEY} --app ${PRODUCTION_CONFIG.heroku.appName}
heroku config:set OPENAI_API_KEY=\${OPENAI_API_KEY} --app ${PRODUCTION_CONFIG.heroku.appName}
heroku config:set TWILIO_ACCOUNT_SID=\${TWILIO_ACCOUNT_SID} --app ${PRODUCTION_CONFIG.heroku.appName}
heroku config:set TWILIO_AUTH_TOKEN=\${TWILIO_AUTH_TOKEN} --app ${PRODUCTION_CONFIG.heroku.appName}

# Ajouter Redis
echo "🔴 Ajout de Redis..."
heroku addons:create heroku-redis:mini --app ${PRODUCTION_CONFIG.heroku.appName}

# Déployer
echo "🚀 Déploiement en cours..."
git add .
git commit -m "Deploy to production"
git push heroku main

# Ouvrir l'application
heroku open --app ${PRODUCTION_CONFIG.heroku.appName}

echo "✅ Déploiement Heroku terminé !"
`;

  fs.writeFileSync('deploy-heroku.sh', herokuDeploy);
  log.success('Script de déploiement Heroku créé');
  
  // Script de déploiement Vercel
  const vercelDeploy = `#!/bin/bash

# Script de déploiement Vercel pour Nowee Frontend
echo "🚀 Déploiement Nowee Frontend sur Vercel..."

# Vérifier Vercel CLI
if ! command -v vercel &> /dev/null; then
    echo "📦 Installation de Vercel CLI..."
    npm install -g vercel
fi

# Déployer
echo "🌐 Déploiement en cours..."
cd frontend
vercel --prod

echo "✅ Déploiement Vercel terminé !"
`;

  fs.writeFileSync('deploy-vercel.sh', vercelDeploy);
  log.success('Script de déploiement Vercel créé');
  
  // Rendre les scripts exécutables (Unix)
  try {
    execSync('chmod +x deploy-heroku.sh deploy-vercel.sh');
    log.success('Scripts rendus exécutables');
  } catch (error) {
    log.warning('Impossible de rendre les scripts exécutables (normal sur Windows)');
  }
}

function generateProductionDocumentation() {
  log.title('Génération de la documentation de production');
  
  const prodDocs = `# 🚀 Nowee - Guide de Déploiement Production

## 📋 Résumé du Déploiement

**Date:** ${new Date().toLocaleDateString('fr-FR')}  
**Version:** 1.0.0  
**Environnement:** Production

## 🏗️ Architecture de Production

\`\`\`
🌐 Frontend (Vercel)
├── Next.js Application
├── Interface Web Responsive
└── PWA Mobile

🔗 API Backend (Heroku)
├── Node.js + Express
├── Authentification JWT
├── APIs Économiques
└── WebSocket Real-time

💾 Base de Données (Supabase)
├── PostgreSQL
├── Authentification
├── Storage Fichiers
└── Real-time Subscriptions

🤖 Services Externes
├── OpenAI (IA)
├── Twilio (WhatsApp)
├── Google Maps
└── Notifications Push
\`\`\`

## 🔧 Configuration Requise

### Variables d'Environnement
\`\`\`bash
# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key

# OpenAI
OPENAI_API_KEY=sk-your-openai-key

# Twilio
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
TWILIO_PHONE_NUMBER=+**********
\`\`\`

## 🚀 Étapes de Déploiement

### 1. Base de Données Supabase
1. Créer un projet sur https://supabase.com
2. Exécuter \`database/deploy-supabase.sql\`
3. Configurer les variables d'environnement

### 2. API Backend (Heroku)
\`\`\`bash
# Déploiement automatique
./deploy-heroku.sh

# Ou manuel
heroku create nowee-api-prod
git push heroku main
\`\`\`

### 3. Frontend (Vercel)
\`\`\`bash
# Déploiement automatique
./deploy-vercel.sh

# Ou manuel
cd frontend
vercel --prod
\`\`\`

## 🧪 Tests de Production

### URLs de Production
- **Frontend:** https://nowee-app.vercel.app
- **API:** https://nowee-api-prod.herokuapp.com
- **Documentation:** https://nowee-docs.vercel.app

### Tests Critiques
- [ ] Authentification utilisateur
- [ ] Création de besoins
- [ ] Système économique (NoweeCoins)
- [ ] Troc et échanges
- [ ] Géolocalisation
- [ ] Notifications WhatsApp
- [ ] Interface mobile

## 📊 Monitoring et Maintenance

### Métriques à Surveiller
- Temps de réponse API
- Utilisation base de données
- Erreurs applicatives
- Satisfaction utilisateur

### Logs et Debug
\`\`\`bash
# Logs Heroku
heroku logs --tail --app nowee-api-prod

# Logs Vercel
vercel logs
\`\`\`

## 🔒 Sécurité

### Mesures Implémentées
- HTTPS obligatoire
- Rate limiting
- Validation des entrées
- Authentification JWT
- CORS configuré

## 📞 Support

### Contacts
- **Technique:** <EMAIL>
- **Support:** <EMAIL>
- **Urgences:** +221 XX XXX XX XX

---

**🎉 Nowee est maintenant en production ! 🎉**
`;

  fs.writeFileSync('PRODUCTION-GUIDE.md', prodDocs);
  log.success('Documentation de production créée');
}

async function main() {
  try {
    log.title('🚀 DÉPLOIEMENT PRODUCTION NOWEE 🚀');
    
    // Vérifications préalables
    if (!checkPrerequisites()) {
      log.error('Prérequis manquants, arrêt du déploiement');
      process.exit(1);
    }
    
    // Configuration
    createProductionEnvFile();
    
    // Base de données
    if (!setupSupabaseDatabase()) {
      log.error('Erreur configuration Supabase');
      process.exit(1);
    }
    
    // Backend
    if (!prepareBackendDeployment()) {
      log.error('Erreur préparation backend');
      process.exit(1);
    }
    
    // Scripts de déploiement
    createDeploymentScripts();
    
    // Documentation
    generateProductionDocumentation();
    
    // Résumé final
    log.title('🎉 Préparation du déploiement terminée !');
    
    console.log(`
${colors.bold}${colors.green}✅ NOWEE PRÊT POUR LA PRODUCTION !${colors.reset}

${colors.cyan}📋 PROCHAINES ÉTAPES:${colors.reset}
1. 💾 Configurer Supabase (voir PRODUCTION-GUIDE.md)
2. 🚀 Déployer l'API: ./deploy-heroku.sh
3. 🌐 Déployer le frontend: ./deploy-vercel.sh
4. 🧪 Tester en production
5. 📊 Configurer le monitoring

${colors.yellow}📁 FICHIERS CRÉÉS:${colors.reset}
- .env.production
- backend/package.json
- backend/Procfile
- backend/app.json
- deploy-heroku.sh
- deploy-vercel.sh
- PRODUCTION-GUIDE.md

${colors.green}🎯 Le système Nowee est prêt à révolutionner l'entraide locale !${colors.reset}
`);
    
  } catch (error) {
    log.error(`Erreur déploiement: ${error.message}`);
    process.exit(1);
  }
}

main();
