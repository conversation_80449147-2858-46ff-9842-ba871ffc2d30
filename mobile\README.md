# 📱 Nowee Mobile - Application React Native

Application mobile native pour Nowee, l'assistant d'entraide locale en temps réel.

## 🎯 Fonctionnalités

### ✅ Fonctionnalités principales
- **Chat intelligent** avec IA contextuelle
- **Géolocalisation** pour l'aide de proximité
- **Notifications push** pour les opportunités d'aide
- **Interface vocale** pour l'accessibilité
- **Mode hors-ligne** pour les zones à faible connectivité
- **Système de réputation** communautaire
- **Matching intelligent** besoins/offres

### 💰 Fonctionnalités économiques (NOUVEAU!)
- **Portefeuille NoweeCoins** avec interface visuelle
- **Système de troc avancé** avec drag & drop
- **Transferts instantanés** entre utilisateurs
- **Carte interactive** avec géolocalisation des ressources
- **Crédits temps** comme monnaie d'échange
- **Statistiques économiques** en temps réel

### 🔮 Fonctionnalités avancées (roadmap)
- **R<PERSON>alité augmentée** pour localiser l'aide
- **Crypto-monnaies** et blockchain
- **Mode mesh** pour communication sans internet
- **Multi-langues** avec traduction automatique

## 🛠️ Installation

### Prérequis
- Node.js >= 16
- React Native CLI
- Android Studio (pour Android)
- Xcode (pour iOS, macOS uniquement)

### Configuration initiale

1. **Cloner le projet**
```bash
git clone https://github.com/votre-username/nowee.git
cd nowee/mobile
```

2. **Installer les dépendances**
```bash
npm install

# iOS uniquement
cd ios && pod install && cd ..
```

3. **Configuration des variables d'environnement**
```bash
# Créer le fichier de configuration
cp .env.example .env

# Éditer avec vos valeurs
# API_BASE_URL=http://localhost:3000
# GOOGLE_MAPS_API_KEY=your-google-maps-key
# SENTRY_DSN=your-sentry-dsn
```

4. **Démarrer le serveur Metro**
```bash
npm start
```

5. **Lancer l'application**
```bash
# Android
npm run android

# iOS
npm run ios
```

## 🏗️ Architecture

```
mobile/
├── src/
│   ├── components/          # Composants réutilisables
│   │   ├── NeedCard.tsx
│   │   ├── QuickActionButton.tsx
│   │   └── StatsCard.tsx
│   ├── screens/             # Écrans de l'application
│   │   ├── HomeScreen.tsx
│   │   ├── ChatScreen.tsx
│   │   ├── MapScreen.tsx
│   │   ├── ProfileScreen.tsx
│   │   └── NeedsScreen.tsx
│   ├── services/            # Services et logique métier
│   │   ├── ApiService.ts
│   │   ├── AuthService.tsx
│   │   ├── LocationService.tsx
│   │   ├── NotificationService.ts
│   │   └── ThemeService.tsx
│   ├── utils/               # Utilitaires
│   ├── types/               # Types TypeScript
│   └── assets/              # Images, fonts, etc.
├── android/                 # Code Android natif
├── ios/                     # Code iOS natif
└── App.tsx                  # Point d'entrée
```

## 🎨 Design System

### Couleurs
```typescript
const Colors = {
  primary: '#2E7D32',      // Vert principal
  secondary: '#4CAF50',    // Vert secondaire
  accent: '#FF9800',       // Orange accent
  background: '#FFFFFF',   // Fond clair
  surface: '#F5F5F5',     // Surface
  text: '#212121',         // Texte principal
  textSecondary: '#757575', // Texte secondaire
  error: '#F44336',        // Erreur
  success: '#4CAF50',      // Succès
  warning: '#FF9800',      // Avertissement
};
```

### Typographie
- **Titre principal** : 24px, Bold
- **Titre section** : 20px, Bold
- **Corps de texte** : 16px, Regular
- **Texte secondaire** : 14px, Regular
- **Caption** : 12px, Regular

### Espacements
- **Petit** : 8px
- **Moyen** : 16px
- **Grand** : 24px
- **Extra-grand** : 32px

## 📱 Écrans principaux

### 🏠 HomeScreen
- Salutation personnalisée avec localisation
- Actions rapides (besoin express, offre d'aide, urgence, vocal)
- Statistiques personnelles (aide donnée/reçue, note)
- Besoins récents à proximité
- Statistiques de la communauté

### 💬 ChatScreen
- Interface de chat avec l'IA Nowee
- Reconnaissance vocale intégrée
- Historique des conversations
- Suggestions contextuelles
- Mode hors-ligne avec synchronisation

### 🗺️ MapScreen
- Carte interactive avec besoins/offres géolocalisés
- Filtres par type, urgence, distance
- Navigation vers les lieux d'aide
- Mode réalité augmentée (futur)

### 👤 ProfileScreen
- Profil utilisateur complet
- Historique des aides données/reçues
- Système de réputation et badges
- Préférences et paramètres
- Statistiques personnelles

### 🙋‍♂️ NeedsScreen
- Liste des besoins actifs
- Création de nouveaux besoins
- Suivi des demandes en cours
- Correspondances proposées

## 🔧 Services

### ApiService
Gère toutes les communications avec le backend :
- Authentification et gestion des tokens
- CRUD pour besoins, offres, profils
- Chat avec l'IA
- Gestion des erreurs et retry automatique

### AuthService
Gestion de l'authentification :
- Connexion par SMS
- Stockage sécurisé des tokens
- Gestion des sessions
- Déconnexion automatique

### LocationService
Service de géolocalisation :
- Localisation GPS précise
- Géocodage inverse (coordonnées → adresse)
- Calcul de distances
- Permissions et gestion d'erreurs

### NotificationService
Notifications push :
- Configuration Firebase/APNs
- Notifications locales et distantes
- Gestion des permissions
- Actions rapides depuis les notifications

### ThemeService
Gestion des thèmes :
- Mode clair/sombre
- Couleurs adaptatives
- Préférences utilisateur
- Accessibilité

## 🧪 Tests

### Tests unitaires
```bash
npm test
```

### Tests d'intégration
```bash
npm run test:integration
```

### Tests E2E (Detox)
```bash
npm run test:e2e:ios
npm run test:e2e:android
```

## 📦 Build et déploiement

### Build de développement
```bash
# Android
npm run build:android:debug

# iOS
npm run build:ios:debug
```

### Build de production
```bash
# Android
npm run build:android:release

# iOS
npm run build:ios:release
```

### Déploiement
```bash
# Google Play Store
npm run deploy:android

# Apple App Store
npm run deploy:ios
```

## 🔒 Sécurité

### Stockage sécurisé
- Tokens d'authentification chiffrés
- Données sensibles dans Keychain/Keystore
- Pas de données critiques en plain text

### Communications
- HTTPS obligatoire
- Certificate pinning
- Validation des certificats SSL

### Permissions
- Demande de permissions explicite
- Permissions minimales nécessaires
- Gestion gracieuse des refus

## 🌍 Internationalisation

### Langues supportées
- 🇫🇷 Français (par défaut)
- 🇬🇧 Anglais
- 🇪🇸 Espagnol
- 🇵🇹 Portugais
- 🇦🇷 Arabe (futur)

### Configuration
```bash
# Extraire les chaînes à traduire
npm run i18n:extract

# Générer les traductions
npm run i18n:generate
```

## 📊 Analytics et monitoring

### Outils intégrés
- **Sentry** : Monitoring d'erreurs
- **Firebase Analytics** : Analytics d'usage
- **Flipper** : Debugging (développement)

### Métriques suivies
- Temps de réponse de l'API
- Taux de crash
- Engagement utilisateur
- Conversion des besoins en aide

## 🤝 Contribution

### Workflow de développement
1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/amazing-feature`)
3. Commit les changements (`git commit -m 'Add amazing feature'`)
4. Push vers la branche (`git push origin feature/amazing-feature`)
5. Ouvrir une Pull Request

### Standards de code
- **ESLint** pour la qualité du code
- **Prettier** pour le formatage
- **TypeScript** obligatoire
- Tests unitaires pour les nouvelles fonctionnalités

## 📄 Licence

MIT License - voir le fichier [LICENSE](../LICENSE) pour plus de détails.

## 💬 Support

- **Email** : <EMAIL>
- **Discord** : [Communauté Nowee](https://discord.gg/nowee)
- **Issues** : [GitHub Issues](https://github.com/votre-username/nowee/issues)

---

**Nowee Mobile** - *L'entraide dans votre poche* 📱❤️
