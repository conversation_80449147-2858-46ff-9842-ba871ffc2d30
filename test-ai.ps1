# Test de l'IA Nowee
Write-Host "🤖 Test de l'Intelligence Artificielle Nowee" -ForegroundColor Blue

$body = @{
    message = "Salut Nowee ! J'ai besoin d'aide pour déménager ce weekend à Dakar. Peux-tu m'aider ?"
    phone = "+221701234567"
    context = "demande_aide_demenagement"
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/api/chat/ai" -Method POST -Body $body -ContentType "application/json"
    
    Write-Host "✅ Réponse reçue:" -ForegroundColor Green
    $result = $response.Content | ConvertFrom-Json
    Write-Host "🤖 Nowee: $($result.response)" -ForegroundColor Cyan
    Write-Host "📊 Tokens utilisés: $($result.tokens_used)" -ForegroundColor Yellow
    Write-Host "🧠 Modèle: $($result.model)" -ForegroundColor Magenta
} catch {
    Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
}
