/**
 * Service de matching intelligent pour Nowee
 * Connecte automatiquement les besoins aux offres disponibles
 */

import { dbService } from './databaseServiceUnified.js';
import { calculateDistance } from '../utils/locationUtils.js';
import { NotificationService } from './notificationService.js';

// Configuration du matching
const MATCHING_CONFIG = {
  maxDistance: 10, // km
  maxMatches: 5,
  urgencyMultiplier: {
    1: 0.5,  // Faible urgence
    2: 1.0,  // Normale
    3: 1.5,  // Urgente
    4: 2.0   // Critique
  },
  reputationWeight: 0.3,
  distanceWeight: 0.4,
  availabilityWeight: 0.3
};

/**
 * Service de matching principal
 */
export class MatchingService {
  
  /**
   * Trouve des correspondances pour un besoin donné
   */
  async findMatches(needId) {
    try {
      const need = await dbService.prisma.need.findUnique({
        where: { id: needId },
        include: {
          user: true
        }
      });

      if (!need || need.status !== 'OPEN') {
        return [];
      }

      // Rechercher les offres compatibles
      const compatibleOffers = await this.findCompatibleOffers(need);
      
      // Calculer les scores de matching
      const scoredMatches = await this.calculateMatchingScores(need, compatibleOffers);
      
      // Trier par score et limiter
      const bestMatches = scoredMatches
        .sort((a, b) => b.score - a.score)
        .slice(0, MATCHING_CONFIG.maxMatches);

      // Créer les correspondances en base
      const matches = await this.createMatches(need, bestMatches);
      
      // Envoyer les notifications
      await this.sendMatchNotifications(matches);
      
      return matches;
    } catch (error) {
      console.error('Erreur lors du matching:', error);
      throw error;
    }
  }

  /**
   * Trouve les offres compatibles avec un besoin
   */
  async findCompatibleOffers(need) {
    const offers = await dbService.prisma.offer.findMany({
      where: {
        type: need.type,
        status: 'AVAILABLE',
        userId: { not: need.userId } // Pas le même utilisateur
      },
      include: {
        user: true
      }
    });

    // Filtrer par distance si géolocalisation disponible
    if (need.location && need.location.coordinates) {
      return offers.filter(offer => {
        if (!offer.location || !offer.location.coordinates) return true;
        
        const distance = calculateDistance(
          need.location.coordinates.latitude,
          need.location.coordinates.longitude,
          offer.location.coordinates.latitude,
          offer.location.coordinates.longitude
        );
        
        return distance <= MATCHING_CONFIG.maxDistance;
      });
    }

    return offers;
  }

  /**
   * Calcule les scores de matching
   */
  async calculateMatchingScores(need, offers) {
    const scoredOffers = [];

    for (const offer of offers) {
      let score = 0;
      const factors = {};

      // 1. Score de distance
      if (need.location && offer.location && 
          need.location.coordinates && offer.location.coordinates) {
        const distance = calculateDistance(
          need.location.coordinates.latitude,
          need.location.coordinates.longitude,
          offer.location.coordinates.latitude,
          offer.location.coordinates.longitude
        );
        
        // Score inversement proportionnel à la distance
        factors.distance = Math.max(0, (MATCHING_CONFIG.maxDistance - distance) / MATCHING_CONFIG.maxDistance);
        score += factors.distance * MATCHING_CONFIG.distanceWeight;
      } else {
        factors.distance = 0.5; // Score neutre si pas de géolocalisation
        score += factors.distance * MATCHING_CONFIG.distanceWeight;
      }

      // 2. Score de réputation
      factors.reputation = offer.user.rating / 5.0; // Normaliser sur 5
      score += factors.reputation * MATCHING_CONFIG.reputationWeight;

      // 3. Score de disponibilité
      factors.availability = this.calculateAvailabilityScore(offer, need);
      score += factors.availability * MATCHING_CONFIG.availabilityWeight;

      // 4. Bonus d'urgence
      const urgencyBonus = MATCHING_CONFIG.urgencyMultiplier[need.urgency] || 1.0;
      score *= urgencyBonus;

      // 5. Bonus de compatibilité textuelle
      const textCompatibility = this.calculateTextCompatibility(need, offer);
      score *= (1 + textCompatibility * 0.2);

      scoredOffers.push({
        offer,
        score: Math.round(score * 100) / 100,
        factors: {
          ...factors,
          urgencyBonus,
          textCompatibility
        }
      });
    }

    return scoredOffers;
  }

  /**
   * Calcule le score de disponibilité
   */
  calculateAvailabilityScore(offer, need) {
    const availability = offer.availability || {};
    const now = new Date();
    const currentHour = now.getHours();
    const isWeekend = now.getDay() === 0 || now.getDay() === 6;

    let score = 0.5; // Score de base

    // Vérifier les créneaux horaires
    if (availability.hours) {
      const [startHour, endHour] = availability.hours.split('-').map(h => parseInt(h));
      if (currentHour >= startHour && currentHour <= endHour) {
        score += 0.3;
      }
    }

    // Vérifier les jours
    if (availability.weekdays && !isWeekend) {
      score += 0.2;
    }
    if (availability.weekends && isWeekend) {
      score += 0.2;
    }

    // Vérifier l'urgence vs délai de préavis
    if (need.urgency >= 3 && !availability.advance_notice) {
      score += 0.2; // Bonus pour disponibilité immédiate en cas d'urgence
    }

    return Math.min(1.0, score);
  }

  /**
   * Calcule la compatibilité textuelle entre besoin et offre
   */
  calculateTextCompatibility(need, offer) {
    const needWords = need.description.toLowerCase().split(/\s+/);
    const offerWords = offer.description.toLowerCase().split(/\s+/);
    
    let commonWords = 0;
    for (const word of needWords) {
      if (word.length > 3 && offerWords.includes(word)) {
        commonWords++;
      }
    }
    
    return commonWords / Math.max(needWords.length, offerWords.length);
  }

  /**
   * Crée les correspondances en base de données
   */
  async createMatches(need, scoredMatches) {
    const matches = [];

    for (const { offer, score, factors } of scoredMatches) {
      try {
        const match = await dbService.prisma.match.create({
          data: {
            needId: need.id,
            offerId: offer.id,
            userId: need.userId,
            status: 'PROPOSED',
            metadata: {
              score,
              factors,
              createdBy: 'auto_matching'
            }
          },
          include: {
            need: { include: { user: true } },
            offer: { include: { user: true } }
          }
        });

        matches.push(match);
      } catch (error) {
        console.error('Erreur création match:', error);
      }
    }

    return matches;
  }

  /**
   * Envoie les notifications de matching
   */
  async sendMatchNotifications(matches) {
    for (const match of matches) {
      try {
        // Notification au demandeur
        await NotificationService.sendMatchNotification(
          match.need.user,
          'match_found',
          {
            match,
            message: `🎯 Quelqu'un peut vous aider ! ${match.offer.user.name || 'Un utilisateur'} propose : "${match.offer.title}"`
          }
        );

        // Notification à l'offreur
        await NotificationService.sendMatchNotification(
          match.offer.user,
          'help_request',
          {
            match,
            message: `🙋‍♂️ ${match.need.user.name || 'Quelqu\'un'} a besoin d'aide : "${match.need.title}"`
          }
        );

        // Log de l'événement
        await dbService.logEvent('match_created', match.userId, {
          matchId: match.id,
          needType: match.need.type,
          score: match.metadata?.score
        });

      } catch (error) {
        console.error('Erreur envoi notification:', error);
      }
    }
  }

  /**
   * Traite automatiquement les nouveaux besoins
   */
  async processNewNeed(needId) {
    try {
      console.log(`🔍 Recherche de correspondances pour le besoin ${needId}`);
      
      const matches = await this.findMatches(needId);
      
      console.log(`✅ ${matches.length} correspondances trouvées et notifiées`);
      
      return matches;
    } catch (error) {
      console.error('Erreur traitement nouveau besoin:', error);
      throw error;
    }
  }

  /**
   * Traite automatiquement les nouvelles offres
   */
  async processNewOffer(offerId) {
    try {
      console.log(`🔍 Recherche de besoins compatibles pour l'offre ${offerId}`);
      
      // Trouver les besoins ouverts compatibles
      const offer = await dbService.prisma.offer.findUnique({
        where: { id: offerId },
        include: { user: true }
      });

      if (!offer) return [];

      const compatibleNeeds = await dbService.prisma.need.findMany({
        where: {
          type: offer.type,
          status: 'OPEN',
          userId: { not: offer.userId }
        },
        include: { user: true }
      });

      const matches = [];
      for (const need of compatibleNeeds) {
        const matchResult = await this.findMatches(need.id);
        matches.push(...matchResult);
      }

      console.log(`✅ ${matches.length} nouvelles correspondances créées`);
      
      return matches;
    } catch (error) {
      console.error('Erreur traitement nouvelle offre:', error);
      throw error;
    }
  }

  /**
   * Accepte une correspondance
   */
  async acceptMatch(matchId, userId) {
    try {
      const match = await dbService.updateMatchStatus(matchId, 'ACCEPTED');
      
      // Notifier les parties
      await NotificationService.sendMatchNotification(
        match.need.user,
        'match_accepted',
        {
          match,
          message: `✅ ${match.offer.user.name} a accepté de vous aider !`
        }
      );

      await NotificationService.sendMatchNotification(
        match.offer.user,
        'match_accepted',
        {
          match,
          message: `🤝 Votre aide a été acceptée par ${match.need.user.name} !`
        }
      );

      // Log de l'événement
      await dbService.logEvent('match_accepted', userId, { matchId });

      return match;
    } catch (error) {
      console.error('Erreur acceptation match:', error);
      throw error;
    }
  }

  /**
   * Rejette une correspondance
   */
  async rejectMatch(matchId, userId, reason = null) {
    try {
      const match = await dbService.updateMatchStatus(matchId, 'REJECTED');
      
      // Log de l'événement
      await dbService.logEvent('match_rejected', userId, { matchId, reason });

      return match;
    } catch (error) {
      console.error('Erreur rejet match:', error);
      throw error;
    }
  }

  /**
   * Complète une correspondance avec évaluation
   */
  async completeMatch(matchId, userId, rating, feedback) {
    try {
      const match = await dbService.updateMatchStatus(matchId, 'COMPLETED', rating, feedback);
      
      // Mettre à jour les réputations
      await this.updateUserReputations(match, rating);
      
      // Notifier la completion
      await NotificationService.sendMatchNotification(
        match.need.user.id === userId ? match.offer.user : match.need.user,
        'match_completed',
        {
          match,
          message: `🎉 L'aide a été marquée comme terminée ! Merci pour votre participation.`
        }
      );

      // Log de l'événement
      await dbService.logEvent('match_completed', userId, { matchId, rating });

      return match;
    } catch (error) {
      console.error('Erreur completion match:', error);
      throw error;
    }
  }

  /**
   * Met à jour les réputations après une aide
   */
  async updateUserReputations(match, rating) {
    try {
      // Mettre à jour la réputation de l'aidant
      const helper = match.offer.user;
      const newRating = ((helper.rating * helper.helpGiven) + rating) / (helper.helpGiven + 1);
      
      await dbService.prisma.user.update({
        where: { id: helper.id },
        data: {
          rating: newRating,
          helpGiven: { increment: 1 }
        }
      });

      // Mettre à jour le compteur d'aide reçue
      await dbService.prisma.user.update({
        where: { id: match.need.user.id },
        data: {
          helpReceived: { increment: 1 }
        }
      });

    } catch (error) {
      console.error('Erreur mise à jour réputation:', error);
    }
  }
}

// Instance singleton
export const matchingService = new MatchingService();

export default matchingService;
