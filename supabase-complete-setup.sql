-- Script de configuration complète Supabase pour Nowee
-- À exécuter dans la console SQL de Supabase

-- 1. DÉSACTIVER RLS TEMPORAIREMENT POUR LES TESTS
ALTER TABLE IF EXISTS users DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS resources DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS matches DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS conversations DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS messages DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS system_events DISABLE ROW LEVEL SECURITY;

-- 2. <PERSON><PERSON><PERSON>IMER LES TABLES EXISTANTES SI ELLES EXISTENT (ATTENTION: PERTE DE DONNÉES)
DROP TABLE IF EXISTS rewards_penalties CASCADE;
DROP TABLE IF EXISTS time_bookings CASCADE;
DROP TABLE IF EXISTS time_services CASCADE;
DROP TABLE IF EXISTS exchange_rates CASCADE;
DROP TABLE IF EXISTS barter_proposals CASCADE;
DROP TABLE IF EXISTS transactions CASCADE;
DROP TABLE IF EXISTS wallets CASCADE;
DROP TABLE IF EXISTS system_events CASCADE;
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS conversations CASCADE;
DROP TABLE IF EXISTS matches CASCADE;
DROP TABLE IF EXISTS resources CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- 3. CRÉER LES TABLES PRINCIPALES

-- Table des utilisateurs
CREATE TABLE users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  phone VARCHAR(20) UNIQUE NOT NULL,
  name VARCHAR(100),
  email VARCHAR(255),
  avatar_url TEXT,
  
  -- Localisation (JSON simple)
  location JSONB,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  city VARCHAR(100),
  country VARCHAR(100) DEFAULT 'Sénégal',
  
  -- Profil et réputation
  rating DECIMAL(3,2) DEFAULT 0.0,
  help_given INTEGER DEFAULT 0,
  help_received INTEGER DEFAULT 0,
  trust_score INTEGER DEFAULT 100,
  
  -- Préférences
  preferences JSONB DEFAULT '{}',
  languages TEXT[] DEFAULT ARRAY['fr'],
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true
);

-- Table des ressources (besoins et offres unifiés)
CREATE TABLE resources (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Type de ressource
  type VARCHAR(20) NOT NULL CHECK (type IN ('NEED', 'OFFER')),
  category VARCHAR(50) NOT NULL,
  
  -- Contenu
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  tags TEXT[] DEFAULT ARRAY[]::TEXT[],
  
  -- Localisation
  location JSONB,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  radius_km INTEGER DEFAULT 5,
  
  -- Conditions
  urgency INTEGER DEFAULT 1 CHECK (urgency BETWEEN 1 AND 5),
  price_range JSONB,
  availability JSONB,
  conditions TEXT,
  
  -- Statut
  status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'MATCHED', 'COMPLETED', 'EXPIRED', 'CANCELLED')),
  expires_at TIMESTAMP WITH TIME ZONE,
  
  -- Recherche textuelle
  search_vector tsvector,
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des correspondances (matches)
CREATE TABLE matches (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  need_id UUID REFERENCES resources(id) ON DELETE CASCADE,
  offer_id UUID REFERENCES resources(id) ON DELETE CASCADE,
  
  -- Participants
  requester_id UUID REFERENCES users(id) ON DELETE CASCADE,
  provider_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Score de matching
  match_score DECIMAL(5,2) NOT NULL,
  match_factors JSONB,
  
  -- Statut
  status VARCHAR(20) DEFAULT 'PROPOSED' CHECK (status IN ('PROPOSED', 'ACCEPTED', 'REJECTED', 'COMPLETED', 'CANCELLED')),
  
  -- Évaluation
  requester_rating INTEGER CHECK (requester_rating BETWEEN 1 AND 5),
  provider_rating INTEGER CHECK (provider_rating BETWEEN 1 AND 5),
  requester_feedback TEXT,
  provider_feedback TEXT,
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  
  -- Contraintes
  CONSTRAINT different_resources CHECK (need_id != offer_id),
  CONSTRAINT different_users CHECK (requester_id != provider_id),
  UNIQUE(need_id, offer_id)
);

-- 4. TABLES ÉCONOMIQUES

-- Table des portefeuilles utilisateurs
CREATE TABLE wallets (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE UNIQUE,
  
  -- Monnaies
  nowee_coins DECIMAL(12,2) DEFAULT 100.0,
  time_credits DECIMAL(8,2) DEFAULT 0.0,
  
  -- Statistiques
  total_earned DECIMAL(12,2) DEFAULT 100.0,
  total_spent DECIMAL(12,2) DEFAULT 0.0,
  reputation_bonus DECIMAL(5,2) DEFAULT 0.0,
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des transactions économiques
CREATE TABLE transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Participants
  from_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  to_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  
  -- Type de transaction
  transaction_type VARCHAR(30) NOT NULL CHECK (transaction_type IN (
    'COIN_TRANSFER', 'TIME_EXCHANGE', 'BARTER_TRADE', 'SERVICE_PAYMENT', 
    'REWARD', 'PENALTY', 'INITIAL_BONUS'
  )),
  
  -- Montants
  nowee_coins DECIMAL(12,2) DEFAULT 0.0,
  time_credits DECIMAL(8,2) DEFAULT 0.0,
  
  -- Contexte
  match_id UUID REFERENCES matches(id) ON DELETE SET NULL,
  resource_id UUID REFERENCES resources(id) ON DELETE SET NULL,
  
  -- Description et métadonnées
  description TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  
  -- Statut
  status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED')),
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Table des propositions de troc
CREATE TABLE barter_proposals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Participants
  proposer_id UUID REFERENCES users(id) ON DELETE CASCADE,
  target_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Ressources échangées
  offered_resource_id UUID REFERENCES resources(id) ON DELETE CASCADE,
  requested_resource_id UUID REFERENCES resources(id) ON DELETE CASCADE,
  
  -- Conditions du troc
  exchange_type VARCHAR(20) NOT NULL CHECK (exchange_type IN (
    'DIRECT_BARTER', 'TIME_FOR_OBJECT', 'SERVICE_FOR_OBJECT', 'MIXED_EXCHANGE'
  )),
  
  -- Valeurs proposées
  offered_coins DECIMAL(12,2) DEFAULT 0.0,
  offered_time_hours DECIMAL(6,2) DEFAULT 0.0,
  requested_coins DECIMAL(12,2) DEFAULT 0.0,
  requested_time_hours DECIMAL(6,2) DEFAULT 0.0,
  
  -- Conditions et description
  conditions TEXT,
  proposal_message TEXT,
  
  -- Statut
  status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN (
    'PENDING', 'ACCEPTED', 'REJECTED', 'COUNTER', 'COMPLETED', 'CANCELLED'
  )),
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- 5. FONCTIONS UTILITAIRES

-- Fonction de calcul de distance
CREATE OR REPLACE FUNCTION calculate_distance_km(lat1 DECIMAL, lon1 DECIMAL, lat2 DECIMAL, lon2 DECIMAL)
RETURNS DECIMAL AS $$
DECLARE
  R DECIMAL := 6371;
  dLat DECIMAL;
  dLon DECIMAL;
  a DECIMAL;
  c DECIMAL;
BEGIN
  IF lat1 IS NULL OR lon1 IS NULL OR lat2 IS NULL OR lon2 IS NULL THEN
    RETURN NULL;
  END IF;
  
  dLat := radians(lat2 - lat1);
  dLon := radians(lon2 - lon1);
  
  a := sin(dLat/2) * sin(dLat/2) + cos(radians(lat1)) * cos(radians(lat2)) * sin(dLon/2) * sin(dLon/2);
  c := 2 * atan2(sqrt(a), sqrt(1-a));
  
  RETURN R * c;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Fonction de mise à jour automatique du timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fonction de mise à jour du search_vector
CREATE OR REPLACE FUNCTION update_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector = to_tsvector('french', 
    COALESCE(NEW.title, '') || ' ' || 
    COALESCE(NEW.description, '') || ' ' || 
    COALESCE(array_to_string(NEW.tags, ' '), '')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fonction de mise à jour du portefeuille
CREATE OR REPLACE FUNCTION update_wallet_balance(
  p_user_id UUID,
  p_coins_delta DECIMAL,
  p_time_delta DECIMAL DEFAULT 0
)
RETURNS BOOLEAN AS $$
DECLARE
  current_coins DECIMAL;
  current_time DECIMAL;
BEGIN
  -- Récupérer les soldes actuels
  SELECT nowee_coins, time_credits 
  INTO current_coins, current_time
  FROM wallets 
  WHERE user_id = p_user_id;
  
  -- Si le portefeuille n'existe pas, le créer
  IF NOT FOUND THEN
    INSERT INTO wallets (user_id, nowee_coins, time_credits)
    VALUES (p_user_id, GREATEST(0, p_coins_delta), GREATEST(0, p_time_delta));
    RETURN TRUE;
  END IF;
  
  -- Vérifier si l'utilisateur a suffisamment de fonds
  IF current_coins + p_coins_delta < 0 THEN
    RETURN FALSE;
  END IF;
  
  IF current_time + p_time_delta < 0 THEN
    RETURN FALSE;
  END IF;
  
  -- Mettre à jour le portefeuille
  UPDATE wallets 
  SET 
    nowee_coins = nowee_coins + p_coins_delta,
    time_credits = time_credits + p_time_delta,
    total_earned = total_earned + GREATEST(0, p_coins_delta),
    total_spent = total_spent + GREATEST(0, -p_coins_delta),
    updated_at = NOW()
  WHERE user_id = p_user_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 6. TRIGGERS

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_resources_updated_at BEFORE UPDATE ON resources
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_resources_search_vector BEFORE INSERT OR UPDATE ON resources
  FOR EACH ROW EXECUTE FUNCTION update_search_vector();

CREATE TRIGGER update_matches_updated_at BEFORE UPDATE ON matches
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_wallets_updated_at BEFORE UPDATE ON wallets
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_barter_proposals_updated_at BEFORE UPDATE ON barter_proposals
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. INDEX POUR PERFORMANCE

CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_location ON users(latitude, longitude);
CREATE INDEX idx_users_active ON users(is_active, last_active_at);

CREATE INDEX idx_resources_type_status ON resources(type, status);
CREATE INDEX idx_resources_category ON resources(category);
CREATE INDEX idx_resources_location ON resources(latitude, longitude);
CREATE INDEX idx_resources_search ON resources USING GIN(search_vector);
CREATE INDEX idx_resources_user_id ON resources(user_id);
CREATE INDEX idx_resources_created_at ON resources(created_at DESC);

CREATE INDEX idx_matches_need_offer ON matches(need_id, offer_id);
CREATE INDEX idx_matches_users ON matches(requester_id, provider_id);
CREATE INDEX idx_matches_status ON matches(status);

CREATE INDEX idx_wallets_user_id ON wallets(user_id);
CREATE INDEX idx_transactions_users ON transactions(from_user_id, to_user_id);
CREATE INDEX idx_transactions_type_status ON transactions(transaction_type, status);
CREATE INDEX idx_barter_proposals_users ON barter_proposals(proposer_id, target_user_id);

-- 8. POLITIQUES DE SÉCURITÉ PERMISSIVES POUR LES TESTS

ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE resources ENABLE ROW LEVEL SECURITY;
ALTER TABLE matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE barter_proposals ENABLE ROW LEVEL SECURITY;

-- Politiques permissives temporaires
CREATE POLICY "Allow all operations on users" ON users FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on resources" ON resources FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on matches" ON matches FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on wallets" ON wallets FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on transactions" ON transactions FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on barter_proposals" ON barter_proposals FOR ALL USING (true) WITH CHECK (true);

-- 9. DONNÉES DE TEST

-- Insérer quelques utilisateurs de test
INSERT INTO users (phone, name, city, latitude, longitude) VALUES
('+221771234567', 'Alice Diop', 'Dakar', 14.7167, -17.4677),
('+221772345678', 'Bob Ndiaye', 'Dakar', 14.7200, -17.4700),
('+221773456789', 'Charlie Fall', 'Thiès', 14.7886, -16.9246);

-- Créer les portefeuilles automatiquement
INSERT INTO wallets (user_id, nowee_coins, time_credits)
SELECT id, 100.0, 0.0 FROM users;

-- Insérer quelques ressources de test
INSERT INTO resources (user_id, type, category, title, description, latitude, longitude, urgency)
SELECT 
  u.id,
  'NEED',
  'MATERIAL',
  'Besoin d''une perceuse électrique',
  'J''ai besoin d''une perceuse pour des travaux de rénovation ce weekend',
  u.latitude,
  u.longitude,
  2
FROM users u WHERE u.name = 'Alice Diop';

INSERT INTO resources (user_id, type, category, title, description, latitude, longitude)
SELECT 
  u.id,
  'OFFER',
  'MATERIAL',
  'Perceuse électrique disponible',
  'Je peux prêter ma perceuse électrique professionnelle',
  u.latitude,
  u.longitude
FROM users u WHERE u.name = 'Bob Ndiaye';

-- Message de confirmation
DO $$
BEGIN
  RAISE NOTICE '🎉 Configuration Supabase terminée avec succès !';
  RAISE NOTICE '✅ Tables créées: users, resources, matches, wallets, transactions, barter_proposals';
  RAISE NOTICE '✅ Fonctions créées: calculate_distance_km, update_wallet_balance';
  RAISE NOTICE '✅ Triggers créés pour mise à jour automatique';
  RAISE NOTICE '✅ Index créés pour performance';
  RAISE NOTICE '✅ Politiques RLS permissives activées';
  RAISE NOTICE '✅ Données de test insérées';
  RAISE NOTICE '🚀 Nowee est prêt pour les tests !';
END $$;
