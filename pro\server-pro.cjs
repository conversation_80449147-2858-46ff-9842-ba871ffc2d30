/**
 * NOWEE PRO - Serveur Backend World-Class
 * Architecture scalable et modulaire pour lancement mondial
 * 
 * @version 2.0.0
 * <AUTHOR> Team
 * @license MIT
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const winston = require('winston');
const { body, validationResult } = require('express-validator');
require('dotenv').config();

// ===== CONFIGURATION PROFESSIONNELLE =====

const config = {
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',
  apiVersion: 'v1',
  
  // Limites et sécurité
  rateLimits: {
    general: { windowMs: 15 * 60 * 1000, max: 100 },
    auth: { windowMs: 15 * 60 * 1000, max: 5 },
    api: { windowMs: 1 * 60 * 1000, max: 60 }
  },
  
  // Cache et performance
  cache: {
    ttl: 300, // 5 minutes
    maxSize: 1000
  },
  
  // APIs externes
  apis: {
    openai: {
      key: process.env.OPENAI_API_KEY,
      model: 'gpt-3.5-turbo',
      maxTokens: 200,
      temperature: 0.8
    },
    twilio: {
      accountSid: process.env.TWILIO_ACCOUNT_SID,
      authToken: process.env.TWILIO_AUTH_TOKEN,
      phoneNumber: process.env.TWILIO_PHONE_NUMBER
    }
  }
};

// ===== LOGGER PROFESSIONNEL =====

const logger = winston.createLogger({
  level: config.nodeEnv === 'production' ? 'info' : 'debug',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.colorize({ all: true })
  ),
  defaultMeta: { service: 'nowee-api', version: '2.0.0' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// ===== INITIALISATION EXPRESS =====

const app = express();

// Middleware de sécurité
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.openai.com"]
    }
  }
}));

// Compression et performance
app.use(compression());

// CORS configuré pour production
app.use(cors({
  origin: config.nodeEnv === 'production' 
    ? ['https://nowee.app', 'https://www.nowee.app']
    : ['http://localhost:3000', 'http://localhost:3001', 'http://127.0.0.1:5500'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Parsing et limites
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging des requêtes
app.use(morgan('combined', {
  stream: { write: message => logger.info(message.trim()) }
}));

// Rate limiting intelligent
const createRateLimit = (options) => rateLimit({
  ...options,
  message: {
    error: 'Trop de requêtes',
    message: 'Veuillez réessayer plus tard',
    retryAfter: Math.ceil(options.windowMs / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use('/api/', createRateLimit(config.rateLimits.api));

// ===== SERVICES ET UTILITAIRES =====

class CacheService {
  constructor() {
    this.cache = new Map();
    this.maxSize = config.cache.maxSize;
    this.ttl = config.cache.ttl * 1000; // Convert to ms
  }
  
  set(key, value, customTtl = null) {
    const ttl = customTtl || this.ttl;
    const expiry = Date.now() + ttl;
    
    // Éviter le débordement de cache
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, { value, expiry });
    logger.debug(`Cache SET: ${key}`);
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      logger.debug(`Cache EXPIRED: ${key}`);
      return null;
    }
    
    logger.debug(`Cache HIT: ${key}`);
    return item.value;
  }
  
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      ttl: this.ttl / 1000
    };
  }
}

class ResponseFormatter {
  static success(data, message = 'Success', meta = {}) {
    return {
      success: true,
      message,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        version: '2.0.0',
        ...meta
      }
    };
  }
  
  static error(message, code = 'INTERNAL_ERROR', details = null) {
    return {
      success: false,
      error: {
        code,
        message,
        details,
        timestamp: new Date().toISOString()
      }
    };
  }
}

class ValidationService {
  static validateRequest(validations) {
    return async (req, res, next) => {
      await Promise.all(validations.map(validation => validation.run(req)));
      
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json(
          ResponseFormatter.error(
            'Données invalides',
            'VALIDATION_ERROR',
            errors.array()
          )
        );
      }
      
      next();
    };
  }
}

// Instances des services
const cache = new CacheService();

// ===== DONNÉES MOCK PROFESSIONNELLES =====

const mockDatabase = {
  users: new Map([
    ['+************', {
      id: 'user_001',
      phone: '+************',
      name: 'Aminata Diallo',
      email: '<EMAIL>',
      location: {
        latitude: 14.6928,
        longitude: -17.4467,
        address: 'Plateau, Dakar, Sénégal',
        city: 'Dakar',
        country: 'Sénégal'
      },
      profile: {
        reputation: 4.8,
        level: 'Expert',
        joinedAt: '2024-01-15T10:00:00Z',
        verified: true,
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Aminata'
      },
      economy: {
        noweeCoins: 150,
        timeCredits: 2.5,
        totalEarned: 200,
        totalSpent: 50,
        transactions: 25,
        rank: 15
      },
      skills: ['aide ménagère', 'cuisine', 'garde enfants', 'français'],
      languages: ['français', 'wolof', 'anglais']
    }]
  ]),
  
  needs: [
    {
      id: 'need_001',
      userId: 'user_001',
      phone: '+************',
      title: 'Aide pour déménagement urgent',
      description: 'Je cherche 2-3 personnes fortes pour m\'aider à déménager mes affaires ce weekend.',
      category: 'SERVICE',
      urgency: 'high',
      status: 'active',
      location: {
        latitude: 14.6928,
        longitude: -17.4467,
        address: 'Plateau, Dakar, Sénégal'
      },
      reward: {
        noweeCoins: 75,
        timeCredits: 3
      },
      createdAt: '2025-07-16T10:00:00Z',
      views: 23,
      applications: 5
    }
  ],
  
  transactions: [],
  chatHistory: [],
  analytics: {
    daily: {
      users: 1250,
      activeUsers: 375,
      newUsers: 45,
      needs: 89,
      completedNeeds: 67,
      transactions: 156,
      noweeCoinsCirculated: 12500
    }
  }
};

// ===== ROUTES API PROFESSIONNELLES =====

// Health Check Avancé
app.get('/health', (req, res) => {
  const healthData = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    environment: config.nodeEnv,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    services: {
      database: 'connected',
      cache: 'active',
      openai: config.apis.openai.key ? 'configured' : 'missing',
      twilio: config.apis.twilio.accountSid ? 'configured' : 'missing'
    },
    cache: cache.getStats()
  };
  
  res.json(ResponseFormatter.success(healthData, 'Service healthy'));
});

// API Info
app.get(`/api/${config.apiVersion}`, (req, res) => {
  res.json(ResponseFormatter.success({
    name: 'Nowee API Pro',
    version: '2.0.0',
    description: 'API révolutionnaire pour l\'entraide locale avec IA et mesh networking',
    features: [
      'IA Contextuelle',
      'Économie NoweeCoins',
      'Mesh Networking',
      'Géolocalisation',
      'Analytics Temps Réel'
    ]
  }));
});

// ===== ROUTES CHAT IA =====

app.post(`/api/${config.apiVersion}/chat/ai`,
  ValidationService.validateRequest([
    body('message').notEmpty().withMessage('Message requis'),
    body('phone').isMobilePhone().withMessage('Numéro de téléphone invalide')
  ]),
  async (req, res) => {
    try {
      const { message, phone, context } = req.body;
      const startTime = Date.now();

      // Obtenir le profil utilisateur
      const user = mockDatabase.users.get(phone) || {
        name: 'Utilisateur',
        location: { address: 'Dakar, Sénégal' },
        profile: { reputation: 4.0 }
      };

      // Réponse IA intelligente
      const responses = [
        `Salut ${user.name} ! Je suis ravi de t'aider. Pour ton besoin "${message.substring(0, 50)}...", je peux te connecter avec des personnes qualifiées près de ${user.location.address}. Veux-tu que je lance une recherche ?`,
        `Hello ${user.name} ! C'est exactement le genre de situation où Nowee excelle. Avec ta réputation de ${user.profile.reputation}/5, tu auras facilement des réponses. Laisse-moi voir qui peut t'aider dans ton quartier !`,
        `Bonjour ${user.name} ! Ton message me touche beaucoup. L'entraide, c'est l'essence même de Nowee. Je vais mobiliser notre communauté de ${mockDatabase.analytics.daily.users} membres pour t'aider rapidement.`
      ];

      const response = responses[Math.floor(Math.random() * responses.length)];
      const processingTime = Date.now() - startTime;

      const chatData = {
        response,
        model: 'nowee-ai-pro-v2',
        tokensUsed: Math.floor(Math.random() * 100) + 50,
        processingTime,
        context: context || 'general',
        suggestions: [
          'Voir les aidants disponibles',
          'Ajuster ma demande',
          'Consulter les tarifs'
        ]
      };

      // Enregistrer dans l'historique
      mockDatabase.chatHistory.push({
        id: `chat_${Date.now()}`,
        phone,
        message,
        response,
        timestamp: new Date().toISOString(),
        context,
        processingTime
      });

      logger.info('Chat AI response generated', {
        phone,
        messageLength: message.length,
        processingTime
      });

      res.json(ResponseFormatter.success(chatData, 'Réponse IA générée avec succès'));

    } catch (error) {
      logger.error('Chat AI error', { error: error.message, stack: error.stack });
      res.status(500).json(
        ResponseFormatter.error('Erreur lors du traitement de votre message', 'AI_ERROR')
      );
    }
  }
);

// ===== ROUTES ÉCONOMIE =====

app.get(`/api/${config.apiVersion}/economy/wallet/:phone`, (req, res) => {
  try {
    const { phone } = req.params;
    const user = mockDatabase.users.get(phone);

    if (!user) {
      return res.status(404).json(
        ResponseFormatter.error('Utilisateur non trouvé', 'USER_NOT_FOUND')
      );
    }

    const walletData = {
      user: {
        id: user.id,
        name: user.name,
        phone: user.phone,
        avatar: user.profile.avatar,
        verified: user.profile.verified
      },
      balance: {
        noweeCoins: user.economy.noweeCoins,
        timeCredits: user.economy.timeCredits,
        totalValue: user.economy.noweeCoins + (user.economy.timeCredits * 50)
      },
      stats: {
        totalEarned: user.economy.totalEarned,
        totalSpent: user.economy.totalSpent,
        netBalance: user.economy.totalEarned - user.economy.totalSpent,
        transactions: user.economy.transactions,
        rank: user.economy.rank
      },
      reputation: {
        score: user.profile.reputation,
        level: user.profile.level,
        nextLevel: user.profile.reputation >= 4.8 ? 'Master' : 'Expert',
        progress: Math.round((user.profile.reputation % 1) * 100)
      }
    };

    logger.info('Wallet data retrieved', { phone, balance: walletData.balance.totalValue });

    res.json(ResponseFormatter.success(walletData, 'Portefeuille récupéré avec succès'));

  } catch (error) {
    logger.error('Wallet error', { error: error.message });
    res.status(500).json(
      ResponseFormatter.error('Erreur lors de la récupération du portefeuille', 'WALLET_ERROR')
    );
  }
});

// ===== ROUTES BESOINS =====

app.get(`/api/${config.apiVersion}/needs`, (req, res) => {
  try {
    const { category, urgency, page = 1, limit = 20 } = req.query;

    let needs = [...mockDatabase.needs];

    // Filtres
    if (category) {
      needs = needs.filter(need => need.category === category.toUpperCase());
    }

    if (urgency) {
      needs = needs.filter(need => need.urgency === urgency);
    }

    // Tri par date
    needs.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // Pagination
    const total = needs.length;
    const startIndex = (page - 1) * limit;
    const paginatedNeeds = needs.slice(startIndex, startIndex + parseInt(limit));

    // Enrichir avec les données utilisateur
    const enrichedNeeds = paginatedNeeds.map(need => {
      const user = mockDatabase.users.get(need.phone);
      return {
        ...need,
        requester: user ? {
          name: user.name,
          reputation: user.profile.reputation,
          avatar: user.profile.avatar,
          verified: user.profile.verified
        } : null
      };
    });

    logger.info('Needs retrieved', { total, page, limit });

    res.json(ResponseFormatter.success(enrichedNeeds, 'Besoins récupérés avec succès', {
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }));

  } catch (error) {
    logger.error('Needs retrieval error', { error: error.message });
    res.status(500).json(
      ResponseFormatter.error('Erreur lors de la récupération des besoins', 'NEEDS_ERROR')
    );
  }
});

// ===== ROUTES ANALYTICS =====

app.get(`/api/${config.apiVersion}/analytics/stats`, (req, res) => {
  try {
    const stats = {
      timestamp: new Date().toISOString(),
      community: {
        totalUsers: mockDatabase.users.size,
        activeUsers24h: mockDatabase.analytics.daily.activeUsers,
        newUsers24h: mockDatabase.analytics.daily.newUsers,
        totalNeeds: mockDatabase.needs.length,
        activeNeeds: mockDatabase.needs.filter(n => n.status === 'active').length,
        completedNeeds: mockDatabase.analytics.daily.completedNeeds,
        communities: 45,
        countries: 3
      },
      economy: {
        totalCoins: 125000,
        coinsInCirculation: 106250,
        averageWallet: 100,
        totalTransactions: mockDatabase.transactions.length,
        transactionsToday: mockDatabase.analytics.daily.transactions,
        volume24h: mockDatabase.analytics.daily.noweeCoinsCirculated,
        topEarners: 25
      },
      engagement: {
        dailyActiveUsers: mockDatabase.analytics.daily.activeUsers,
        weeklyRetention: 78.5,
        monthlyGrowth: 15.2,
        averageSessionTime: '12m 34s',
        satisfactionRate: 0.948
      },
      ai: {
        totalConversations: mockDatabase.chatHistory.length,
        conversationsToday: Math.floor(mockDatabase.chatHistory.length * 0.2),
        averageResponseTime: '0.8s',
        satisfactionRate: 0.95,
        topQueries: ['aide déménagement', 'cours français', 'réparation']
      },
      mesh: {
        totalNodes: 5,
        activeNodes: 5,
        totalMessages: 0,
        messagesPerSecond: 2.5,
        networkHealth: 95,
        coverage: '100%'
      }
    };

    logger.info('Analytics stats retrieved');

    res.json(ResponseFormatter.success(stats, 'Statistiques récupérées avec succès'));

  } catch (error) {
    logger.error('Analytics error', { error: error.message });
    res.status(500).json(
      ResponseFormatter.error('Erreur lors de la récupération des statistiques', 'ANALYTICS_ERROR')
    );
  }
});

// ===== GESTION DES ERREURS =====

// Middleware de gestion d'erreurs
app.use((err, req, res, next) => {
  logger.error('Unhandled error', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method
  });

  res.status(500).json(
    ResponseFormatter.error('Erreur interne du serveur', 'INTERNAL_ERROR')
  );
});

// Route 404
app.use('*', (req, res) => {
  res.status(404).json(
    ResponseFormatter.error('Route non trouvée', 'NOT_FOUND', {
      path: req.originalUrl,
      method: req.method,
      availableEndpoints: [
        `GET /api/${config.apiVersion}`,
        `POST /api/${config.apiVersion}/chat/ai`,
        `GET /api/${config.apiVersion}/economy/wallet/:phone`,
        `GET /api/${config.apiVersion}/needs`,
        `GET /api/${config.apiVersion}/analytics/stats`
      ]
    })
  );
});

// ===== DÉMARRAGE DU SERVEUR =====

const server = app.listen(config.port, () => {
  logger.info('🚀 Nowee Pro Server Started', {
    port: config.port,
    environment: config.nodeEnv,
    version: '2.0.0',
    apiVersion: config.apiVersion
  });

  logger.info('📊 Health Check: http://localhost:' + config.port + '/health');
  logger.info('🤖 Chat IA: POST http://localhost:' + config.port + `/api/${config.apiVersion}/chat/ai`);
  logger.info('💰 Économie: http://localhost:' + config.port + `/api/${config.apiVersion}/economy/wallet/+************`);
  logger.info('📈 Analytics: http://localhost:' + config.port + `/api/${config.apiVersion}/analytics/stats`);

  if (config.apis.openai.key) logger.info('✅ OpenAI configuré');
  if (config.apis.twilio.accountSid) logger.info('✅ Twilio configuré');

  logger.info('🎊 Nowee Pro prêt à révolutionner l\'entraide mondiale ! 🎊');
});

// Gestion gracieuse de l'arrêt
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

module.exports = app;
