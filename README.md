# 🌟 Nowee - L'entraide locale en temps réel

> **"Ensemble, ici et maintenant"** - Transforme ton besoin du moment en action immédiate, locale et solidaire.

## 🎯 Concept

Nowee est la première application universelle qui permet à n'importe quel humain de transformer son besoin du moment en une action immédiate, locale, solidaire et pertinente, grâce à l'intelligence contextuelle en temps réel.

**En clair :** Tu as un besoin → Nowee te connecte instantanément aux ressources humaines, matérielles ou numériques autour de toi pour le résoudre — même sans argent.

## 🚀 MVP - Bot WhatsApp

Cette version initiale est un bot WhatsApp intelligent qui :
- ✅ Comprend tes besoins en langage naturel
- ✅ Analyse le contexte géographique
- ✅ Propose des solutions immédiates
- ✅ Connecte avec la communauté locale
- ✅ Fonctionne sur n'importe quel téléphone

## 🛠️ Installation

### Prérequis
- Node.js >= 18
- Compte <PERSON> (pour WhatsApp)
- Clé API OpenAI
- ngrok (pour exposer le webhook en local)

### Étapes

1. **Clone et installe les dépendances**
```bash
git clone <repo-url>
cd nowee
npm install
```

2. **Configure les variables d'environnement**
```bash
cp .env.example .env
# Édite .env avec tes clés API
```

3. **Lance le serveur**
```bash
npm start
# ou en mode développement
npm run dev
```

4. **Expose le webhook avec ngrok**
```bash
ngrok http 3000
# Copie l'URL HTTPS générée
```

5. **Configure Twilio**
- Va dans la console Twilio
- WhatsApp Sandbox Settings
- Webhook URL: `https://ton-url-ngrok.com/webhook`

## 🔧 Configuration

### Variables d'environnement (.env)
```env
OPENAI_API_KEY=sk-your-openai-key
TWILIO_ACCOUNT_SID=AC-your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
PORT=3000
```

### Test du bot
1. Envoie "join <sandbox-keyword>" au numéro Twilio WhatsApp
2. Écris ton besoin : "J'ai besoin d'une perceuse à Dakar"
3. Reçois une réponse intelligente et contextuelle !

## 📱 Exemples d'utilisation

```
Utilisateur: "J'ai besoin d'une perceuse maintenant à Dakar"
Nowee: "🔧 Je vais chercher qui a une perceuse près de chez toi ! En attendant, as-tu vérifié chez tes voisins directs ?"

Utilisateur: "Qui peut m'aider à déménager ?"
Nowee: "📦 Déménagement en vue ! Je peux te connecter avec des personnes disponibles dans ton quartier. Quel jour et quelle heure ?"

Utilisateur: "Je cherche un plombier urgent"
Nowee: "🚨 Urgence plomberie comprise ! Voici les plombiers disponibles près de toi, et en attendant, as-tu coupé l'eau ?"
```

## 🏗️ Architecture

```
nowee/
├── src/
│   ├── bot/
│   │   └── nowee-whatsapp-bot.js    # Bot WhatsApp principal
│   ├── services/                     # Services métier (futur)
│   ├── models/                       # Modèles de données (futur)
│   └── utils/                        # Utilitaires
├── tests/                            # Tests unitaires
├── docs/                             # Documentation
└── package.json
```

## 🔮 Roadmap

### Phase 1 - MVP Bot WhatsApp ✅
- [x] Bot WhatsApp fonctionnel
- [x] IA contextuelle avec OpenAI
- [x] Détection géographique basique
- [x] Gestion des sessions utilisateur

### Phase 2 - Communauté (Q1 2024)
- [ ] Base de données utilisateurs
- [ ] Système de réputation
- [ ] Matching intelligent des besoins
- [ ] Notifications push

### Phase 3 - App Mobile (Q2 2024)
- [ ] Application React Native
- [ ] Géolocalisation précise
- [ ] Mode offline/mesh
- [ ] Système de paiement alternatif

### Phase 4 - Écosystème (Q3 2024)
- [ ] API publique
- [ ] Intégrations tierces
- [ ] IA prédictive
- [ ] Expansion internationale

## 🤝 Contribuer

1. Fork le projet
2. Crée ta branche (`git checkout -b feature/amazing-feature`)
3. Commit tes changements (`git commit -m 'Add amazing feature'`)
4. Push vers la branche (`git push origin feature/amazing-feature`)
5. Ouvre une Pull Request

## 📄 Licence

MIT License - voir le fichier [LICENSE](LICENSE) pour plus de détails.

## 💬 Contact

- Email: <EMAIL>
- Twitter: [@NoweeApp](https://twitter.com/NoweeApp)
- Discord: [Communauté Nowee](https://discord.gg/nowee)

---

**Nowee** - *Parce que l'entraide ne devrait jamais attendre* 🌍❤️
