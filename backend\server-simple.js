/**
 * Serveur Nowee Simple pour Test
 */

const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Données de test
const mockData = {
  users: new Map([
    ['+************', {
      phone: '+************',
      name: 'Aminata Diallo',
      location: { latitude: 14.6928, longitude: -17.4467, address: 'Dakar, Sénégal' },
      reputation: 4.8,
      noweeCoins: 150,
      timeCredits: 2.5,
      totalEarned: 200,
      totalSpent: 50
    }]
  ]),
  
  needs: [
    {
      id: '1',
      phone: '+************',
      title: 'Aide pour déménagement',
      description: 'Je cherche 2-3 personnes pour m\'aider à déménager ce weekend',
      category: 'SERVICE',
      urgency: 'medium',
      latitude: 14.6928,
      longitude: -17.4467,
      address: 'Dakar, Sénégal',
      status: 'active',
      createdAt: new Date().toISOString()
    }
  ],
  
  chatHistory: []
};

// Routes
app.get('/', (req, res) => {
  res.json({
    message: '🚀 Nowee API Simple - Test',
    version: '2.0.0',
    status: 'active',
    timestamp: new Date().toISOString()
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    environment: process.env.NODE_ENV || 'development',
    apis: {
      openai: !!process.env.OPENAI_API_KEY,
      twilio: !!process.env.TWILIO_ACCOUNT_SID
    },
    features: {
      economy: true,
      barter: true,
      mesh: true
    }
  });
});

// API Chat IA (version simplifiée)
app.post('/api/chat/ai', async (req, res) => {
  try {
    const { message, phone, context } = req.body;
    
    console.log('📱 Message reçu:', { message, phone, context });
    
    if (!message || !phone) {
      return res.status(400).json({
        success: false,
        error: 'Message et numéro de téléphone requis'
      });
    }

    // Réponse simulée pour test
    const responses = [
      "Salut ! Je suis Nowee, ton assistant d'entraide locale au Sénégal. Comment puis-je t'aider aujourd'hui ? 🤝",
      "Hello ! C'est formidable de te voir ici. Dis-moi, as-tu besoin d'aide ou souhaites-tu aider quelqu'un dans ta communauté ?",
      "Bonjour ! Je suis là pour faciliter l'entraide dans ta région. Que puis-je faire pour toi aujourd'hui ?",
      "Salut ! Bienvenue sur Nowee. Je peux t'aider à trouver de l'aide ou à proposer tes services. Que cherches-tu ?"
    ];
    
    const response = responses[Math.floor(Math.random() * responses.length)];

    // Enregistrer la conversation
    mockData.chatHistory.push({
      id: Date.now().toString(),
      phone,
      message,
      response,
      timestamp: new Date().toISOString(),
      context
    });

    console.log('✅ Réponse envoyée:', response);

    res.json({
      success: true,
      response,
      tokensUsed: 50,
      model: 'nowee-simple'
    });

  } catch (error) {
    console.error('❌ Erreur Chat IA:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors du traitement de votre message'
    });
  }
});

// API Économie
app.get('/api/economy/wallet/:phone', (req, res) => {
  try {
    const { phone } = req.params;
    const user = mockData.users.get(phone);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'Utilisateur non trouvé'
      });
    }

    const stats = {
      netBalance: user.totalEarned - user.totalSpent,
      reputationLevel: user.reputation >= 4.5 ? 'Expert' : 
                      user.reputation >= 4.0 ? 'Avancé' :
                      user.reputation >= 3.0 ? 'Intermédiaire' : 'Débutant',
      totalValue: user.noweeCoins + (user.timeCredits * 50),
      rank: Math.floor(Math.random() * 100) + 1
    };

    res.json({
      success: true,
      wallet: { ...user, stats }
    });
  } catch (error) {
    console.error('❌ Erreur wallet:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API Besoins
app.get('/api/needs', (req, res) => {
  try {
    res.json({ success: true, needs: mockData.needs });
  } catch (error) {
    console.error('❌ Erreur besoins:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API Statistiques
app.get('/api/stats/advanced', (req, res) => {
  try {
    const stats = {
      timestamp: new Date().toISOString(),
      community: {
        totalUsers: mockData.users.size,
        activeUsers24h: Math.floor(mockData.users.size * 0.3),
        totalNeeds: mockData.needs.length,
        needsToday: Math.floor(mockData.needs.length * 0.1),
        communities: 45
      },
      economy: {
        totalCoins: 125000,
        coinsInCirculation: 106250,
        averageWallet: 100,
        totalTransactions: 0,
        transactionsToday: 0
      },
      mesh: {
        totalNodes: 5,
        activeNodes: 5,
        totalMessages: 0,
        messagesPerSecond: 2.5,
        networkHealth: 95
      },
      ai: {
        totalConversations: mockData.chatHistory.length,
        conversationsToday: Math.floor(mockData.chatHistory.length * 0.2),
        averageResponseTime: '0.5s',
        satisfactionRate: 0.95
      }
    };
    
    res.json({ success: true, stats });
  } catch (error) {
    console.error('❌ Erreur stats:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API Test de connectivité
app.get('/api/test/connectivity', (req, res) => {
  try {
    res.json({
      openai: {
        status: process.env.OPENAI_API_KEY ? 'SUCCESS' : 'NOT_CONFIGURED',
        response: process.env.OPENAI_API_KEY ? 'OK' : 'Clé API manquante'
      },
      twilio: {
        status: process.env.TWILIO_ACCOUNT_SID ? 'SUCCESS' : 'NOT_CONFIGURED',
        response: process.env.TWILIO_ACCOUNT_SID ? 'OK' : 'Clés Twilio manquantes'
      }
    });
  } catch (error) {
    console.error('❌ Erreur test connectivité:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({
    success: false,
    error: 'Erreur interne du serveur'
  });
});

// Route 404
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route non trouvée'
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur Nowee Simple démarré sur le port ${PORT}`);
  console.log(`📊 Santé: http://localhost:${PORT}/health`);
  console.log(`🤖 Chat IA: POST http://localhost:${PORT}/api/chat/ai`);
  console.log(`💰 Économie: http://localhost:${PORT}/api/economy/wallet/+************`);
  console.log(`📈 Stats: http://localhost:${PORT}/api/stats/advanced`);
  console.log(`🎊 Nowee prêt pour les tests ! 🎊`);
});

module.exports = app;
