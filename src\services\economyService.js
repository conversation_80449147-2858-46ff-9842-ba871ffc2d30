/**
 * Service économique pour Nowee
 * Gère les NoweeCoins, le troc, les échanges de temps et les transactions
 */

import { dbService } from './databaseServiceUnified.js';

// Configuration économique
const ECONOMY_CONFIG = {
  // Monnaie
  INITIAL_COINS: 100,
  COINS_PER_HOUR: 50,
  BASE_HOURLY_RATE: 50,
  
  // Récompenses
  HELP_COMPLETION_REWARD: 25,
  GOOD_RATING_BONUS: 10,
  DAILY_LOGIN_BONUS: 5,
  REFERRAL_BONUS: 50,
  
  // Pénalités
  NO_SHOW_PENALTY: -20,
  BAD_RATING_PENALTY: -10,
  LATE_COMPLETION_PENALTY: -5,
  
  // Multiplicateurs
  URGENCY_MULTIPLIERS: {
    1: 1.0,   // Normal
    2: 1.2,   // Peu urgent
    3: 1.5,   // Urgent
    4: 2.0,   // Très urgent
    5: 3.0    // Critique
  },
  
  SKILL_MULTIPLIERS: {
    'EDUCATION': 1.2,
    'REPAIR': 1.1,
    'TRANSPORT': 1.0,
    'CARE': 1.3,
    'CLEANING': 0.9,
    'COOKING': 1.0,
    'TECHNOLOGY': 1.4,
    'LEGAL': 1.5,
    'MEDICAL': 1.6
  }
};

/**
 * Service économique principal
 */
export class EconomyService {
  
  /**
   * Initialise le portefeuille d'un utilisateur
   */
  static async initializeWallet(userId) {
    try {
      if (dbService.isUsingSupabase()) {
        // Vérifier si le portefeuille existe déjà
        const { data: existingWallet } = await dbService.supabase
          .from('wallets')
          .select('*')
          .eq('user_id', userId)
          .single();
        
        if (existingWallet) {
          return existingWallet;
        }
        
        // Créer un nouveau portefeuille
        const { data: wallet, error } = await dbService.supabase
          .from('wallets')
          .insert([{
            user_id: userId,
            nowee_coins: ECONOMY_CONFIG.INITIAL_COINS,
            time_credits: 0.0
          }])
          .select()
          .single();
        
        if (error) throw error;
        
        // Enregistrer la transaction initiale
        await this.recordTransaction({
          to_user_id: userId,
          transaction_type: 'INITIAL_BONUS',
          nowee_coins: ECONOMY_CONFIG.INITIAL_COINS,
          description: 'Bonus de bienvenue Nowee',
          status: 'COMPLETED'
        });
        
        return wallet;
      }
      
      // Mode fallback
      return this.initializeWalletFallback(userId);
      
    } catch (error) {
      console.error('Erreur initialisation portefeuille:', error);
      return this.initializeWalletFallback(userId);
    }
  }
  
  /**
   * Fallback pour l'initialisation du portefeuille
   */
  static initializeWalletFallback(userId) {
    const memoryStorage = dbService.getMemoryStorage();
    
    if (!memoryStorage.wallets) {
      memoryStorage.wallets = new Map();
    }
    
    const wallet = {
      id: `wallet_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      user_id: userId,
      nowee_coins: ECONOMY_CONFIG.INITIAL_COINS,
      time_credits: 0.0,
      total_earned: ECONOMY_CONFIG.INITIAL_COINS,
      total_spent: 0.0,
      reputation_bonus: 0.0,
      created_at: new Date(),
      updated_at: new Date()
    };
    
    memoryStorage.wallets.set(userId, wallet);
    return wallet;
  }
  
  /**
   * Obtient le portefeuille d'un utilisateur
   */
  static async getWallet(userId) {
    try {
      if (dbService.isUsingSupabase()) {
        const { data: wallet, error } = await dbService.supabase
          .from('wallets')
          .select('*')
          .eq('user_id', userId)
          .single();
        
        if (error && error.code === 'PGRST116') {
          // Portefeuille n'existe pas, le créer
          return await this.initializeWallet(userId);
        }
        
        if (error) throw error;
        return wallet;
      }
      
      // Mode fallback
      const memoryStorage = dbService.getMemoryStorage();
      if (!memoryStorage.wallets) {
        memoryStorage.wallets = new Map();
      }
      
      let wallet = memoryStorage.wallets.get(userId);
      if (!wallet) {
        wallet = this.initializeWalletFallback(userId);
      }
      
      return wallet;
      
    } catch (error) {
      console.error('Erreur récupération portefeuille:', error);
      return this.initializeWalletFallback(userId);
    }
  }
  
  /**
   * Effectue une transaction entre utilisateurs
   */
  static async transferCoins(fromUserId, toUserId, amount, description, context = {}) {
    try {
      if (amount <= 0) {
        throw new Error('Le montant doit être positif');
      }
      
      // Vérifier les soldes
      const fromWallet = await this.getWallet(fromUserId);
      const toWallet = await this.getWallet(toUserId);
      
      if (fromWallet.nowee_coins < amount) {
        throw new Error('Solde insuffisant');
      }
      
      if (dbService.isUsingSupabase()) {
        // Transaction Supabase
        const { data: transaction, error } = await dbService.supabase
          .from('transactions')
          .insert([{
            from_user_id: fromUserId,
            to_user_id: toUserId,
            transaction_type: 'COIN_TRANSFER',
            nowee_coins: amount,
            description: description,
            metadata: context,
            status: 'COMPLETED'
          }])
          .select()
          .single();
        
        if (error) throw error;
        
        // Mettre à jour les portefeuilles
        await this.updateWalletBalance(fromUserId, -amount, 0);
        await this.updateWalletBalance(toUserId, amount, 0);
        
        return transaction;
      }
      
      // Mode fallback
      return this.transferCoinsFallback(fromUserId, toUserId, amount, description, context);
      
    } catch (error) {
      console.error('Erreur transfert coins:', error);
      throw error;
    }
  }
  
  /**
   * Fallback pour le transfert de coins
   */
  static transferCoinsFallback(fromUserId, toUserId, amount, description, context) {
    const memoryStorage = dbService.getMemoryStorage();
    
    if (!memoryStorage.wallets) {
      memoryStorage.wallets = new Map();
    }
    
    const fromWallet = memoryStorage.wallets.get(fromUserId);
    const toWallet = memoryStorage.wallets.get(toUserId);
    
    if (!fromWallet || fromWallet.nowee_coins < amount) {
      throw new Error('Solde insuffisant');
    }
    
    // Effectuer le transfert
    fromWallet.nowee_coins -= amount;
    fromWallet.total_spent += amount;
    fromWallet.updated_at = new Date();
    
    if (toWallet) {
      toWallet.nowee_coins += amount;
      toWallet.total_earned += amount;
      toWallet.updated_at = new Date();
    }
    
    // Enregistrer la transaction
    const transaction = {
      id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      from_user_id: fromUserId,
      to_user_id: toUserId,
      transaction_type: 'COIN_TRANSFER',
      nowee_coins: amount,
      time_credits: 0,
      description: description,
      metadata: context,
      status: 'COMPLETED',
      created_at: new Date(),
      completed_at: new Date()
    };
    
    if (!memoryStorage.transactions) {
      memoryStorage.transactions = [];
    }
    memoryStorage.transactions.push(transaction);
    
    return transaction;
  }
  
  /**
   * Met à jour le solde d'un portefeuille
   */
  static async updateWalletBalance(userId, coinsDelta, timeDelta = 0) {
    try {
      if (dbService.isUsingSupabase()) {
        // Version optimisée pour contourner l'erreur timestamp
        try {
          // Essayer d'abord la fonction PostgreSQL
          const { data, error } = await dbService.supabase
            .rpc('update_wallet_balance', {
              p_user_id: userId,
              p_coins_delta: coinsDelta,
              p_time_delta: timeDelta
            });

          if (error) throw error;
          return data;
        } catch (rpcError) {
          console.warn('Erreur RPC, utilisation de la méthode alternative:', rpcError.message);

          // Méthode alternative : mise à jour directe
          const { data: currentWallet, error: selectError } = await dbService.supabase
            .from('wallets')
            .select('*')
            .eq('user_id', userId)
            .single();

          if (selectError && selectError.code !== 'PGRST116') {
            throw selectError;
          }

          if (!currentWallet) {
            // Créer le portefeuille
            const { data, error } = await dbService.supabase
              .from('wallets')
              .insert([{
                user_id: userId,
                nowee_coins: Math.max(0, coinsDelta),
                time_credits: Math.max(0, timeDelta)
              }])
              .select()
              .single();

            if (error) throw error;
            return true;
          }

          // Vérifier les soldes
          if (currentWallet.nowee_coins + coinsDelta < 0) {
            return false;
          }

          if (currentWallet.time_credits + timeDelta < 0) {
            return false;
          }

          // Mettre à jour
          const { error: updateError } = await dbService.supabase
            .from('wallets')
            .update({
              nowee_coins: currentWallet.nowee_coins + coinsDelta,
              time_credits: currentWallet.time_credits + timeDelta,
              total_earned: currentWallet.total_earned + Math.max(0, coinsDelta),
              total_spent: currentWallet.total_spent + Math.max(0, -coinsDelta),
              updated_at: new Date().toISOString()
            })
            .eq('user_id', userId);

          if (updateError) throw updateError;
          return true;
        }
      }
      
      // Mode fallback
      const memoryStorage = dbService.getMemoryStorage();
      const wallet = memoryStorage.wallets?.get(userId);
      
      if (wallet) {
        wallet.nowee_coins += coinsDelta;
        wallet.time_credits += timeDelta;
        
        if (coinsDelta > 0) {
          wallet.total_earned += coinsDelta;
        } else {
          wallet.total_spent += Math.abs(coinsDelta);
        }
        
        wallet.updated_at = new Date();
      }
      
      return true;
      
    } catch (error) {
      console.error('Erreur mise à jour portefeuille:', error);
      return false;
    }
  }
  
  /**
   * Calcule le coût d'un service
   */
  static calculateServiceCost(baseRate, durationHours, urgency = 1, providerRating = 0, skillCategory = null) {
    let cost = baseRate * durationHours;
    
    // Multiplicateur d'urgence
    const urgencyMultiplier = ECONOMY_CONFIG.URGENCY_MULTIPLIERS[urgency] || 1.0;
    cost *= urgencyMultiplier;
    
    // Multiplicateur de compétence
    if (skillCategory && ECONOMY_CONFIG.SKILL_MULTIPLIERS[skillCategory]) {
      cost *= ECONOMY_CONFIG.SKILL_MULTIPLIERS[skillCategory];
    }
    
    // Bonus de réputation (10% par point au-dessus de 3)
    if (providerRating > 3) {
      const reputationBonus = (providerRating - 3) * 0.1;
      cost *= (1 + reputationBonus);
    }
    
    return Math.round(cost * 100) / 100; // Arrondir à 2 décimales
  }
  
  /**
   * Récompense un utilisateur
   */
  static async rewardUser(userId, actionType, amount, description, context = {}) {
    try {
      // Enregistrer la récompense
      await this.recordTransaction({
        to_user_id: userId,
        transaction_type: 'REWARD',
        nowee_coins: amount,
        description: description,
        metadata: { action_type: actionType, ...context },
        status: 'COMPLETED'
      });
      
      // Mettre à jour le portefeuille
      await this.updateWalletBalance(userId, amount, 0);
      
      console.log(`💰 Récompense: ${amount} NoweeCoins pour ${actionType} - ${userId}`);
      
    } catch (error) {
      console.error('Erreur récompense utilisateur:', error);
    }
  }
  
  /**
   * Applique une pénalité à un utilisateur
   */
  static async penalizeUser(userId, actionType, amount, description, context = {}) {
    try {
      const wallet = await this.getWallet(userId);
      const penaltyAmount = Math.min(Math.abs(amount), wallet.nowee_coins); // Ne pas aller en négatif
      
      // Enregistrer la pénalité
      await this.recordTransaction({
        from_user_id: userId,
        transaction_type: 'PENALTY',
        nowee_coins: penaltyAmount,
        description: description,
        metadata: { action_type: actionType, ...context },
        status: 'COMPLETED'
      });
      
      // Mettre à jour le portefeuille
      await this.updateWalletBalance(userId, -penaltyAmount, 0);
      
      console.log(`⚠️ Pénalité: ${penaltyAmount} NoweeCoins pour ${actionType} - ${userId}`);
      
    } catch (error) {
      console.error('Erreur pénalité utilisateur:', error);
    }
  }
  
  /**
   * Enregistre une transaction
   */
  static async recordTransaction(transactionData) {
    try {
      if (dbService.isUsingSupabase()) {
        const { data, error } = await dbService.supabase
          .from('transactions')
          .insert([transactionData])
          .select()
          .single();
        
        if (error) throw error;
        return data;
      }
      
      // Mode fallback
      const memoryStorage = dbService.getMemoryStorage();
      if (!memoryStorage.transactions) {
        memoryStorage.transactions = [];
      }
      
      const transaction = {
        id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...transactionData,
        created_at: new Date(),
        completed_at: transactionData.status === 'COMPLETED' ? new Date() : null
      };
      
      memoryStorage.transactions.push(transaction);
      return transaction;
      
    } catch (error) {
      console.error('Erreur enregistrement transaction:', error);
      return null;
    }
  }
  
  /**
   * Obtient l'historique des transactions d'un utilisateur
   */
  static async getTransactionHistory(userId, limit = 20) {
    try {
      if (dbService.isUsingSupabase()) {
        const { data, error } = await dbService.supabase
          .from('transactions')
          .select('*')
          .or(`from_user_id.eq.${userId},to_user_id.eq.${userId}`)
          .order('created_at', { ascending: false })
          .limit(limit);
        
        if (error) throw error;
        return data;
      }
      
      // Mode fallback
      const memoryStorage = dbService.getMemoryStorage();
      const transactions = memoryStorage.transactions || [];
      
      return transactions
        .filter(tx => tx.from_user_id === userId || tx.to_user_id === userId)
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, limit);
      
    } catch (error) {
      console.error('Erreur historique transactions:', error);
      return [];
    }
  }
  
  /**
   * Traite la fin d'une aide avec récompenses automatiques
   */
  static async processHelpCompletion(matchId, rating) {
    try {
      // Récupérer les détails du match
      const match = await this.getMatchDetails(matchId);
      if (!match) return;
      
      const { requester_id, provider_id } = match;
      
      // Récompense de base pour l'aide terminée
      await this.rewardUser(
        provider_id,
        'HELP_COMPLETED',
        ECONOMY_CONFIG.HELP_COMPLETION_REWARD,
        'Récompense pour aide terminée',
        { match_id: matchId }
      );
      
      // Bonus pour bonne évaluation
      if (rating >= 4) {
        await this.rewardUser(
          provider_id,
          'GOOD_RATING',
          ECONOMY_CONFIG.GOOD_RATING_BONUS,
          `Bonus pour excellente évaluation (${rating}/5)`,
          { match_id: matchId, rating }
        );
      }
      
      // Pénalité pour mauvaise évaluation
      if (rating <= 2) {
        await this.penalizeUser(
          provider_id,
          'BAD_RATING',
          ECONOMY_CONFIG.BAD_RATING_PENALTY,
          `Pénalité pour mauvaise évaluation (${rating}/5)`,
          { match_id: matchId, rating }
        );
      }
      
    } catch (error) {
      console.error('Erreur traitement fin d\'aide:', error);
    }
  }
  
  /**
   * Récupère les détails d'un match
   */
  static async getMatchDetails(matchId) {
    try {
      if (dbService.isUsingSupabase()) {
        const { data, error } = await dbService.supabase
          .from('matches')
          .select('*')
          .eq('id', matchId)
          .single();
        
        if (error) throw error;
        return data;
      }
      
      // Mode fallback
      const memoryStorage = dbService.getMemoryStorage();
      return Array.from(memoryStorage.matches.values()).find(m => m.id === matchId);
      
    } catch (error) {
      console.error('Erreur récupération match:', error);
      return null;
    }
  }
  
  /**
   * Obtient les statistiques économiques globales
   */
  static async getEconomyStats() {
    try {
      if (dbService.isUsingSupabase()) {
        const [walletsResult, transactionsResult] = await Promise.all([
          dbService.supabase.from('wallets').select('nowee_coins, time_credits'),
          dbService.supabase.from('transactions').select('nowee_coins, transaction_type')
        ]);
        
        const wallets = walletsResult.data || [];
        const transactions = transactionsResult.data || [];
        
        return {
          totalCoinsInCirculation: wallets.reduce((sum, w) => sum + (w.nowee_coins || 0), 0),
          totalTimeCredits: wallets.reduce((sum, w) => sum + (w.time_credits || 0), 0),
          totalTransactions: transactions.length,
          averageWalletBalance: wallets.length > 0 ? 
            wallets.reduce((sum, w) => sum + (w.nowee_coins || 0), 0) / wallets.length : 0
        };
      }
      
      // Mode fallback
      const memoryStorage = dbService.getMemoryStorage();
      const wallets = Array.from(memoryStorage.wallets?.values() || []);
      const transactions = memoryStorage.transactions || [];
      
      return {
        totalCoinsInCirculation: wallets.reduce((sum, w) => sum + w.nowee_coins, 0),
        totalTimeCredits: wallets.reduce((sum, w) => sum + w.time_credits, 0),
        totalTransactions: transactions.length,
        averageWalletBalance: wallets.length > 0 ? 
          wallets.reduce((sum, w) => sum + w.nowee_coins, 0) / wallets.length : 0
      };
      
    } catch (error) {
      console.error('Erreur statistiques économie:', error);
      return {
        totalCoinsInCirculation: 0,
        totalTimeCredits: 0,
        totalTransactions: 0,
        averageWalletBalance: 0
      };
    }
  }
}

export default EconomyService;
