<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Tests Nowee - Interface Interactive</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .test-card h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: transform 0.2s;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .result.success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        
        .result.error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        
        .result.loading {
            background: #fff3e0;
            border: 1px solid #ff9800;
            color: #ef6c00;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            color: white;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .chat-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .chat-messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
        }
        
        .message.user {
            background: #e3f2fd;
            margin-left: 20px;
        }
        
        .message.ai {
            background: #f3e5f5;
            margin-right: 20px;
        }
        
        .chat-input {
            display: flex;
            gap: 10px;
        }
        
        .chat-input input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Tests Nowee - Interface Interactive</h1>
            <p>Testez toutes les fonctionnalités révolutionnaires de Nowee</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value" id="users-stat">1,250</div>
                <div>Utilisateurs</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="helps-stat">3,420</div>
                <div>Aides</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="coins-stat">125,000</div>
                <div>NoweeCoins</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="communities-stat">45</div>
                <div>Communautés</div>
            </div>
        </div>
        
        <div class="test-grid">
            <!-- Test Connectivité -->
            <div class="test-card">
                <h3>🔗 Test Connectivité APIs</h3>
                <button class="test-button" onclick="testConnectivity()">Tester OpenAI + Twilio</button>
                <div id="connectivity-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- Test Portefeuille -->
            <div class="test-card">
                <h3>💰 Test Portefeuille</h3>
                <button class="test-button" onclick="testWallet()">Voir Portefeuille</button>
                <button class="test-button" onclick="testTransfer()">Test Transfert</button>
                <div id="wallet-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- Test Troc -->
            <div class="test-card">
                <h3>🔄 Test Système Troc</h3>
                <button class="test-button" onclick="testBarter()">Voir Propositions</button>
                <button class="test-button" onclick="testSuggestions()">Suggestions IA</button>
                <div id="barter-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- Test Besoins -->
            <div class="test-card">
                <h3>🗺️ Test Besoins Locaux</h3>
                <button class="test-button" onclick="testNeeds()">Voir Besoins</button>
                <button class="test-button" onclick="createNeed()">Créer Besoin</button>
                <div id="needs-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- Test Statistiques -->
            <div class="test-card">
                <h3>📊 Test Statistiques</h3>
                <button class="test-button" onclick="testStats()">Stats Avancées</button>
                <button class="test-button" onclick="testAllFeatures()">Test Complet</button>
                <div id="stats-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- Test WhatsApp -->
            <div class="test-card">
                <h3>📱 Test WhatsApp Bot</h3>
                <button class="test-button" onclick="testWhatsApp()">Simuler Message</button>
                <div id="whatsapp-result" class="result" style="display: none;"></div>
            </div>
        </div>
        
        <!-- Chat IA Interactif -->
        <div class="chat-container">
            <h3>🤖 Chat IA Nowee - Test Interactif</h3>
            <div class="chat-messages" id="chat-messages">
                <div class="message ai">
                    <strong>Nowee:</strong> Salut ! Je suis Nowee, ton assistant d'entraide locale au Sénégal. Comment puis-je t'aider aujourd'hui ? 🤝
                </div>
            </div>
            <div class="chat-input">
                <input type="text" id="chat-input" placeholder="Tapez votre message..." onkeypress="handleChatKeyPress(event)">
                <button class="test-button" onclick="sendChatMessage()">Envoyer</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        
        // Animation des statistiques
        function animateStats() {
            const stats = {
                users: { element: 'users-stat', value: 1250, increment: 2 },
                helps: { element: 'helps-stat', value: 3420, increment: 3 },
                coins: { element: 'coins-stat', value: 125000, increment: 50 },
                communities: { element: 'communities-stat', value: 45, increment: 0.1 }
            };
            
            setInterval(() => {
                Object.values(stats).forEach(stat => {
                    stat.value += Math.random() * stat.increment;
                    document.getElementById(stat.element).textContent = 
                        stat.element === 'coins-stat' ? 
                        Math.floor(stat.value).toLocaleString() : 
                        Math.floor(stat.value).toLocaleString();
                });
            }, 3000);
        }
        
        // Fonctions de test
        async function testConnectivity() {
            showLoading('connectivity-result');
            try {
                const response = await fetch(`${API_BASE}/api/test/connectivity`);
                const data = await response.json();
                showResult('connectivity-result', data, 'success');
            } catch (error) {
                showResult('connectivity-result', { error: error.message }, 'error');
            }
        }
        
        async function testWallet() {
            showLoading('wallet-result');
            try {
                const response = await fetch(`${API_BASE}/api/economy/wallet/+221701234567`);
                const data = await response.json();
                showResult('wallet-result', data, 'success');
            } catch (error) {
                showResult('wallet-result', { error: error.message }, 'error');
            }
        }
        
        async function testBarter() {
            showLoading('barter-result');
            try {
                const response = await fetch(`${API_BASE}/api/economy/barter/+221701234567`);
                const data = await response.json();
                showResult('barter-result', data, 'success');
            } catch (error) {
                showResult('barter-result', { error: error.message }, 'error');
            }
        }
        
        async function testSuggestions() {
            showLoading('barter-result');
            try {
                const response = await fetch(`${API_BASE}/api/economy/barter/+221701234567/suggestions`);
                const data = await response.json();
                showResult('barter-result', data, 'success');
            } catch (error) {
                showResult('barter-result', { error: error.message }, 'error');
            }
        }
        
        async function testNeeds() {
            showLoading('needs-result');
            try {
                const response = await fetch(`${API_BASE}/api/needs`);
                const data = await response.json();
                showResult('needs-result', data, 'success');
            } catch (error) {
                showResult('needs-result', { error: error.message }, 'error');
            }
        }
        
        async function testStats() {
            showLoading('stats-result');
            try {
                const response = await fetch(`${API_BASE}/api/stats/advanced`);
                const data = await response.json();
                showResult('stats-result', data, 'success');
            } catch (error) {
                showResult('stats-result', { error: error.message }, 'error');
            }
        }

        async function testTransfer() {
            showLoading('wallet-result');
            try {
                const response = await fetch(`${API_BASE}/api/economy/transfer`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        fromPhone: '+221701234567',
                        toPhone: '+221701234568',
                        amount: 25,
                        description: 'Test de transfert'
                    })
                });
                const data = await response.json();
                showResult('wallet-result', data, 'success');
            } catch (error) {
                showResult('wallet-result', { error: error.message }, 'error');
            }
        }

        async function createNeed() {
            showLoading('needs-result');
            try {
                const response = await fetch(`${API_BASE}/api/needs`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        phone: '+221701234567',
                        title: 'Test - Aide pour jardinage',
                        description: 'Je cherche quelqu\'un pour m\'aider à entretenir mon jardin',
                        category: 'SERVICE',
                        urgency: 'low',
                        latitude: 14.6928,
                        longitude: -17.4467,
                        address: 'Dakar, Sénégal'
                    })
                });
                const data = await response.json();
                showResult('needs-result', data, 'success');
            } catch (error) {
                showResult('needs-result', { error: error.message }, 'error');
            }
        }

        async function testAllFeatures() {
            showLoading('stats-result');
            try {
                const response = await fetch(`${API_BASE}/api/test/all-features`);
                const data = await response.json();
                showResult('stats-result', data, 'success');
            } catch (error) {
                showResult('stats-result', { error: error.message }, 'error');
            }
        }
        
        async function testWhatsApp() {
            showLoading('whatsapp-result');
            try {
                const response = await fetch(`${API_BASE}/api/whatsapp/webhook`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        Body: 'Salut Nowee ! J\'ai besoin d\'aide pour réparer mon téléphone',
                        From: '+221701234567'
                    })
                });
                const data = await response.json();
                showResult('whatsapp-result', data, 'success');
            } catch (error) {
                showResult('whatsapp-result', { error: error.message }, 'error');
            }
        }
        
        async function sendChatMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            if (!message) return;
            
            // Afficher le message utilisateur
            addChatMessage(message, 'user');
            input.value = '';
            
            // Afficher "en cours de frappe"
            const typingDiv = addChatMessage('Nowee est en train d\'écrire...', 'ai', true);
            
            try {
                const response = await fetch(`${API_BASE}/api/chat/ai`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        phone: '+221701234567',
                        context: 'test_interface'
                    })
                });
                const data = await response.json();
                
                // Supprimer le message "en cours de frappe"
                typingDiv.remove();
                
                // Afficher la réponse
                addChatMessage(data.response || 'Erreur de réponse', 'ai');
            } catch (error) {
                typingDiv.remove();
                addChatMessage('Erreur: ' + error.message, 'ai');
            }
        }
        
        function addChatMessage(message, sender, isTemporary = false) {
            const messagesDiv = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.innerHTML = `<strong>${sender === 'user' ? 'Vous' : 'Nowee'}:</strong> ${message}`;
            
            if (isTemporary) {
                messageDiv.style.opacity = '0.7';
                messageDiv.style.fontStyle = 'italic';
            }
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            
            return messageDiv;
        }
        
        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendChatMessage();
            }
        }
        
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result loading';
            element.textContent = 'Chargement...';
        }
        
        function showResult(elementId, data, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = JSON.stringify(data, null, 2);
        }
        
        // Démarrer l'animation des stats
        animateStats();
        
        // Message de bienvenue
        console.log('🚀 Interface de test Nowee chargée !');
        console.log('🧪 Testez toutes les fonctionnalités via les boutons');
        console.log('🤖 Chattez avec l\'IA en bas de page');
    </script>
</body>
</html>
