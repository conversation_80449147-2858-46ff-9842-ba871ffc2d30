#!/usr/bin/env node

/**
 * Bot simple pour tester le système de base de données
 */

import 'dotenv/config';
import express from 'express';
import bodyParser from 'body-parser';
import { dbService } from './src/services/databaseServiceUnified.js';

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

// Route de santé
app.get('/health', async (req, res) => {
  try {
    const stats = await dbService.getGlobalStats();
    
    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      database: dbService.isUsingSupabase() ? 'Supabase' : 'Memory',
      stats: stats,
      version: '1.0.0'
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      error: error.message
    });
  }
});

// Route de test utilisateur
app.post('/test-user', async (req, res) => {
  try {
    const { phone, name } = req.body;
    
    if (!phone) {
      return res.status(400).json({
        success: false,
        error: 'Phone requis'
      });
    }
    
    const user = await dbService.getUserProfile(phone);
    
    res.json({
      success: true,
      user: {
        id: user.id,
        phone: user.phone,
        name: user.name,
        created_at: user.created_at
      }
    });
    
  } catch (error) {
    console.error('Erreur test-user:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route de test besoin
app.post('/test-need', async (req, res) => {
  try {
    const { phone, description } = req.body;
    
    if (!phone || !description) {
      return res.status(400).json({
        success: false,
        error: 'Phone et description requis'
      });
    }
    
    const need = await dbService.recordUserNeed(phone, {
      type: 'GENERAL',
      description: description,
      urgency: 1
    });
    
    res.json({
      success: true,
      need: {
        id: need.id,
        title: need.title,
        description: need.description,
        category: need.category
      }
    });
    
  } catch (error) {
    console.error('Erreur test-need:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route de test matching
app.post('/test-matching', async (req, res) => {
  try {
    const { phone1, phone2, needDesc, offerDesc } = req.body;
    
    // Créer un besoin
    const need = await dbService.recordUserNeed(phone1, {
      type: 'MATERIAL',
      description: needDesc || 'J\'ai besoin d\'une perceuse',
      urgency: 2
    });
    
    // Créer une offre
    const offer = await dbService.recordUserOffer(phone2, {
      type: 'MATERIAL',
      description: offerDesc || 'Je peux prêter ma perceuse',
    });
    
    // Tester le matching
    const matches = await dbService.findMatches(need.id, 20);
    
    res.json({
      success: true,
      need: { id: need.id, title: need.title },
      offer: { id: offer.id, title: offer.title },
      matches: matches.map(m => ({
        id: m.id,
        title: m.title,
        score: m.match_score,
        distance: m.distance_km
      }))
    });
    
  } catch (error) {
    console.error('Erreur test-matching:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Bot Nowee simple démarré sur le port ${PORT}`);
  console.log(`📊 Base de données: ${dbService.isUsingSupabase() ? 'Supabase' : 'Mémoire'}`);
  console.log(`🔗 Health: http://localhost:${PORT}/health`);
  console.log(`👤 Test user: POST http://localhost:${PORT}/test-user`);
  console.log(`📋 Test need: POST http://localhost:${PORT}/test-need`);
  console.log(`🎯 Test matching: POST http://localhost:${PORT}/test-matching`);
});
