/**
 * NOWEE PRO - Serveur Backend World-Class
 * Architecture scalable et modulaire pour lancement mondial
 * 
 * @version 2.0.0
 * <AUTHOR> Team
 * @license MIT
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import morgan from 'morgan';
import winston from 'winston';
import { body, validationResult } from 'express-validator';
import dotenv from 'dotenv';

dotenv.config();

// ===== CONFIGURATION PROFESSIONNELLE =====

const config = {
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',
  apiVersion: 'v1',
  
  // Limites et sécurité
  rateLimits: {
    general: { windowMs: 15 * 60 * 1000, max: 100 },
    auth: { windowMs: 15 * 60 * 1000, max: 5 },
    api: { windowMs: 1 * 60 * 1000, max: 60 }
  },
  
  // Cache et performance
  cache: {
    ttl: 300, // 5 minutes
    maxSize: 1000
  },
  
  // APIs externes
  apis: {
    openai: {
      key: process.env.OPENAI_API_KEY,
      model: 'gpt-3.5-turbo',
      maxTokens: 200,
      temperature: 0.8
    },
    twilio: {
      accountSid: process.env.TWILIO_ACCOUNT_SID,
      authToken: process.env.TWILIO_AUTH_TOKEN,
      phoneNumber: process.env.TWILIO_PHONE_NUMBER
    }
  }
};

// ===== LOGGER PROFESSIONNEL =====

const logger = winston.createLogger({
  level: config.nodeEnv === 'production' ? 'info' : 'debug',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.colorize({ all: true })
  ),
  defaultMeta: { service: 'nowee-api', version: '2.0.0' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// ===== INITIALISATION EXPRESS =====

const app = express();

// Middleware de sécurité
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.openai.com"]
    }
  }
}));

// Compression et performance
app.use(compression());

// CORS configuré pour production
app.use(cors({
  origin: config.nodeEnv === 'production' 
    ? ['https://nowee.app', 'https://www.nowee.app']
    : ['http://localhost:3000', 'http://localhost:3001', 'http://127.0.0.1:5500'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Parsing et limites
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging des requêtes
app.use(morgan('combined', {
  stream: { write: message => logger.info(message.trim()) }
}));

// Rate limiting intelligent
const createRateLimit = (options) => rateLimit({
  ...options,
  message: {
    error: 'Trop de requêtes',
    message: 'Veuillez réessayer plus tard',
    retryAfter: Math.ceil(options.windowMs / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use('/api/', createRateLimit(config.rateLimits.api));
app.use('/api/auth/', createRateLimit(config.rateLimits.auth));

// ===== SERVICES ET UTILITAIRES =====

class CacheService {
  constructor() {
    this.cache = new Map();
    this.maxSize = config.cache.maxSize;
    this.ttl = config.cache.ttl * 1000; // Convert to ms
  }
  
  set(key, value, customTtl = null) {
    const ttl = customTtl || this.ttl;
    const expiry = Date.now() + ttl;
    
    // Éviter le débordement de cache
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, { value, expiry });
    logger.debug(`Cache SET: ${key}`);
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      logger.debug(`Cache EXPIRED: ${key}`);
      return null;
    }
    
    logger.debug(`Cache HIT: ${key}`);
    return item.value;
  }
  
  delete(key) {
    const deleted = this.cache.delete(key);
    if (deleted) logger.debug(`Cache DELETE: ${key}`);
    return deleted;
  }
  
  clear() {
    this.cache.clear();
    logger.info('Cache cleared');
  }
  
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      ttl: this.ttl / 1000
    };
  }
}

class ResponseFormatter {
  static success(data, message = 'Success', meta = {}) {
    return {
      success: true,
      message,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        version: '2.0.0',
        ...meta
      }
    };
  }
  
  static error(message, code = 'INTERNAL_ERROR', details = null) {
    return {
      success: false,
      error: {
        code,
        message,
        details,
        timestamp: new Date().toISOString()
      }
    };
  }
  
  static paginated(data, page, limit, total) {
    return this.success(data, 'Data retrieved successfully', {
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    });
  }
}

class ValidationService {
  static validateRequest(validations) {
    return async (req, res, next) => {
      await Promise.all(validations.map(validation => validation.run(req)));
      
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json(
          ResponseFormatter.error(
            'Données invalides',
            'VALIDATION_ERROR',
            errors.array()
          )
        );
      }
      
      next();
    };
  }
}

// Instances des services
const cache = new CacheService();

// ===== DONNÉES MOCK PROFESSIONNELLES =====

const mockDatabase = {
  users: new Map([
    ['+************', {
      id: 'user_001',
      phone: '+************',
      name: 'Aminata Diallo',
      email: '<EMAIL>',
      location: {
        latitude: 14.6928,
        longitude: -17.4467,
        address: 'Plateau, Dakar, Sénégal',
        city: 'Dakar',
        country: 'Sénégal'
      },
      profile: {
        reputation: 4.8,
        level: 'Expert',
        joinedAt: '2024-01-15T10:00:00Z',
        lastActive: new Date().toISOString(),
        verified: true,
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Aminata'
      },
      economy: {
        noweeCoins: 150,
        timeCredits: 2.5,
        totalEarned: 200,
        totalSpent: 50,
        transactions: 25,
        rank: 15
      },
      skills: ['aide ménagère', 'cuisine', 'garde enfants', 'français'],
      languages: ['français', 'wolof', 'anglais'],
      preferences: {
        notifications: true,
        radius: 5000, // 5km
        categories: ['SERVICE', 'EDUCATION', 'HEALTH']
      }
    }],
    ['+221701234568', {
      id: 'user_002',
      phone: '+221701234568',
      name: 'Moussa Sow',
      email: '<EMAIL>',
      location: {
        latitude: 14.6937,
        longitude: -17.4441,
        address: 'Médina, Dakar, Sénégal',
        city: 'Dakar',
        country: 'Sénégal'
      },
      profile: {
        reputation: 4.9,
        level: 'Expert',
        joinedAt: '2024-02-01T08:30:00Z',
        lastActive: new Date().toISOString(),
        verified: true,
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Moussa'
      },
      economy: {
        noweeCoins: 200,
        timeCredits: 1.0,
        totalEarned: 300,
        totalSpent: 100,
        transactions: 42,
        rank: 8
      },
      skills: ['réparation', 'électricité', 'plomberie', 'mécanique'],
      languages: ['français', 'wolof', 'anglais'],
      preferences: {
        notifications: true,
        radius: 10000, // 10km
        categories: ['TECHNICAL', 'SERVICE', 'EMERGENCY']
      }
    }]
  ]),
  
  needs: [
    {
      id: 'need_001',
      userId: 'user_001',
      phone: '+************',
      title: 'Aide pour déménagement urgent',
      description: 'Je cherche 2-3 personnes fortes pour m\'aider à déménager mes affaires ce weekend. Appartement au 3ème étage sans ascenseur.',
      category: 'SERVICE',
      subcategory: 'MOVING',
      urgency: 'high',
      status: 'active',
      location: {
        latitude: 14.6928,
        longitude: -17.4467,
        address: 'Plateau, Dakar, Sénégal',
        radius: 5000
      },
      schedule: {
        startDate: '2025-07-19T08:00:00Z',
        endDate: '2025-07-19T18:00:00Z',
        flexible: true
      },
      reward: {
        noweeCoins: 75,
        timeCredits: 3,
        additional: 'Repas offert + transport remboursé'
      },
      requirements: {
        minAge: 18,
        skills: ['force physique'],
        experience: false
      },
      createdAt: '2025-07-16T10:00:00Z',
      updatedAt: '2025-07-16T10:00:00Z',
      views: 23,
      applications: 5
    },
    {
      id: 'need_002',
      userId: 'user_002',
      phone: '+221701234568',
      title: 'Cours de français pour débutants',
      description: 'J\'offre des cours de français personnalisés pour adultes débutants. Méthode interactive et adaptée au contexte sénégalais.',
      category: 'EDUCATION',
      subcategory: 'LANGUAGE',
      urgency: 'low',
      status: 'active',
      location: {
        latitude: 14.6937,
        longitude: -17.4441,
        address: 'Médina, Dakar, Sénégal',
        radius: 8000
      },
      schedule: {
        startDate: '2025-07-20T16:00:00Z',
        endDate: '2025-07-20T18:00:00Z',
        flexible: true,
        recurring: 'weekly'
      },
      reward: {
        noweeCoins: 40,
        timeCredits: 1.5,
        additional: 'Matériel pédagogique fourni'
      },
      requirements: {
        minAge: 16,
        skills: [],
        experience: false
      },
      createdAt: '2025-07-15T14:30:00Z',
      updatedAt: '2025-07-15T14:30:00Z',
      views: 18,
      applications: 3
    }
  ],
  
  transactions: [],
  chatHistory: [],
  analytics: {
    daily: {
      users: 1250,
      activeUsers: 375,
      newUsers: 45,
      needs: 89,
      completedNeeds: 67,
      transactions: 156,
      noweeCoinsCirculated: 12500
    },
    weekly: {
      growth: 15.2,
      retention: 78.5,
      satisfaction: 94.8
    }
  }
};

// ===== ROUTES API PROFESSIONNELLES =====

// Health Check Avancé
app.get('/health', (req, res) => {
  const healthData = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    environment: config.nodeEnv,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    services: {
      database: 'connected',
      cache: 'active',
      openai: config.apis.openai.key ? 'configured' : 'missing',
      twilio: config.apis.twilio.accountSid ? 'configured' : 'missing'
    },
    cache: cache.getStats()
  };
  
  res.json(ResponseFormatter.success(healthData, 'Service healthy'));
});

// API Info
app.get(`/api/${config.apiVersion}`, (req, res) => {
  res.json(ResponseFormatter.success({
    name: 'Nowee API',
    version: '2.0.0',
    description: 'API révolutionnaire pour l\'entraide locale avec IA et mesh networking',
    endpoints: {
      health: '/health',
      chat: `/api/${config.apiVersion}/chat`,
      economy: `/api/${config.apiVersion}/economy`,
      needs: `/api/${config.apiVersion}/needs`,
      users: `/api/${config.apiVersion}/users`,
      analytics: `/api/${config.apiVersion}/analytics`
    },
    features: [
      'IA Contextuelle',
      'Économie NoweeCoins',
      'Mesh Networking',
      'Géolocalisation',
      'Analytics Temps Réel'
    ]
  }));
});

// ===== MIDDLEWARE D'AUTHENTIFICATION =====

const authenticateUser = (req, res, next) => {
  const phone = req.headers['x-user-phone'] || req.body.phone || req.query.phone;
  
  if (!phone) {
    return res.status(401).json(
      ResponseFormatter.error('Numéro de téléphone requis', 'AUTH_REQUIRED')
    );
  }
  
  const user = mockDatabase.users.get(phone);
  if (!user) {
    return res.status(404).json(
      ResponseFormatter.error('Utilisateur non trouvé', 'USER_NOT_FOUND')
    );
  }
  
  req.user = user;
  next();
};

// ===== ROUTES CHAT IA =====

app.post(`/api/${config.apiVersion}/chat/ai`, 
  ValidationService.validateRequest([
    body('message').notEmpty().withMessage('Message requis'),
    body('phone').isMobilePhone().withMessage('Numéro de téléphone invalide')
  ]),
  async (req, res) => {
    try {
      const { message, phone, context } = req.body;
      const startTime = Date.now();
      
      // Cache check
      const cacheKey = `chat:${phone}:${Buffer.from(message).toString('base64').slice(0, 20)}`;
      const cachedResponse = cache.get(cacheKey);
      
      if (cachedResponse) {
        logger.info('Chat response served from cache', { phone, cacheKey });
        return res.json(ResponseFormatter.success(cachedResponse, 'Réponse IA (cache)'));
      }
      
      // Obtenir le profil utilisateur
      const user = mockDatabase.users.get(phone) || {
        name: 'Utilisateur',
        location: { address: 'Dakar, Sénégal' },
        profile: { reputation: 4.0 }
      };
      
      // Réponse simulée intelligente (en production, utiliser OpenAI)
      const responses = [
        `Salut ${user.name} ! Je suis ravi de t'aider. Pour ton besoin "${message.substring(0, 50)}...", je peux te connecter avec des personnes qualifiées près de ${user.location.address}. Veux-tu que je lance une recherche ?`,
        `Hello ${user.name} ! C'est exactement le genre de situation où Nowee excelle. Avec ta réputation de ${user.profile.reputation}/5, tu auras facilement des réponses. Laisse-moi voir qui peut t'aider dans ton quartier !`,
        `Bonjour ${user.name} ! Ton message me touche beaucoup. L'entraide, c'est l'essence même de Nowee. Je vais mobiliser notre communauté de ${mockDatabase.analytics.daily.users} membres pour t'aider rapidement.`,
        `Salut ! Parfait timing ${user.name} ! J'ai justement ${Math.floor(Math.random() * 10) + 5} personnes actives près de chez toi qui pourraient t'aider. Veux-tu que je leur envoie ta demande ?`
      ];
      
      const response = responses[Math.floor(Math.random() * responses.length)];
      const processingTime = Date.now() - startTime;
      
      const chatData = {
        response,
        model: 'nowee-ai-v2',
        tokensUsed: Math.floor(Math.random() * 100) + 50,
        processingTime,
        context: context || 'general',
        suggestions: [
          'Voir les aidants disponibles',
          'Ajuster ma demande',
          'Consulter les tarifs'
        ]
      };
      
      // Enregistrer dans l'historique
      mockDatabase.chatHistory.push({
        id: `chat_${Date.now()}`,
        phone,
        message,
        response,
        timestamp: new Date().toISOString(),
        context,
        processingTime
      });
      
      // Cache la réponse
      cache.set(cacheKey, chatData, 300); // 5 minutes
      
      logger.info('Chat AI response generated', { 
        phone, 
        messageLength: message.length, 
        processingTime 
      });
      
      res.json(ResponseFormatter.success(chatData, 'Réponse IA générée'));
      
    } catch (error) {
      logger.error('Chat AI error', { error: error.message, stack: error.stack });
      res.status(500).json(
        ResponseFormatter.error('Erreur lors du traitement de votre message', 'AI_ERROR')
      );
    }
  }
);

// ===== ROUTES ÉCONOMIE =====

// Portefeuille utilisateur
app.get(`/api/${config.apiVersion}/economy/wallet/:phone`, (req, res) => {
  try {
    const { phone } = req.params;
    const user = mockDatabase.users.get(phone);

    if (!user) {
      return res.status(404).json(
        ResponseFormatter.error('Utilisateur non trouvé', 'USER_NOT_FOUND')
      );
    }

    const walletData = {
      user: {
        id: user.id,
        name: user.name,
        phone: user.phone,
        avatar: user.profile.avatar,
        verified: user.profile.verified
      },
      balance: {
        noweeCoins: user.economy.noweeCoins,
        timeCredits: user.economy.timeCredits,
        totalValue: user.economy.noweeCoins + (user.economy.timeCredits * 50)
      },
      stats: {
        totalEarned: user.economy.totalEarned,
        totalSpent: user.economy.totalSpent,
        netBalance: user.economy.totalEarned - user.economy.totalSpent,
        transactions: user.economy.transactions,
        rank: user.economy.rank
      },
      reputation: {
        score: user.profile.reputation,
        level: user.profile.level,
        nextLevel: user.profile.reputation >= 4.8 ? 'Master' : 'Expert',
        progress: Math.round((user.profile.reputation % 1) * 100)
      }
    };

    logger.info('Wallet data retrieved', { phone, balance: walletData.balance.totalValue });

    res.json(ResponseFormatter.success(walletData, 'Portefeuille récupéré'));

  } catch (error) {
    logger.error('Wallet error', { error: error.message });
    res.status(500).json(
      ResponseFormatter.error('Erreur lors de la récupération du portefeuille', 'WALLET_ERROR')
    );
  }
});

// Transaction
app.post(`/api/${config.apiVersion}/economy/transaction`,
  ValidationService.validateRequest([
    body('from').isMobilePhone().withMessage('Numéro expéditeur invalide'),
    body('to').isMobilePhone().withMessage('Numéro destinataire invalide'),
    body('amount').isFloat({ min: 1 }).withMessage('Montant invalide'),
    body('type').isIn(['coins', 'credits']).withMessage('Type invalide')
  ]),
  (req, res) => {
    try {
      const { from, to, amount, type, description } = req.body;

      const fromUser = mockDatabase.users.get(from);
      const toUser = mockDatabase.users.get(to);

      if (!fromUser || !toUser) {
        return res.status(404).json(
          ResponseFormatter.error('Utilisateur non trouvé', 'USER_NOT_FOUND')
        );
      }

      // Vérifier le solde
      const balance = type === 'coins' ? fromUser.economy.noweeCoins : fromUser.economy.timeCredits;
      if (balance < amount) {
        return res.status(400).json(
          ResponseFormatter.error('Solde insuffisant', 'INSUFFICIENT_BALANCE')
        );
      }

      // Créer la transaction
      const transaction = {
        id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        from: from,
        to: to,
        amount: amount,
        type: type,
        description: description || 'Transaction Nowee',
        status: 'completed',
        timestamp: new Date().toISOString(),
        fees: Math.round(amount * 0.02), // 2% de frais
        reference: `NOW${Date.now()}`
      };

      // Mettre à jour les soldes
      if (type === 'coins') {
        fromUser.economy.noweeCoins -= amount;
        toUser.economy.noweeCoins += amount;
      } else {
        fromUser.economy.timeCredits -= amount;
        toUser.economy.timeCredits += amount;
      }

      // Enregistrer la transaction
      mockDatabase.transactions.push(transaction);

      logger.info('Transaction completed', {
        id: transaction.id,
        from,
        to,
        amount,
        type
      });

      res.json(ResponseFormatter.success(transaction, 'Transaction réalisée avec succès'));

    } catch (error) {
      logger.error('Transaction error', { error: error.message });
      res.status(500).json(
        ResponseFormatter.error('Erreur lors de la transaction', 'TRANSACTION_ERROR')
      );
    }
  }
);

// ===== ROUTES BESOINS =====

// Liste des besoins
app.get(`/api/${config.apiVersion}/needs`, (req, res) => {
  try {
    const {
      category,
      urgency,
      location,
      radius = 10000,
      page = 1,
      limit = 20,
      sort = 'recent'
    } = req.query;

    let needs = [...mockDatabase.needs];

    // Filtres
    if (category) {
      needs = needs.filter(need => need.category === category.toUpperCase());
    }

    if (urgency) {
      needs = needs.filter(need => need.urgency === urgency);
    }

    // Tri
    switch (sort) {
      case 'recent':
        needs.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        break;
      case 'urgent':
        const urgencyOrder = { emergency: 4, high: 3, medium: 2, low: 1 };
        needs.sort((a, b) => urgencyOrder[b.urgency] - urgencyOrder[a.urgency]);
        break;
      case 'reward':
        needs.sort((a, b) => b.reward.noweeCoins - a.reward.noweeCoins);
        break;
    }

    // Pagination
    const total = needs.length;
    const startIndex = (page - 1) * limit;
    const paginatedNeeds = needs.slice(startIndex, startIndex + parseInt(limit));

    // Enrichir avec les données utilisateur
    const enrichedNeeds = paginatedNeeds.map(need => {
      const user = mockDatabase.users.get(need.phone);
      return {
        ...need,
        requester: user ? {
          name: user.name,
          reputation: user.profile.reputation,
          avatar: user.profile.avatar,
          verified: user.profile.verified
        } : null
      };
    });

    logger.info('Needs retrieved', {
      total,
      page,
      limit,
      filters: { category, urgency, sort }
    });

    res.json(ResponseFormatter.paginated(enrichedNeeds, page, limit, total));

  } catch (error) {
    logger.error('Needs retrieval error', { error: error.message });
    res.status(500).json(
      ResponseFormatter.error('Erreur lors de la récupération des besoins', 'NEEDS_ERROR')
    );
  }
});

// Créer un besoin
app.post(`/api/${config.apiVersion}/needs`,
  ValidationService.validateRequest([
    body('title').notEmpty().withMessage('Titre requis'),
    body('description').isLength({ min: 10 }).withMessage('Description trop courte'),
    body('category').isIn(['SERVICE', 'EDUCATION', 'TECHNICAL', 'HEALTH', 'EMERGENCY']).withMessage('Catégorie invalide'),
    body('phone').isMobilePhone().withMessage('Numéro de téléphone invalide')
  ]),
  (req, res) => {
    try {
      const needData = req.body;
      const user = mockDatabase.users.get(needData.phone);

      if (!user) {
        return res.status(404).json(
          ResponseFormatter.error('Utilisateur non trouvé', 'USER_NOT_FOUND')
        );
      }

      const need = {
        id: `need_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId: user.id,
        ...needData,
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        views: 0,
        applications: 0,
        location: needData.location || user.location
      };

      mockDatabase.needs.push(need);

      logger.info('Need created', {
        id: need.id,
        userId: user.id,
        category: need.category
      });

      res.status(201).json(ResponseFormatter.success(need, 'Besoin créé avec succès'));

    } catch (error) {
      logger.error('Need creation error', { error: error.message });
      res.status(500).json(
        ResponseFormatter.error('Erreur lors de la création du besoin', 'NEED_CREATION_ERROR')
      );
    }
  }
);

// ===== ROUTES ANALYTICS =====

// Statistiques avancées
app.get(`/api/${config.apiVersion}/analytics/stats`, (req, res) => {
  try {
    const stats = {
      timestamp: new Date().toISOString(),
      community: {
        totalUsers: mockDatabase.users.size,
        activeUsers24h: mockDatabase.analytics.daily.activeUsers,
        newUsers24h: mockDatabase.analytics.daily.newUsers,
        totalNeeds: mockDatabase.needs.length,
        activeNeeds: mockDatabase.needs.filter(n => n.status === 'active').length,
        completedNeeds: mockDatabase.analytics.daily.completedNeeds,
        communities: 45,
        countries: 3
      },
      economy: {
        totalCoins: 125000,
        coinsInCirculation: 106250,
        averageWallet: 100,
        totalTransactions: mockDatabase.transactions.length,
        transactionsToday: mockDatabase.analytics.daily.transactions,
        volume24h: mockDatabase.analytics.daily.noweeCoinsCirculated,
        topEarners: 25
      },
      engagement: {
        dailyActiveUsers: mockDatabase.analytics.daily.activeUsers,
        weeklyRetention: mockDatabase.analytics.weekly.retention,
        monthlyGrowth: mockDatabase.analytics.weekly.growth,
        averageSessionTime: '12m 34s',
        satisfactionRate: mockDatabase.analytics.weekly.satisfaction / 100
      },
      ai: {
        totalConversations: mockDatabase.chatHistory.length,
        conversationsToday: Math.floor(mockDatabase.chatHistory.length * 0.2),
        averageResponseTime: '0.8s',
        satisfactionRate: 0.95,
        topQueries: ['aide déménagement', 'cours français', 'réparation']
      },
      mesh: {
        totalNodes: 5,
        activeNodes: 5,
        totalMessages: 0,
        messagesPerSecond: 2.5,
        networkHealth: 95,
        coverage: '100%'
      }
    };

    // Cache les stats pour 1 minute
    cache.set('analytics:stats', stats, 60);

    logger.info('Analytics stats retrieved');

    res.json(ResponseFormatter.success(stats, 'Statistiques récupérées'));

  } catch (error) {
    logger.error('Analytics error', { error: error.message });
    res.status(500).json(
      ResponseFormatter.error('Erreur lors de la récupération des statistiques', 'ANALYTICS_ERROR')
    );
  }
});

// ===== GESTION DES ERREURS =====

// Middleware de gestion d'erreurs
app.use((err, req, res, next) => {
  logger.error('Unhandled error', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method
  });

  res.status(500).json(
    ResponseFormatter.error('Erreur interne du serveur', 'INTERNAL_ERROR')
  );
});

// Route 404
app.use('*', (req, res) => {
  res.status(404).json(
    ResponseFormatter.error('Route non trouvée', 'NOT_FOUND', {
      path: req.originalUrl,
      method: req.method,
      availableEndpoints: [
        `GET /api/${config.apiVersion}`,
        `POST /api/${config.apiVersion}/chat/ai`,
        `GET /api/${config.apiVersion}/economy/wallet/:phone`,
        `POST /api/${config.apiVersion}/economy/transaction`,
        `GET /api/${config.apiVersion}/needs`,
        `POST /api/${config.apiVersion}/needs`,
        `GET /api/${config.apiVersion}/analytics/stats`
      ]
    })
  );
});

// ===== DÉMARRAGE DU SERVEUR =====

const server = app.listen(config.port, () => {
  logger.info('🚀 Nowee Pro Server Started', {
    port: config.port,
    environment: config.nodeEnv,
    version: '2.0.0',
    apiVersion: config.apiVersion,
    features: [
      'IA Contextuelle',
      'Économie NoweeCoins',
      'Analytics Avancés',
      'Cache Intelligent',
      'Validation Robuste'
    ]
  });

  logger.info('📊 Health Check: http://localhost:' + config.port + '/health');
  logger.info('🤖 Chat IA: POST http://localhost:' + config.port + `/api/${config.apiVersion}/chat/ai`);
  logger.info('💰 Économie: http://localhost:' + config.port + `/api/${config.apiVersion}/economy/wallet/+************`);
  logger.info('📈 Analytics: http://localhost:' + config.port + `/api/${config.apiVersion}/analytics/stats`);

  if (config.apis.openai.key) logger.info('✅ OpenAI configuré');
  if (config.apis.twilio.accountSid) logger.info('✅ Twilio configuré');

  logger.info('🎊 Nowee Pro prêt à révolutionner l\'entraide mondiale ! 🎊');
});

// Gestion gracieuse de l'arrêt
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

export default app;
