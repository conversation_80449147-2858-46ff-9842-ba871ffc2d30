/**
 * Composant WalletCard
 * Affiche un résumé du portefeuille utilisateur sur l'écran d'accueil
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import { useTheme } from '../services/ThemeService';
import { useNavigation } from '@react-navigation/native';

interface WalletData {
  nowee_coins: number;
  time_credits: number;
  total_earned: number;
  total_spent: number;
}

interface WalletCardProps {
  walletData: WalletData;
  onPress?: () => void;
}

const WalletCard: React.FC<WalletCardProps> = ({ walletData, onPress }) => {
  const { colors } = useTheme();
  const navigation = useNavigation();
  const scaleAnim = new Animated.Value(1);

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      navigation.navigate('Wallet' as never);
    }
  };

  const formatCoins = (amount: number): string => {
    if (amount >= 1000) {
      return `${(amount / 1000).toFixed(1)}k`;
    }
    return amount.toFixed(0);
  };

  const formatTime = (hours: number): string => {
    if (hours < 1) {
      return `${Math.round(hours * 60)}min`;
    }
    return `${hours.toFixed(1)}h`;
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={0.9}
    >
      <Animated.View 
        style={[
          styles.container,
          { transform: [{ scale: scaleAnim }] }
        ]}
      >
        <LinearGradient
          colors={[colors.primary, colors.secondary]}
          style={styles.gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.header}>
            <View style={styles.titleContainer}>
              <Icon name="account-balance-wallet" size={24} color="#FFFFFF" />
              <Text style={styles.title}>Mon Portefeuille</Text>
            </View>
            <Icon name="chevron-right" size={24} color="#FFFFFF" />
          </View>

          <View style={styles.balanceContainer}>
            <View style={styles.mainBalance}>
              <Text style={styles.balanceLabel}>NoweeCoins</Text>
              <Text style={styles.balanceAmount}>
                {formatCoins(walletData.nowee_coins)}
              </Text>
            </View>

            <View style={styles.timeBalance}>
              <Text style={styles.timeLabel}>Crédits Temps</Text>
              <Text style={styles.timeAmount}>
                {formatTime(walletData.time_credits)}
              </Text>
            </View>
          </View>

          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Icon name="trending-up" size={16} color="#FFFFFF" />
              <Text style={styles.statValue}>
                {formatCoins(walletData.total_earned)}
              </Text>
              <Text style={styles.statLabel}>Gagné</Text>
            </View>

            <View style={styles.statDivider} />

            <View style={styles.statItem}>
              <Icon name="trending-down" size={16} color="#FFFFFF" />
              <Text style={styles.statValue}>
                {formatCoins(walletData.total_spent)}
              </Text>
              <Text style={styles.statLabel}>Dépensé</Text>
            </View>

            <View style={styles.statDivider} />

            <View style={styles.statItem}>
              <Icon name="account-balance" size={16} color="#FFFFFF" />
              <Text style={styles.statValue}>
                {formatCoins(walletData.total_earned - walletData.total_spent)}
              </Text>
              <Text style={styles.statLabel}>Net</Text>
            </View>
          </View>

          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.actionButton}>
              <Icon name="send" size={18} color="#FFFFFF" />
              <Text style={styles.actionText}>Envoyer</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton}>
              <Icon name="qr-code" size={18} color="#FFFFFF" />
              <Text style={styles.actionText}>Recevoir</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton}>
              <Icon name="swap-horiz" size={18} color="#FFFFFF" />
              <Text style={styles.actionText}>Troquer</Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  gradient: {
    padding: 20,
    borderRadius: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginLeft: 8,
  },
  balanceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  mainBalance: {
    flex: 1,
  },
  balanceLabel: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.8,
    marginBottom: 4,
  },
  balanceAmount: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  timeBalance: {
    alignItems: 'flex-end',
  },
  timeLabel: {
    fontSize: 12,
    color: '#FFFFFF',
    opacity: 0.8,
    marginBottom: 4,
  },
  timeAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 4,
  },
  statLabel: {
    fontSize: 10,
    color: '#FFFFFF',
    opacity: 0.8,
    marginTop: 2,
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  actionText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
});

export { WalletCard };
export default WalletCard;
