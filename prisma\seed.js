/**
 * Script de seed pour la base de données Nowee
 * Initialise la base avec des données de test
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Initialisation de la base de données Nowee...');

  // Nettoyer les données existantes (attention en production !)
  if (process.env.NODE_ENV === 'development') {
    console.log('🧹 Nettoyage des données existantes...');
    
    await prisma.rating.deleteMany();
    await prisma.match.deleteMany();
    await prisma.message.deleteMany();
    await prisma.conversation.deleteMany();
    await prisma.need.deleteMany();
    await prisma.offer.deleteMany();
    await prisma.systemEvent.deleteMany();
    await prisma.user.deleteMany();
  }

  // Créer des utilisateurs de test
  console.log('👥 Création des utilisateurs de test...');
  
  const users = await Promise.all([
    prisma.user.create({
      data: {
        phone: '+221771234567',
        name: '<PERSON><PERSON><PERSON>',
        preferredLanguage: 'fr',
        location: {
          city: 'Dakar',
          country: 'Sénégal',
          coordinates: { latitude: 14.7167, longitude: -17.4677 }
        },
        messageCount: 15,
        helpGiven: 3,
        helpReceived: 2,
        rating: 4.8,
        badges: ['helper', 'active_user']
      }
    }),
    
    prisma.user.create({
      data: {
        phone: '+33123456789',
        name: 'Marie Dubois',
        preferredLanguage: 'fr',
        location: {
          city: 'Paris',
          country: 'France',
          coordinates: { latitude: 48.8566, longitude: 2.3522 }
        },
        messageCount: 8,
        helpGiven: 1,
        helpReceived: 1,
        rating: 5.0,
        badges: ['newcomer']
      }
    }),
    
    prisma.user.create({
      data: {
        phone: '+225123456789',
        name: 'Koffi Asante',
        preferredLanguage: 'fr',
        location: {
          city: 'Abidjan',
          country: 'Côte d\'Ivoire',
          coordinates: { latitude: 5.3600, longitude: -4.0083 }
        },
        messageCount: 25,
        helpGiven: 8,
        helpReceived: 3,
        rating: 4.9,
        badges: ['super_helper', 'community_leader']
      }
    })
  ]);

  console.log(`✅ ${users.length} utilisateurs créés`);

  // Créer des besoins de test
  console.log('🙋‍♂️ Création des besoins de test...');
  
  const needs = await Promise.all([
    prisma.need.create({
      data: {
        userId: users[0].id,
        type: 'MATERIAL',
        title: 'Perceuse pour travaux',
        description: 'J\'ai besoin d\'une perceuse pour quelques heures pour des travaux dans mon appartement',
        location: {
          city: 'Dakar',
          country: 'Sénégal',
          coordinates: { latitude: 14.7167, longitude: -17.4677 }
        },
        urgency: 2,
        status: 'OPEN'
      }
    }),
    
    prisma.need.create({
      data: {
        userId: users[1].id,
        type: 'SERVICE',
        title: 'Aide pour déménagement',
        description: 'Je cherche quelqu\'un pour m\'aider à déménager ce weekend',
        location: {
          city: 'Paris',
          country: 'France',
          coordinates: { latitude: 48.8566, longitude: 2.3522 }
        },
        urgency: 3,
        status: 'OPEN'
      }
    }),
    
    prisma.need.create({
      data: {
        userId: users[2].id,
        type: 'ADVICE',
        title: 'Conseil jardinage',
        description: 'Comment bien entretenir un jardin tropical ?',
        location: {
          city: 'Abidjan',
          country: 'Côte d\'Ivoire',
          coordinates: { latitude: 5.3600, longitude: -4.0083 }
        },
        urgency: 1,
        status: 'OPEN'
      }
    })
  ]);

  console.log(`✅ ${needs.length} besoins créés`);

  // Créer des offres de test
  console.log('🤝 Création des offres de test...');
  
  const offers = await Promise.all([
    prisma.offer.create({
      data: {
        userId: users[2].id,
        type: 'MATERIAL',
        title: 'Outils de bricolage disponibles',
        description: 'J\'ai une perceuse, une scie et d\'autres outils à prêter',
        location: {
          city: 'Abidjan',
          country: 'Côte d\'Ivoire',
          coordinates: { latitude: 5.3600, longitude: -4.0083 }
        },
        availability: {
          weekdays: true,
          weekends: true,
          hours: '8h-18h'
        },
        conditions: 'Retour dans les 24h',
        status: 'AVAILABLE'
      }
    }),
    
    prisma.offer.create({
      data: {
        userId: users[0].id,
        type: 'SERVICE',
        title: 'Aide au déménagement',
        description: 'Je peux aider pour les déménagements, j\'ai un véhicule',
        location: {
          city: 'Dakar',
          country: 'Sénégal',
          coordinates: { latitude: 14.7167, longitude: -17.4677 }
        },
        availability: {
          weekends: true,
          advance_notice: '2 jours'
        },
        conditions: 'Participation aux frais d\'essence',
        status: 'AVAILABLE'
      }
    })
  ]);

  console.log(`✅ ${offers.length} offres créées`);

  // Créer des conversations de test
  console.log('💬 Création des conversations de test...');
  
  const conversations = await Promise.all([
    prisma.conversation.create({
      data: {
        userId: users[0].id,
        context: { topic: 'material_need', last_intent: 'seeking_drill' }
      }
    }),
    
    prisma.conversation.create({
      data: {
        userId: users[1].id,
        context: { topic: 'service_need', last_intent: 'moving_help' }
      }
    })
  ]);

  // Créer des messages de test
  console.log('📝 Création des messages de test...');
  
  await Promise.all([
    prisma.message.create({
      data: {
        conversationId: conversations[0].id,
        senderId: users[0].id,
        content: 'J\'ai besoin d\'une perceuse pour aujourd\'hui',
        messageType: 'TEXT',
        analysis: {
          needType: 'MATERIAL',
          urgency: 3,
          sentiment: 'neutral',
          keywords: ['perceuse', 'aujourd\'hui']
        }
      }
    }),
    
    prisma.message.create({
      data: {
        conversationId: conversations[0].id,
        content: '🔧 Je vais chercher qui a une perceuse près de chez toi ! En attendant, as-tu vérifié chez tes voisins directs ?',
        messageType: 'TEXT'
      }
    }),
    
    prisma.message.create({
      data: {
        conversationId: conversations[1].id,
        senderId: users[1].id,
        content: 'Qui peut m\'aider à déménager ce weekend ?',
        messageType: 'TEXT',
        analysis: {
          needType: 'SERVICE',
          urgency: 2,
          sentiment: 'neutral',
          keywords: ['déménager', 'weekend']
        }
      }
    })
  ]);

  console.log(`✅ Messages de test créés`);

  // Créer une correspondance de test
  console.log('🔗 Création d\'une correspondance de test...');
  
  const match = await prisma.match.create({
    data: {
      needId: needs[0].id,
      offerId: offers[0].id,
      userId: users[0].id,
      status: 'PROPOSED'
    }
  });

  console.log(`✅ Correspondance créée`);

  // Créer des évaluations de test
  console.log('⭐ Création des évaluations de test...');
  
  await Promise.all([
    prisma.rating.create({
      data: {
        giverId: users[0].id,
        receiverId: users[2].id,
        rating: 5.0,
        comment: 'Très serviable et rapide !',
        context: 'help'
      }
    }),
    
    prisma.rating.create({
      data: {
        giverId: users[2].id,
        receiverId: users[0].id,
        rating: 4.8,
        comment: 'Personne respectueuse et reconnaissante',
        context: 'help'
      }
    })
  ]);

  console.log(`✅ Évaluations créées`);

  // Créer des événements système de test
  console.log('📊 Création des événements système...');
  
  await Promise.all([
    prisma.systemEvent.create({
      data: {
        type: 'user_registered',
        userId: users[0].id,
        data: { source: 'whatsapp', location: 'Dakar' }
      }
    }),
    
    prisma.systemEvent.create({
      data: {
        type: 'need_created',
        userId: users[0].id,
        data: { needType: 'MATERIAL', urgency: 2 }
      }
    }),
    
    prisma.systemEvent.create({
      data: {
        type: 'match_created',
        data: { needId: needs[0].id, offerId: offers[0].id }
      }
    })
  ]);

  console.log(`✅ Événements système créés`);

  // Afficher les statistiques finales
  const stats = await Promise.all([
    prisma.user.count(),
    prisma.need.count(),
    prisma.offer.count(),
    prisma.match.count(),
    prisma.message.count(),
    prisma.rating.count()
  ]);

  console.log('\n🎉 Base de données initialisée avec succès !');
  console.log('📊 Statistiques:');
  console.log(`   👥 Utilisateurs: ${stats[0]}`);
  console.log(`   🙋‍♂️ Besoins: ${stats[1]}`);
  console.log(`   🤝 Offres: ${stats[2]}`);
  console.log(`   🔗 Correspondances: ${stats[3]}`);
  console.log(`   💬 Messages: ${stats[4]}`);
  console.log(`   ⭐ Évaluations: ${stats[5]}`);
}

main()
  .catch((e) => {
    console.error('❌ Erreur lors de l\'initialisation:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
