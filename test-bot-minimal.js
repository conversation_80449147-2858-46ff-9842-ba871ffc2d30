#!/usr/bin/env node

/**
 * Bot minimal pour tester le système Supabase
 */

import 'dotenv/config';
import express from 'express';
import bodyParser from 'body-parser';
import { dbService } from './src/services/databaseServiceUnified.js';
import registerAPI from './src/api/registerAPI.js';

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

// Routes API
app.use('/api', registerAPI);

// Route de santé
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    database: dbService.isUsingSupabase() ? 'Supabase' : 'Memory',
    version: '1.0.0'
  });
});

// Route de test simple
app.post('/test', async (req, res) => {
  try {
    const { phone, message } = req.body;
    
    if (!phone || !message) {
      return res.status(400).json({
        success: false,
        error: 'Phone et message requis'
      });
    }
    
    // Créer/récupérer utilisateur
    const user = await dbService.getUserProfile(phone);
    
    // Créer un besoin de test
    const need = await dbService.recordUserNeed(phone, {
      type: 'GENERAL',
      description: message,
      urgency: 1
    });
    
    res.json({
      success: true,
      message: 'Test réussi',
      user: {
        id: user.id,
        phone: user.phone
      },
      need: {
        id: need.id,
        title: need.title
      }
    });
    
  } catch (error) {
    console.error('Erreur test:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Bot Nowee minimal démarré sur le port ${PORT}`);
  console.log(`📊 Base de données: ${dbService.isUsingSupabase() ? 'Supabase' : 'Mémoire'}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`🧪 Test API: http://localhost:${PORT}/test`);
  console.log(`📝 Register API: http://localhost:${PORT}/api/register`);
});
