<!DOCTYPE html>
<html lang="fr" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Nowee - Révolutionnez l'entraide locale avec l'IA, l'économie alternative et le mesh networking">
    <meta name="keywords" content="entraide locale, IA, économie alternative, mesh networking, communauté, solidarité">
    <meta name="author" content="Nowee">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://nowee.app/">
    <meta property="og:title" content="Nowee - L'Avenir de l'Entraide Locale">
    <meta property="og:description" content="Révolutionnez l'entraide locale avec l'IA, l'économie alternative et le mesh networking">
    <meta property="og:image" content="https://nowee.app/og-image.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://nowee.app/">
    <meta property="twitter:title" content="Nowee - L'Avenir de l'Entraide Locale">
    <meta property="twitter:description" content="Révolutionnez l'entraide locale avec l'IA, l'économie alternative et le mesh networking">
    <meta property="twitter:image" content="https://nowee.app/twitter-image.jpg">

    <title>Nowee Pro - L'Avenir de l'Entraide Locale</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="icon" type="image/png" href="/favicon.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cal+Sans:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js">

    <!-- Design System -->
    <link rel="stylesheet" href="design-system.css">

    <style>
        /* ===== STYLES SPÉCIFIQUES NOWEE PRO ===== */

        /* Hero Section Époustouflante */
        .hero-gradient {
            background: linear-gradient(135deg,
                var(--nowee-primary) 0%,
                var(--nowee-secondary) 50%,
                var(--nowee-mesh) 100%);
            position: relative;
            overflow: hidden;
        }

        .hero-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 10;
        }

        /* Animations Fluides */
        .float-animation {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite alternate;
        }

        @keyframes pulseGlow {
            from { box-shadow: 0 0 20px rgba(37, 99, 235, 0.3); }
            to { box-shadow: 0 0 40px rgba(37, 99, 235, 0.6); }
        }

        /* Cartes Interactives */
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all var(--transition-base) var(--ease-in-out-cubic);
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-2xl);
            background: rgba(255, 255, 255, 1);
        }

        /* Boutons Professionnels */
        .btn-primary {
            background: linear-gradient(135deg, var(--nowee-primary), var(--nowee-primary-light));
            color: white;
            padding: var(--space-4) var(--space-8);
            border-radius: var(--radius-xl);
            font-weight: var(--font-semibold);
            font-size: var(--text-lg);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base) var(--ease-in-out-cubic);
            box-shadow: var(--shadow-primary);
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left var(--transition-slow) ease;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(37, 99, 235, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: var(--space-3) var(--space-6);
            border-radius: var(--radius-lg);
            font-weight: var(--font-medium);
            cursor: pointer;
            transition: all var(--transition-base) var(--ease-in-out-cubic);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        /* Statistiques Animées */
        .stat-number {
            font-family: var(--font-display);
            font-weight: var(--font-bold);
            font-size: var(--text-4xl);
            background: linear-gradient(135deg, var(--nowee-primary), var(--nowee-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Mesh Network Visualization */
        .mesh-node {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--nowee-mesh);
            position: absolute;
            animation: meshPulse 3s ease-in-out infinite;
        }

        @keyframes meshPulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.5);
                opacity: 0.7;
            }
        }

        .mesh-connection {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, var(--nowee-mesh), transparent);
            animation: dataFlow 2s linear infinite;
        }

        @keyframes dataFlow {
            0% { opacity: 0; }
            50% { opacity: 1; }
            100% { opacity: 0; }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-gradient {
                padding: var(--space-16) 0;
            }

            .stat-number {
                font-size: var(--text-2xl);
            }

            .btn-primary {
                padding: var(--space-3) var(--space-6);
                font-size: var(--text-base);
            }
        }

        /* Scroll Animations */
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease-out;
        }

        .scroll-reveal.revealed {
            opacity: 1;
            transform: translateY(0);
        }

        /* Loading States */
        .loading-shimmer {
            background: linear-gradient(90deg,
                var(--nowee-gray-200) 25%,
                var(--nowee-gray-100) 50%,
                var(--nowee-gray-200) 75%);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Status Indicators */
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: var(--space-2);
        }

        .status-online {
            background: var(--nowee-success);
            box-shadow: 0 0 0 2px var(--nowee-success-bg);
            animation: statusPulse 2s ease-in-out infinite;
        }

        @keyframes statusPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Glassmorphism Effects */
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-2xl);
        }

        /* Gradient Borders */
        .gradient-border {
            position: relative;
            background: white;
            border-radius: var(--radius-xl);
        }

        .gradient-border::before {
            content: '';
            position: absolute;
            inset: 0;
            padding: 2px;
            background: linear-gradient(135deg, var(--nowee-primary), var(--nowee-secondary));
            border-radius: inherit;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
        }
    </style>
</head>
<body>
    <!-- Navigation Professionnelle -->
    <nav class="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-filter backdrop-blur-lg border-b border-gray-200/50">
        <div class="container-nowee">
            <div class="flex-between py-4">
                <!-- Logo Nowee -->
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </div>
                    <span class="text-display text-2xl text-gradient-primary">Nowee</span>
                </div>

                <!-- Navigation Links -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#features" class="text-gray-600 hover:text-blue-600 font-medium transition-colors">Fonctionnalités</a>
                    <a href="#technology" class="text-gray-600 hover:text-blue-600 font-medium transition-colors">Technologie</a>
                    <a href="#community" class="text-gray-600 hover:text-blue-600 font-medium transition-colors">Communauté</a>
                    <a href="#demo" class="text-gray-600 hover:text-blue-600 font-medium transition-colors">Démo</a>
                </div>

                <!-- CTA Buttons -->
                <div class="flex items-center space-x-4">
                    <button class="btn-secondary hidden md:block">
                        Découvrir
                    </button>
                    <button class="btn-primary">
                        Commencer
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section Époustouflante -->
    <section class="hero-gradient min-h-screen flex items-center justify-center text-white relative">
        <div class="hero-content container-nowee text-center">
            <!-- Badge Innovation -->
            <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-filter backdrop-blur-lg rounded-full border border-white/20 mb-8 animate-fade-in">
                <span class="status-dot status-online"></span>
                <span class="text-sm font-medium">Révolution de l'Entraide Locale</span>
            </div>

            <!-- Titre Principal -->
            <h1 class="text-display text-6xl md:text-7xl lg:text-8xl font-bold mb-6 animate-slide-up">
                L'Avenir de
                <br>
                <span class="text-gradient-mesh">l'Entraide</span>
            </h1>

            <!-- Sous-titre -->
            <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto mb-12 leading-relaxed animate-slide-up" style="animation-delay: 0.2s;">
                Connectez votre communauté avec l'intelligence artificielle,
                l'économie alternative et le mesh networking révolutionnaire.
            </p>

            <!-- Boutons d'Action -->
            <div class="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16 animate-slide-up" style="animation-delay: 0.4s;">
                <button class="btn-primary pulse-glow" onclick="startDemo()">
                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Tester l'IA Nowee
                </button>
                <button class="btn-secondary" onclick="scrollToDemo()">
                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                    </svg>
                    Voir la Démo
                </button>
            </div>

            <!-- Statistiques Impressionnantes -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto animate-fade-in" style="animation-delay: 0.6s;">
                <div class="text-center">
                    <div class="stat-number" id="stat-users">1,250+</div>
                    <div class="text-white/80 text-sm font-medium">Utilisateurs</div>
                </div>
                <div class="text-center">
                    <div class="stat-number" id="stat-helps">3,420+</div>
                    <div class="text-white/80 text-sm font-medium">Aides Réalisées</div>
                </div>
                <div class="text-center">
                    <div class="stat-number" id="stat-coins">125K+</div>
                    <div class="text-white/80 text-sm font-medium">NoweeCoins</div>
                </div>
                <div class="text-center">
                    <div class="stat-number" id="stat-communities">45+</div>
                    <div class="text-white/80 text-sm font-medium">Communautés</div>
                </div>
            </div>
        </div>

        <!-- Mesh Network Animation -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="mesh-node" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
            <div class="mesh-node" style="top: 30%; left: 80%; animation-delay: 1s;"></div>
            <div class="mesh-node" style="top: 70%; left: 20%; animation-delay: 2s;"></div>
            <div class="mesh-node" style="top: 60%; left: 90%; animation-delay: 0.5s;"></div>
            <div class="mesh-node" style="top: 40%; left: 50%; animation-delay: 1.5s;"></div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <svg class="w-6 h-6 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
            </svg>
        </div>
    </section>

    <!-- Section Fonctionnalités Révolutionnaires -->
    <section id="features" class="py-24 bg-gradient-to-b from-gray-50 to-white">
        <div class="container-nowee">
            <!-- En-tête Section -->
            <div class="text-center mb-20 scroll-reveal">
                <div class="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                    Fonctionnalités Révolutionnaires
                </div>
                <h2 class="text-display text-5xl md:text-6xl font-bold text-gray-900 mb-6">
                    Technologie
                    <span class="text-gradient-primary">d'Avant-Garde</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Découvrez comment Nowee révolutionne l'entraide locale avec des technologies
                    de pointe qui transforment la façon dont les communautés se connectent et s'entraident.
                </p>
            </div>

            <!-- Grille de Fonctionnalités -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- IA Contextuelle -->
                <div class="feature-card p-8 rounded-2xl scroll-reveal">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 float-animation">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">IA Contextuelle</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Intelligence artificielle adaptée à la culture sénégalaise qui comprend
                        les nuances locales et propose des solutions d'entraide personnalisées.
                    </p>
                    <div class="flex items-center text-blue-600 font-medium">
                        <span>En savoir plus</span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                </div>

                <!-- Économie Alternative -->
                <div class="feature-card p-8 rounded-2xl scroll-reveal">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mb-6 float-animation" style="animation-delay: 0.5s;">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">NoweeCoins</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Économie alternative basée sur les NoweeCoins et crédits temps qui
                        valorise l'entraide et crée un système d'échange équitable.
                    </p>
                    <div class="flex items-center text-orange-600 font-medium">
                        <span>Découvrir l'économie</span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                </div>

                <!-- Mesh Networking -->
                <div class="feature-card p-8 rounded-2xl scroll-reveal">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 float-animation" style="animation-delay: 1s;">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Mesh Networking</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Réseau maillé révolutionnaire qui maintient la communication
                        même sans internet, garantissant la résilience communautaire.
                    </p>
                    <div class="flex items-center text-purple-600 font-medium">
                        <span>Explorer le mesh</span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                </div>

                <!-- Géolocalisation -->
                <div class="feature-card p-8 rounded-2xl scroll-reveal">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-500 rounded-2xl flex items-center justify-center mb-6 float-animation" style="animation-delay: 1.5s;">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Aide de Proximité</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Géolocalisation précise pour connecter les personnes dans le besoin
                        avec les aidants les plus proches de leur localisation.
                    </p>
                    <div class="flex items-center text-green-600 font-medium">
                        <span>Voir la carte</span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                </div>

                <!-- WhatsApp Integration -->
                <div class="feature-card p-8 rounded-2xl scroll-reveal">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-600 to-green-700 rounded-2xl flex items-center justify-center mb-6 float-animation" style="animation-delay: 2s;">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">WhatsApp Natif</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Intégration complète avec WhatsApp pour une accessibilité maximale,
                        utilisant l'application la plus populaire au Sénégal.
                    </p>
                    <div class="flex items-center text-green-600 font-medium">
                        <span>Tester le bot</span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                </div>

                <!-- Analytics -->
                <div class="feature-card p-8 rounded-2xl scroll-reveal">
                    <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 float-animation" style="animation-delay: 2.5s;">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Analytics Avancés</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Tableaux de bord intelligents pour suivre l'impact communautaire,
                        optimiser l'entraide et mesurer la transformation sociale.
                    </p>
                    <div class="flex items-center text-indigo-600 font-medium">
                        <span>Voir les métriques</span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section Démonstration Interactive -->
    <section id="demo" class="py-24 bg-gray-900 text-white relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                        <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5"/>
                    </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#grid)"/>
            </svg>
        </div>

        <div class="container-nowee relative z-10">
            <!-- En-tête Section -->
            <div class="text-center mb-16 scroll-reveal">
                <div class="inline-flex items-center px-4 py-2 bg-blue-600/20 text-blue-300 rounded-full text-sm font-medium mb-6">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                    </svg>
                    Démonstration Interactive
                </div>
                <h2 class="text-display text-5xl md:text-6xl font-bold mb-6">
                    Découvrez la
                    <span class="text-gradient-mesh">Magie</span>
                    en Action
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                    Testez toutes les fonctionnalités de Nowee dans un environnement interactif
                    et découvrez comment l'IA, l'économie alternative et le mesh networking transforment l'entraide.
                </p>
            </div>

            <!-- Interface de Démonstration -->
            <div class="max-w-6xl mx-auto">
                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <!-- Panel de Contrôle -->
                    <div class="space-y-6 scroll-reveal">
                        <div class="glass-card p-8">
                            <h3 class="text-2xl font-bold mb-6 flex items-center">
                                <svg class="w-6 h-6 mr-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                </svg>
                                Chat IA Contextuel
                            </h3>
                            <div class="space-y-4">
                                <div class="bg-gray-800/50 rounded-lg p-4">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-gray-300 text-sm">
                                                "Salut Nowee ! J'ai besoin d'aide pour déménager ce weekend à Dakar"
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-blue-600/20 rounded-lg p-4">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-blue-200 text-sm">
                                                "Hello ! Bien sûr, je suis là pour t'aider. Le déménagement peut être difficile,
                                                mais ensemble on va s'en sortir ! Je peux te connecter avec des personnes
                                                disponibles près de chez toi à Dakar..."
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn-primary w-full" onclick="testAI()">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                    </svg>
                                    Tester l'IA Nowee
                                </button>
                            </div>
                        </div>

                        <div class="glass-card p-8">
                            <h3 class="text-2xl font-bold mb-6 flex items-center">
                                <svg class="w-6 h-6 mr-3 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                                </svg>
                                Économie NoweeCoins
                            </h3>
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="bg-orange-600/20 rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-orange-400" id="demo-coins">150</div>
                                    <div class="text-sm text-orange-200">NoweeCoins</div>
                                </div>
                                <div class="bg-green-600/20 rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-green-400" id="demo-credits">2.5h</div>
                                    <div class="text-sm text-green-200">Crédits Temps</div>
                                </div>
                            </div>
                            <button class="btn-secondary w-full" onclick="testEconomy()">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                </svg>
                                Explorer l'Économie
                            </button>
                        </div>
                    </div>

                    <!-- Visualisation Mesh -->
                    <div class="scroll-reveal">
                        <div class="glass-card p-8 h-96 relative overflow-hidden">
                            <h3 class="text-2xl font-bold mb-6 flex items-center">
                                <svg class="w-6 h-6 mr-3 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"/>
                                </svg>
                                Réseau Mesh en Temps Réel
                            </h3>

                            <!-- Mesh Network Visualization -->
                            <div class="absolute inset-8 top-20">
                                <svg class="w-full h-full" viewBox="0 0 300 200">
                                    <!-- Nodes -->
                                    <circle cx="50" cy="50" r="8" fill="#8b5cf6" class="animate-pulse">
                                        <animate attributeName="r" values="8;12;8" dur="2s" repeatCount="indefinite"/>
                                    </circle>
                                    <circle cx="150" cy="30" r="8" fill="#3b82f6" class="animate-pulse">
                                        <animate attributeName="r" values="8;12;8" dur="2s" begin="0.5s" repeatCount="indefinite"/>
                                    </circle>
                                    <circle cx="250" cy="70" r="8" fill="#10b981" class="animate-pulse">
                                        <animate attributeName="r" values="8;12;8" dur="2s" begin="1s" repeatCount="indefinite"/>
                                    </circle>
                                    <circle cx="80" cy="120" r="8" fill="#f59e0b" class="animate-pulse">
                                        <animate attributeName="r" values="8;12;8" dur="2s" begin="1.5s" repeatCount="indefinite"/>
                                    </circle>
                                    <circle cx="200" cy="150" r="8" fill="#ef4444" class="animate-pulse">
                                        <animate attributeName="r" values="8;12;8" dur="2s" begin="2s" repeatCount="indefinite"/>
                                    </circle>

                                    <!-- Connections -->
                                    <line x1="50" y1="50" x2="150" y2="30" stroke="url(#gradient1)" stroke-width="2" opacity="0.7">
                                        <animate attributeName="opacity" values="0.3;1;0.3" dur="3s" repeatCount="indefinite"/>
                                    </line>
                                    <line x1="150" y1="30" x2="250" y2="70" stroke="url(#gradient2)" stroke-width="2" opacity="0.7">
                                        <animate attributeName="opacity" values="0.3;1;0.3" dur="3s" begin="0.5s" repeatCount="indefinite"/>
                                    </line>
                                    <line x1="50" y1="50" x2="80" y2="120" stroke="url(#gradient3)" stroke-width="2" opacity="0.7">
                                        <animate attributeName="opacity" values="0.3;1;0.3" dur="3s" begin="1s" repeatCount="indefinite"/>
                                    </line>
                                    <line x1="80" y1="120" x2="200" y2="150" stroke="url(#gradient4)" stroke-width="2" opacity="0.7">
                                        <animate attributeName="opacity" values="0.3;1;0.3" dur="3s" begin="1.5s" repeatCount="indefinite"/>
                                    </line>
                                    <line x1="250" y1="70" x2="200" y2="150" stroke="url(#gradient5)" stroke-width="2" opacity="0.7">
                                        <animate attributeName="opacity" values="0.3;1;0.3" dur="3s" begin="2s" repeatCount="indefinite"/>
                                    </line>

                                    <!-- Gradients -->
                                    <defs>
                                        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
                                        </linearGradient>
                                        <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
                                        </linearGradient>
                                        <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
                                        </linearGradient>
                                        <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
                                        </linearGradient>
                                        <linearGradient id="gradient5" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
                                        </linearGradient>
                                    </defs>
                                </svg>
                            </div>

                            <div class="absolute bottom-8 left-8 right-8">
                                <button class="btn-primary w-full" onclick="testMesh()">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                    </svg>
                                    Tester le Mesh
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer Professionnel -->
    <footer class="bg-gray-900 text-white py-16">
        <div class="container-nowee">
            <div class="grid md:grid-cols-4 gap-8 mb-12">
                <!-- Logo et Description -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        <span class="text-display text-2xl text-gradient-primary">Nowee</span>
                    </div>
                    <p class="text-gray-300 leading-relaxed max-w-md">
                        Révolutionnons l'entraide locale avec l'intelligence artificielle,
                        l'économie alternative et le mesh networking. Ensemble, créons un monde plus solidaire.
                    </p>
                    <div class="flex space-x-4 mt-6">
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Liens Rapides -->
                <div>
                    <h4 class="font-semibold text-lg mb-4">Produit</h4>
                    <ul class="space-y-2">
                        <li><a href="#features" class="text-gray-400 hover:text-white transition-colors">Fonctionnalités</a></li>
                        <li><a href="#technology" class="text-gray-400 hover:text-white transition-colors">Technologie</a></li>
                        <li><a href="#demo" class="text-gray-400 hover:text-white transition-colors">Démonstration</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Documentation</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h4 class="font-semibold text-lg mb-4">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Centre d'aide</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Communauté</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Statut</a></li>
                    </ul>
                </div>
            </div>

            <!-- Copyright -->
            <div class="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm">
                    © 2025 Nowee. Tous droits réservés. Révolutionnons l'entraide ensemble.
                </p>
                <div class="flex space-x-6 mt-4 md:mt-0">
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Confidentialité</a>
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Conditions</a>
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Cookies</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Professionnel -->
    <script>
        // ===== CONFIGURATION API =====
        const API_BASE = 'http://localhost:3000';

        // ===== ANIMATIONS ET INTERACTIONS =====

        // Scroll Reveal Animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                }
            });
        }, observerOptions);

        // Observer tous les éléments scroll-reveal
        document.addEventListener('DOMContentLoaded', () => {
            const scrollElements = document.querySelectorAll('.scroll-reveal');
            scrollElements.forEach(el => observer.observe(el));

            // Animation des statistiques
            animateStats();

            // Smooth scroll pour les liens de navigation
            setupSmoothScroll();
        });

        // Animation des statistiques avec compteur
        function animateStats() {
            const stats = [
                { id: 'stat-users', target: 1250, suffix: '+' },
                { id: 'stat-helps', target: 3420, suffix: '+' },
                { id: 'stat-coins', target: 125, suffix: 'K+' },
                { id: 'stat-communities', target: 45, suffix: '+' }
            ];

            stats.forEach(stat => {
                const element = document.getElementById(stat.id);
                if (element) {
                    animateCounter(element, 0, stat.target, stat.suffix, 2000);
                }
            });
        }

        function animateCounter(element, start, end, suffix, duration) {
            const startTime = performance.now();

            function updateCounter(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Easing function pour une animation fluide
                const easeOutCubic = 1 - Math.pow(1 - progress, 3);
                const current = Math.floor(start + (end - start) * easeOutCubic);

                element.textContent = current.toLocaleString() + suffix;

                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                }
            }

            requestAnimationFrame(updateCounter);
        }

        // Smooth scroll pour la navigation
        function setupSmoothScroll() {
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const targetId = link.getAttribute('href');
                    const targetElement = document.querySelector(targetId);

                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // ===== FONCTIONS DE DÉMONSTRATION =====

        // Test de l'IA
        async function testAI() {
            showNotification('🤖 Test de l\'IA en cours...', 'info');

            try {
                const response = await fetch(`${API_BASE}/api/chat/ai`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: "Salut Nowee ! J'ai besoin d'aide pour déménager ce weekend à Dakar",
                        phone: '+221701234567',
                        context: 'demo_pro'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showNotification('✅ IA Nowee : ' + data.response.substring(0, 100) + '...', 'success');
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                showNotification('❌ Erreur : ' + error.message, 'error');
            }
        }

        // Test de l'économie
        async function testEconomy() {
            showNotification('💰 Test de l\'économie en cours...', 'info');

            try {
                const response = await fetch(`${API_BASE}/api/economy/wallet/+221701234567`);
                const data = await response.json();

                if (data.success) {
                    // Animation des valeurs
                    const coinsElement = document.getElementById('demo-coins');
                    const creditsElement = document.getElementById('demo-credits');

                    if (coinsElement) {
                        animateCounter(coinsElement, 0, data.wallet.noweeCoins, '', 1000);
                    }

                    if (creditsElement) {
                        setTimeout(() => {
                            creditsElement.textContent = data.wallet.timeCredits + 'h';
                        }, 500);
                    }

                    showNotification(`✅ Portefeuille : ${data.wallet.noweeCoins} NoweeCoins, ${data.wallet.timeCredits}h crédits`, 'success');
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                showNotification('❌ Erreur : ' + error.message, 'error');
            }
        }

        // Test du mesh networking
        function testMesh() {
            showNotification('🕸️ Activation du réseau mesh...', 'info');

            // Animation des nœuds mesh
            const meshNodes = document.querySelectorAll('.mesh-node');
            meshNodes.forEach((node, index) => {
                setTimeout(() => {
                    node.style.transform = 'scale(1.5)';
                    node.style.opacity = '1';
                    setTimeout(() => {
                        node.style.transform = 'scale(1)';
                    }, 300);
                }, index * 200);
            });

            setTimeout(() => {
                showNotification('✅ Réseau mesh activé ! 5 nœuds connectés', 'success');
            }, 2000);
        }

        // Démarrer la démo complète
        function startDemo() {
            window.open('/test-interface.html', '_blank');
        }

        // Scroll vers la démo
        function scrollToDemo() {
            document.getElementById('demo').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        // ===== SYSTÈME DE NOTIFICATIONS =====

        function showNotification(message, type = 'info') {
            // Créer l'élément notification
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform translate-x-full transition-transform duration-300 ${getNotificationClasses(type)}`;
            notification.textContent = message;

            // Ajouter au DOM
            document.body.appendChild(notification);

            // Animation d'entrée
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Auto-suppression
            setTimeout(() => {
                notification.style.transform = 'translateX(full)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        function getNotificationClasses(type) {
            const classes = {
                info: 'bg-blue-600 text-white',
                success: 'bg-green-600 text-white',
                error: 'bg-red-600 text-white',
                warning: 'bg-orange-600 text-white'
            };
            return classes[type] || classes.info;
        }

        // ===== PERFORMANCE ET OPTIMISATIONS =====

        // Lazy loading pour les images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('loading');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }

        // Préchargement des ressources critiques
        function preloadCriticalResources() {
            const criticalUrls = [
                `${API_BASE}/health`,
                `${API_BASE}/api/stats/advanced`
            ];

            criticalUrls.forEach(url => {
                fetch(url).catch(() => {}); // Préchargement silencieux
            });
        }

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', () => {
            preloadCriticalResources();
        });

        // ===== ANALYTICS ET TRACKING =====

        // Tracking des interactions utilisateur
        function trackEvent(category, action, label) {
            // Intégration avec Google Analytics, Mixpanel, etc.
            console.log('Event tracked:', { category, action, label });

            // Exemple d'intégration GA4
            if (typeof gtag !== 'undefined') {
                gtag('event', action, {
                    event_category: category,
                    event_label: label
                });
            }
        }

        // Tracking automatique des clics sur les boutons
        document.addEventListener('click', (e) => {
            if (e.target.matches('button, .btn-primary, .btn-secondary')) {
                const buttonText = e.target.textContent.trim();
                trackEvent('Button', 'Click', buttonText);
            }
        });
    </script>
</body>
</html>