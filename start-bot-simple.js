#!/usr/bin/env node

/**
 * Démarrage simple du bot Nowee
 */

import 'dotenv/config';
import express from 'express';
import bodyParser from 'body-parser';
import twilio from 'twilio';
import { dbService } from './src/services/databaseServiceUnified.js';

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

// Route de santé
app.get('/health', async (req, res) => {
  try {
    const stats = await dbService.getGlobalStats();
    
    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      database: dbService.isUsingSupabase() ? 'Supabase' : 'Memory',
      stats: stats,
      version: '1.0.0'
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      error: error.message
    });
  }
});

// Route webhook WhatsApp simplifiée
app.post('/webhook', async (req, res) => {
  try {
    const { Body: incomingMsg, From: userPhone } = req.body;
    
    console.log(`📱 Message reçu de ${userPhone}: ${incomingMsg}`);
    
    const twiml = new twilio.twiml.MessagingResponse();
    
    // Commandes simples
    if (incomingMsg === '/aide') {
      twiml.message(`📚 *Commandes Nowee*

🔧 *Entraide:*
/aide - Cette aide
/profil - Voir votre profil
/stats - Statistiques

💰 *Économie:* (Bientôt disponible)
/portefeuille - Votre portefeuille
/troc - Vos trocs

💬 Décrivez votre besoin pour commencer !`);
    } else if (incomingMsg === '/profil') {
      const user = await dbService.getUserProfile(userPhone);
      twiml.message(`👤 *Votre Profil*

📞 Téléphone: ${user.phone}
👤 Nom: ${user.name || 'Non défini'}
🏙️ Ville: ${user.city || 'Non définie'}
⭐ Note: ${user.rating || 0}/5
🤝 Aides données: ${user.help_given || 0}
🙏 Aides reçues: ${user.help_received || 0}`);
    } else if (incomingMsg === '/stats') {
      const stats = await dbService.getGlobalStats();
      twiml.message(`📊 *Statistiques Nowee*

👥 Utilisateurs: ${stats.totalUsers}
📋 Ressources: ${stats.totalResources || stats.totalNeeds + stats.totalOffers || 'N/A'}
🎯 Correspondances: ${stats.totalMatches}

🗄️ Mode: ${dbService.isUsingSupabase() ? 'Supabase' : 'Mémoire'}`);
    } else {
      // Traitement normal du message
      const user = await dbService.getUserProfile(userPhone);
      
      // Créer un besoin
      const need = await dbService.recordUserNeed(userPhone, {
        type: 'GENERAL',
        description: incomingMsg,
        urgency: 1
      });
      
      // Chercher des correspondances
      const matches = await dbService.findMatches(need.id, 10);
      
      if (matches.length > 0) {
        let response = `🎯 *${matches.length} aide(s) trouvée(s) !*\n\n`;
        
        matches.slice(0, 3).forEach((match, index) => {
          response += `${index + 1}. ${match.title}\n`;
          if (match.distance_km) {
            response += `   📍 ${match.distance_km.toFixed(1)} km\n`;
          }
          response += `   ⭐ Score: ${match.match_score || 'N/A'}%\n\n`;
        });
        
        response += `💡 Quelqu'un peut vous aider ! Nous vous mettrons en contact bientôt.`;
        twiml.message(response);
      } else {
        twiml.message(`📝 *Demande enregistrée !*

"${incomingMsg}"

🔍 Nous cherchons quelqu'un pour vous aider...
📱 Vous serez notifié dès qu'une aide sera trouvée !

💡 En attendant, vous pouvez aussi proposer votre aide avec /offrir`);
      }
    }
    
    res.type('text/xml').send(twiml.toString());
    
  } catch (error) {
    console.error('Erreur webhook:', error);
    const twiml = new twilio.twiml.MessagingResponse();
    twiml.message('❌ Une erreur est survenue. Réessayez plus tard.');
    res.type('text/xml').send(twiml.toString());
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Bot Nowee démarré sur le port ${PORT}`);
  console.log(`📊 Base de données: ${dbService.isUsingSupabase() ? 'Supabase' : 'Mémoire'}`);
  console.log(`🔗 Health: http://localhost:${PORT}/health`);
  console.log(`📱 Webhook: http://localhost:${PORT}/webhook`);
  console.log(`\n💡 Testez avec curl ou ngrok pour WhatsApp !`);
});
