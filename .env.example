# ===========================================
# CONFIGURATION NOWEE - CLÉS API
# ===========================================
# ⚠️  IMPORTANT: Ne jamais commiter ce fichier avec de vraies clés !
# Copiez ce fichier vers .env et remplacez les valeurs

# 🤖 OpenAI API (pour l'intelligence artificielle)
# Obtenez votre clé sur: https://platform.openai.com/api-keys
# Format: sk-proj-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
OPENAI_API_KEY=sk-your-openai-key-here

# 📱 Twilio API (pour WhatsApp)
# Obtenez vos clés sur: https://console.twilio.com/
# Account SID format: ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_ACCOUNT_SID=AC-your-twilio-account-sid-here

# Auth Token format: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your-twilio-auth-token-here

# Numéro WhatsApp Twilio (Sandbox ou numéro dédié)
# Sandbox: whatsapp:+***********
# Numéro dédié: whatsapp:+**********
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# 🌐 Configuration serveur
PORT=3000
NODE_ENV=development

# 📊 Webhook URL (sera générée par ngrok en développement)
# Format: https://xxxxxxxx.ngrok.io/webhook
WEBHOOK_URL=

# 🔐 Sécurité (optionnel pour le MVP)
# JWT_SECRET=your-jwt-secret-for-future-auth
# ENCRYPTION_KEY=your-encryption-key-for-sensitive-data

# 🗄️ Base de données (pour évolution future)
# DATABASE_URL=postgres://user:password@localhost:5432/nowee
# REDIS_URL=redis://localhost:6379
