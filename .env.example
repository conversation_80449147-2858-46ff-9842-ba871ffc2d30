# ===========================================
# CONFIGURATION NOWEE - CLÉS API
# ===========================================
# ⚠️  IMPORTANT: Ne jamais commiter ce fichier avec de vraies clés !
# Copiez ce fichier vers .env et remplacez les valeurs

# 🤖 OpenAI API (pour l'intelligence artificielle)
# Obtenez votre clé sur: https://platform.openai.com/api-keys
# Format: sk-proj-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
OPENAI_API_KEY=********************************************************************************************************************************************************************

# 📱 Twilio API (pour WhatsApp)
# Obtenez vos clés sur: https://console.twilio.com/
# Account SID format: ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_ACCOUNT_SID=**********************************

# Auth Token format: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=bddbdb48271cdeef1b324933d6268951

# Numéro WhatsApp Twilio (Sandbox ou numéro dédié)
# Sandbox: whatsapp:+***********
# Numéro dédié: whatsapp:+**********
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# 🌐 Configuration serveur
PORT=3000
NODE_ENV=development

# 📊 Webhook URL (sera générée par ngrok en développement)
# Format: https://xxxxxxxx.ngrok.io/webhook
WEBHOOK_URL=https://c576e59be3fa.ngrok-free.app/webhook

# 🔐 Sécurité (optionnel pour le MVP)
# JWT_SECRET=your-jwt-secret-for-future-auth
# ENCRYPTION_KEY=your-encryption-key-for-sensitive-data

# 🗄️ Base de données PostgreSQL
# Pour développement local avec Docker:
# docker run --name nowee-postgres -e POSTGRES_PASSWORD=nowee123 -e POSTGRES_DB=nowee -p 5432:5432 -d postgres:15
# Pour production, utilisez un service cloud comme Supabase, Railway, ou Heroku Postgres
DATABASE_URL=postgresql://postgres:nowee123@localhost:5432/nowee

# 🔴 Cache Redis (optionnel pour l'instant)
# REDIS_URL=redis://localhost:6379

# 🔐 Clé API Admin (pour les routes d'administration)
ADMIN_API_KEY=your-admin-api-key-here
