-- Schéma Supabase simplifié pour Nowee - Ressources Locales et Matching
-- Compatible avec Supabase standard (sans PostGIS)

-- Table des utilisateurs
CREATE TABLE users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  phone VARCHAR(20) UNIQUE NOT NULL,
  name VARCHAR(100),
  email VARCHAR(255),
  avatar_url TEXT,
  
  -- Localisation (JSON simple)
  location JSONB,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  city VARCHAR(100),
  country VARCHAR(100) DEFAULT 'Sénégal',
  
  -- Profil et réputation
  rating DECIMAL(3,2) DEFAULT 0.0,
  help_given INTEGER DEFAULT 0,
  help_received INTEGER DEFAULT 0,
  trust_score INTEGER DEFAULT 100,
  
  -- Préférences
  preferences JSONB DEFAULT '{}',
  languages TEXT[] DEFAULT ARRAY['fr'],
  
  -- M<PERSON>tadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true
);

-- Table des ressources (besoins et offres unifiés)
CREATE TABLE resources (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Type de ressource
  type VARCHAR(20) NOT NULL CHECK (type IN ('NEED', 'OFFER')),
  category VARCHAR(50) NOT NULL, -- MATERIAL, SERVICE, TRANSPORT, EMERGENCY, etc.
  
  -- Contenu
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  tags TEXT[] DEFAULT ARRAY[]::TEXT[],
  
  -- Localisation
  location JSONB,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  radius_km INTEGER DEFAULT 5, -- Rayon de recherche
  
  -- Conditions
  urgency INTEGER DEFAULT 1 CHECK (urgency BETWEEN 1 AND 5),
  price_range JSONB, -- {min: 0, max: 1000, currency: 'XOF', negotiable: true}
  availability JSONB, -- {flexible: true, hours: '9h-17h', days: ['lun', 'mar']}
  conditions TEXT,
  
  -- Statut
  status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'MATCHED', 'COMPLETED', 'EXPIRED', 'CANCELLED')),
  expires_at TIMESTAMP WITH TIME ZONE,
  
  -- Recherche textuelle
  search_vector tsvector,
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des correspondances (matches)
CREATE TABLE matches (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  need_id UUID REFERENCES resources(id) ON DELETE CASCADE,
  offer_id UUID REFERENCES resources(id) ON DELETE CASCADE,
  
  -- Participants
  requester_id UUID REFERENCES users(id) ON DELETE CASCADE,
  provider_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Score de matching
  match_score DECIMAL(5,2) NOT NULL,
  match_factors JSONB, -- Détails du calcul de score
  
  -- Statut
  status VARCHAR(20) DEFAULT 'PROPOSED' CHECK (status IN ('PROPOSED', 'ACCEPTED', 'REJECTED', 'COMPLETED', 'CANCELLED')),
  
  -- Évaluation
  requester_rating INTEGER CHECK (requester_rating BETWEEN 1 AND 5),
  provider_rating INTEGER CHECK (provider_rating BETWEEN 1 AND 5),
  requester_feedback TEXT,
  provider_feedback TEXT,
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  
  -- Contraintes
  CONSTRAINT different_resources CHECK (need_id != offer_id),
  CONSTRAINT different_users CHECK (requester_id != provider_id),
  UNIQUE(need_id, offer_id)
);

-- Table des conversations
CREATE TABLE conversations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  match_id UUID REFERENCES matches(id) ON DELETE CASCADE,
  
  -- Participants
  user1_id UUID REFERENCES users(id) ON DELETE CASCADE,
  user2_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Statut
  status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'ARCHIVED', 'BLOCKED')),
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des messages
CREATE TABLE messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
  sender_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Contenu
  content TEXT NOT NULL,
  message_type VARCHAR(20) DEFAULT 'TEXT' CHECK (message_type IN ('TEXT', 'VOICE', 'IMAGE', 'LOCATION', 'SYSTEM')),
  metadata JSONB DEFAULT '{}',
  
  -- Statut
  is_read BOOLEAN DEFAULT false,
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des événements système
CREATE TABLE system_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  
  -- Type d'événement
  event_type VARCHAR(50) NOT NULL,
  event_data JSONB DEFAULT '{}',
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ip_address INET,
  user_agent TEXT
);

-- Index pour performance
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_location ON users(latitude, longitude);
CREATE INDEX idx_users_active ON users(is_active, last_active_at);

CREATE INDEX idx_resources_type_status ON resources(type, status);
CREATE INDEX idx_resources_category ON resources(category);
CREATE INDEX idx_resources_location ON resources(latitude, longitude);
CREATE INDEX idx_resources_search ON resources USING GIN(search_vector);
CREATE INDEX idx_resources_user_id ON resources(user_id);
CREATE INDEX idx_resources_created_at ON resources(created_at DESC);

CREATE INDEX idx_matches_need_offer ON matches(need_id, offer_id);
CREATE INDEX idx_matches_users ON matches(requester_id, provider_id);
CREATE INDEX idx_matches_status ON matches(status);
CREATE INDEX idx_matches_score ON matches(match_score DESC);

CREATE INDEX idx_conversations_match ON conversations(match_id);
CREATE INDEX idx_conversations_users ON conversations(user1_id, user2_id);

CREATE INDEX idx_messages_conversation ON messages(conversation_id, created_at DESC);
CREATE INDEX idx_messages_sender ON messages(sender_id);

CREATE INDEX idx_events_type ON system_events(event_type);
CREATE INDEX idx_events_user ON system_events(user_id, created_at DESC);

-- Fonctions utilitaires

-- Fonction de calcul de distance (formule de Haversine)
CREATE OR REPLACE FUNCTION calculate_distance_km(lat1 DECIMAL, lon1 DECIMAL, lat2 DECIMAL, lon2 DECIMAL)
RETURNS DECIMAL AS $$
DECLARE
  R DECIMAL := 6371; -- Rayon de la Terre en km
  dLat DECIMAL;
  dLon DECIMAL;
  a DECIMAL;
  c DECIMAL;
BEGIN
  dLat := radians(lat2 - lat1);
  dLon := radians(lon2 - lon1);
  
  a := sin(dLat/2) * sin(dLat/2) + cos(radians(lat1)) * cos(radians(lat2)) * sin(dLon/2) * sin(dLon/2);
  c := 2 * atan2(sqrt(a), sqrt(1-a));
  
  RETURN R * c;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Fonction de mise à jour automatique du timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fonction de mise à jour du search_vector
CREATE OR REPLACE FUNCTION update_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector = to_tsvector('french', 
    COALESCE(NEW.title, '') || ' ' || 
    COALESCE(NEW.description, '') || ' ' || 
    COALESCE(array_to_string(NEW.tags, ' '), '')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers pour mise à jour automatique
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_resources_updated_at BEFORE UPDATE ON resources
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_matches_updated_at BEFORE UPDATE ON matches
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON conversations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger pour mise à jour du search_vector
CREATE TRIGGER update_resources_search_vector BEFORE INSERT OR UPDATE ON resources
  FOR EACH ROW EXECUTE FUNCTION update_search_vector();

-- Politiques de sécurité RLS (Row Level Security)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE resources ENABLE ROW LEVEL SECURITY;
ALTER TABLE matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Politiques pour les utilisateurs (peuvent voir/modifier leurs propres données)
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid()::text = id::text);

-- Politiques pour les ressources (publiques en lecture, propriétaire en écriture)
CREATE POLICY "Resources are viewable by everyone" ON resources
  FOR SELECT USING (true);

CREATE POLICY "Users can insert own resources" ON resources
  FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own resources" ON resources
  FOR UPDATE USING (auth.uid()::text = user_id::text);

-- Vues utiles

-- Vue des ressources avec informations utilisateur
CREATE VIEW resources_with_user AS
SELECT 
  r.*,
  u.name as user_name,
  u.rating as user_rating,
  u.help_given as user_help_given,
  CASE 
    WHEN r.latitude IS NOT NULL AND r.longitude IS NOT NULL 
    THEN calculate_distance_km(r.latitude, r.longitude, r.latitude, r.longitude)
    ELSE NULL 
  END as distance_km
FROM resources r
JOIN users u ON r.user_id = u.id
WHERE r.status = 'ACTIVE';

-- Vue des matches avec détails
CREATE VIEW matches_detailed AS
SELECT 
  m.*,
  rn.title as need_title,
  ro.title as offer_title,
  ur.name as requester_name,
  up.name as provider_name,
  CASE 
    WHEN rn.latitude IS NOT NULL AND rn.longitude IS NOT NULL 
         AND ro.latitude IS NOT NULL AND ro.longitude IS NOT NULL
    THEN calculate_distance_km(rn.latitude, rn.longitude, ro.latitude, ro.longitude)
    ELSE NULL 
  END as distance_km
FROM matches m
JOIN resources rn ON m.need_id = rn.id
JOIN resources ro ON m.offer_id = ro.id
JOIN users ur ON m.requester_id = ur.id
JOIN users up ON m.provider_id = up.id;
