/**
 * Service vocal pour l'application mobile Nowee
 * Gère la reconnaissance vocale et la synthèse vocale
 */

import Voice from '@react-native-voice/voice';
import Tts from 'react-native-tts';
import { Platform, PermissionsAndroid } from 'react-native';
import { ApiService } from './ApiService';

// Types
interface VoiceRecognitionResult {
  success: boolean;
  text?: string;
  error?: string;
  confidence?: number;
}

interface VoiceSynthesisOptions {
  language?: string;
  pitch?: number;
  rate?: number;
  voice?: string;
}

/**
 * Service vocal principal pour mobile
 */
export class VoiceService {
  private static isListening = false;
  private static isInitialized = false;

  /**
   * Initialise le service vocal
   */
  static async initialize(): Promise<boolean> {
    try {
      // Initialiser la reconnaissance vocale
      Voice.onSpeechStart = this.onSpeechStart;
      Voice.onSpeechEnd = this.onSpeechEnd;
      Voice.onSpeechResults = this.onSpeechResults;
      Voice.onSpeechError = this.onSpeechError;

      // Initialiser la synthèse vocale
      await this.initializeTts();

      // Demander les permissions sur Android
      if (Platform.OS === 'android') {
        await this.requestAndroidPermissions();
      }

      this.isInitialized = true;
      console.log('✅ Service vocal initialisé');
      return true;

    } catch (error) {
      console.error('❌ Erreur initialisation service vocal:', error);
      return false;
    }
  }

  /**
   * Initialise la synthèse vocale
   */
  private static async initializeTts(): Promise<void> {
    try {
      // Configuration TTS
      await Tts.setDefaultLanguage('fr-FR');
      await Tts.setDefaultRate(0.5); // Vitesse normale
      await Tts.setDefaultPitch(1.0); // Ton normal

      // Événements TTS
      Tts.addEventListener('tts-start', () => console.log('🔊 TTS démarré'));
      Tts.addEventListener('tts-finish', () => console.log('🔊 TTS terminé'));
      Tts.addEventListener('tts-cancel', () => console.log('🔊 TTS annulé'));

    } catch (error) {
      console.error('Erreur initialisation TTS:', error);
    }
  }

  /**
   * Demande les permissions Android
   */
  private static async requestAndroidPermissions(): Promise<boolean> {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        {
          title: 'Permission Microphone',
          message: 'Nowee a besoin d\'accéder au microphone pour la reconnaissance vocale.',
          buttonNeutral: 'Plus tard',
          buttonNegative: 'Refuser',
          buttonPositive: 'Accepter',
        }
      );

      return granted === PermissionsAndroid.RESULTS.GRANTED;

    } catch (error) {
      console.error('Erreur permissions Android:', error);
      return false;
    }
  }

  /**
   * Démarre la reconnaissance vocale
   */
  static async startListening(language = 'fr-FR'): Promise<void> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      if (this.isListening) {
        console.log('⚠️ Reconnaissance vocale déjà en cours');
        return;
      }

      await Voice.start(language);
      this.isListening = true;
      console.log('🎤 Reconnaissance vocale démarrée');

    } catch (error) {
      console.error('Erreur démarrage reconnaissance vocale:', error);
      this.isListening = false;
      throw error;
    }
  }

  /**
   * Arrête la reconnaissance vocale
   */
  static async stopListening(): Promise<void> {
    try {
      await Voice.stop();
      this.isListening = false;
      console.log('🎤 Reconnaissance vocale arrêtée');

    } catch (error) {
      console.error('Erreur arrêt reconnaissance vocale:', error);
      this.isListening = false;
    }
  }

  /**
   * Annule la reconnaissance vocale
   */
  static async cancelListening(): Promise<void> {
    try {
      await Voice.cancel();
      this.isListening = false;
      console.log('🎤 Reconnaissance vocale annulée');

    } catch (error) {
      console.error('Erreur annulation reconnaissance vocale:', error);
      this.isListening = false;
    }
  }

  /**
   * Synthèse vocale d'un texte
   */
  static async speak(text: string, options: VoiceSynthesisOptions = {}): Promise<void> {
    try {
      const {
        language = 'fr-FR',
        pitch = 1.0,
        rate = 0.5
      } = options;

      // Nettoyer le texte des emojis et caractères spéciaux
      const cleanText = this.cleanTextForSpeech(text);

      // Configurer les paramètres
      await Tts.setDefaultLanguage(language);
      await Tts.setDefaultPitch(pitch);
      await Tts.setDefaultRate(rate);

      // Parler
      await Tts.speak(cleanText);
      console.log(`🔊 Synthèse vocale: "${cleanText}"`);

    } catch (error) {
      console.error('Erreur synthèse vocale:', error);
      throw error;
    }
  }

  /**
   * Arrête la synthèse vocale
   */
  static async stopSpeaking(): Promise<void> {
    try {
      await Tts.stop();
      console.log('🔊 Synthèse vocale arrêtée');

    } catch (error) {
      console.error('Erreur arrêt synthèse vocale:', error);
    }
  }

  /**
   * Nettoie le texte pour la synthèse vocale
   */
  private static cleanTextForSpeech(text: string): string {
    return text
      // Enlever les emojis
      .replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '')
      // Enlever les caractères spéciaux
      .replace(/[*_`]/g, '')
      // Remplacer les sauts de ligne par des pauses
      .replace(/\n+/g, '. ')
      // Nettoyer les espaces multiples
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Traite un message vocal complet
   */
  static async processVoiceMessage(text: string): Promise<any> {
    try {
      console.log(`🎤 Traitement message vocal: "${text}"`);

      // Envoyer le message à l'API
      const response = await ApiService.sendMessage(text, { 
        isVoice: true,
        timestamp: new Date().toISOString()
      });

      if (response.success && response.data?.response) {
        // Synthèse vocale de la réponse
        await this.speak(response.data.response);
        
        return {
          success: true,
          userMessage: text,
          botResponse: response.data.response
        };
      } else {
        throw new Error(response.error || 'Erreur API');
      }

    } catch (error) {
      console.error('Erreur traitement message vocal:', error);
      
      // Message d'erreur vocal
      await this.speak("Désolé, je n'ai pas pu traiter votre demande. Veuillez réessayer.");
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Obtient les langues disponibles
   */
  static async getAvailableLanguages(): Promise<string[]> {
    try {
      const voices = await Tts.voices();
      const languages = [...new Set(voices.map(voice => voice.language))];
      return languages;

    } catch (error) {
      console.error('Erreur récupération langues:', error);
      return ['fr-FR', 'en-US'];
    }
  }

  /**
   * Vérifie si la reconnaissance vocale est disponible
   */
  static async isRecognitionAvailable(): Promise<boolean> {
    try {
      return await Voice.isAvailable();
    } catch (error) {
      console.error('Erreur vérification reconnaissance vocale:', error);
      return false;
    }
  }

  /**
   * Obtient l'état actuel de l'écoute
   */
  static isCurrentlyListening(): boolean {
    return this.isListening;
  }

  // Callbacks pour la reconnaissance vocale
  private static onSpeechStart = () => {
    console.log('🎤 Début de la reconnaissance vocale');
  };

  private static onSpeechEnd = () => {
    console.log('🎤 Fin de la reconnaissance vocale');
    this.isListening = false;
  };

  private static onSpeechResults = (event: any) => {
    const results = event.value;
    if (results && results.length > 0) {
      const recognizedText = results[0];
      console.log(`🎤 Texte reconnu: "${recognizedText}"`);
      
      // Traiter automatiquement le message vocal
      this.processVoiceMessage(recognizedText);
    }
  };

  private static onSpeechError = (event: any) => {
    console.error('❌ Erreur reconnaissance vocale:', event.error);
    this.isListening = false;
  };

  /**
   * Nettoie les ressources
   */
  static async cleanup(): Promise<void> {
    try {
      await this.stopListening();
      await this.stopSpeaking();
      
      Voice.destroy();
      Tts.removeAllListeners();
      
      this.isInitialized = false;
      console.log('🧹 Service vocal nettoyé');

    } catch (error) {
      console.error('Erreur nettoyage service vocal:', error);
    }
  }
}

export default VoiceService;
