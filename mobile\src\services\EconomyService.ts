/**
 * Service économique pour l'application mobile Nowee
 * Interface avec l'API backend pour les fonctionnalités économiques
 */

import axios, { AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configuration de l'API
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api/economy' 
  : 'https://nowee-app.herokuapp.com/api/economy';

// Types
interface WalletData {
  nowee_coins: number;
  time_credits: number;
  total_earned: number;
  total_spent: number;
  reputation_bonus: number;
}

interface Transaction {
  id: string;
  type: string;
  amount: number;
  time_credits: number;
  description: string;
  status: string;
  created_at: string;
  is_incoming: boolean;
}

interface EconomyStats {
  total_coins_in_circulation: number;
  total_time_credits: number;
  total_transactions: number;
  average_wallet_balance: number;
}

interface TransferRequest {
  fromPhone: string;
  toPhone: string;
  amount: number;
  description?: string;
}

interface ServiceCostRequest {
  baseRate?: number;
  durationHours?: number;
  urgency?: number;
  providerPhone?: string;
  skillCategory?: string;
}

interface ServiceCostResponse {
  total_coins: number;
  base_rate: number;
  duration_hours: number;
  urgency_multiplier: number;
  provider_rating: number;
  skill_category?: string;
}

/**
 * Service économique principal
 */
export class EconomyService {
  private static instance: EconomyService;
  private apiClient = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  constructor() {
    this.setupInterceptors();
  }

  static getInstance(): EconomyService {
    if (!EconomyService.instance) {
      EconomyService.instance = new EconomyService();
    }
    return EconomyService.instance;
  }

  private setupInterceptors() {
    // Intercepteur de requête pour ajouter l'authentification
    this.apiClient.interceptors.request.use(
      async (config) => {
        try {
          const token = await AsyncStorage.getItem('auth_token');
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
        } catch (error) {
          console.warn('Erreur récupération token:', error);
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Intercepteur de réponse pour gérer les erreurs
    this.apiClient.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('Erreur API Economy:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Récupère le portefeuille d'un utilisateur
   */
  async getWallet(phone: string): Promise<WalletData> {
    try {
      const response: AxiosResponse<{ success: boolean; wallet: WalletData }> = 
        await this.apiClient.get(`/wallet/${encodeURIComponent(phone)}`);
      
      if (!response.data.success) {
        throw new Error('Erreur récupération portefeuille');
      }

      return response.data.wallet;
    } catch (error) {
      console.error('Erreur getWallet:', error);
      // Retourner des données par défaut en cas d'erreur
      return {
        nowee_coins: 100,
        time_credits: 0,
        total_earned: 100,
        total_spent: 0,
        reputation_bonus: 0,
      };
    }
  }

  /**
   * Récupère l'historique des transactions
   */
  async getTransactions(phone: string, limit: number = 20): Promise<Transaction[]> {
    try {
      const response: AxiosResponse<{ success: boolean; transactions: Transaction[] }> = 
        await this.apiClient.get(`/transactions/${encodeURIComponent(phone)}?limit=${limit}`);
      
      if (!response.data.success) {
        throw new Error('Erreur récupération transactions');
      }

      return response.data.transactions;
    } catch (error) {
      console.error('Erreur getTransactions:', error);
      return [];
    }
  }

  /**
   * Effectue un transfert de NoweeCoins
   */
  async transferCoins(transferData: TransferRequest): Promise<boolean> {
    try {
      const response: AxiosResponse<{ success: boolean; message: string }> = 
        await this.apiClient.post('/transfer', transferData);
      
      return response.data.success;
    } catch (error) {
      console.error('Erreur transferCoins:', error);
      throw new Error('Impossible d\'effectuer le transfert');
    }
  }

  /**
   * Calcule le coût d'un service
   */
  async calculateServiceCost(costData: ServiceCostRequest): Promise<ServiceCostResponse> {
    try {
      const response: AxiosResponse<{ success: boolean; cost: ServiceCostResponse }> = 
        await this.apiClient.post('/service-cost', costData);
      
      if (!response.data.success) {
        throw new Error('Erreur calcul coût service');
      }

      return response.data.cost;
    } catch (error) {
      console.error('Erreur calculateServiceCost:', error);
      // Calcul par défaut
      const baseRate = costData.baseRate || 50;
      const duration = costData.durationHours || 1;
      const urgency = costData.urgency || 1;
      
      return {
        total_coins: baseRate * duration * urgency,
        base_rate: baseRate,
        duration_hours: duration,
        urgency_multiplier: urgency,
        provider_rating: 0,
        skill_category: costData.skillCategory,
      };
    }
  }

  /**
   * Récupère les statistiques économiques globales
   */
  async getEconomyStats(): Promise<EconomyStats> {
    try {
      const response: AxiosResponse<{ success: boolean; stats: EconomyStats }> = 
        await this.apiClient.get('/stats');
      
      if (!response.data.success) {
        throw new Error('Erreur récupération statistiques');
      }

      return response.data.stats;
    } catch (error) {
      console.error('Erreur getEconomyStats:', error);
      return {
        total_coins_in_circulation: 0,
        total_time_credits: 0,
        total_transactions: 0,
        average_wallet_balance: 0,
      };
    }
  }

  /**
   * Récompense un utilisateur (admin uniquement)
   */
  async rewardUser(phone: string, actionType: string, amount: number, description: string): Promise<boolean> {
    try {
      const response: AxiosResponse<{ success: boolean; message: string }> = 
        await this.apiClient.post('/reward', {
          phone,
          actionType,
          amount,
          description,
        });
      
      return response.data.success;
    } catch (error) {
      console.error('Erreur rewardUser:', error);
      return false;
    }
  }

  /**
   * Vérifie la connectivité avec l'API
   */
  async checkConnection(): Promise<boolean> {
    try {
      const response = await this.apiClient.get('/stats');
      return response.status === 200;
    } catch (error) {
      console.error('Erreur connexion API:', error);
      return false;
    }
  }

  /**
   * Cache les données du portefeuille localement
   */
  async cacheWalletData(phone: string, walletData: WalletData): Promise<void> {
    try {
      const cacheKey = `wallet_${phone}`;
      const cacheData = {
        data: walletData,
        timestamp: Date.now(),
      };
      await AsyncStorage.setItem(cacheKey, JSON.stringify(cacheData));
    } catch (error) {
      console.error('Erreur cache wallet:', error);
    }
  }

  /**
   * Récupère les données du portefeuille depuis le cache
   */
  async getCachedWalletData(phone: string): Promise<WalletData | null> {
    try {
      const cacheKey = `wallet_${phone}`;
      const cachedData = await AsyncStorage.getItem(cacheKey);
      
      if (!cachedData) return null;
      
      const { data, timestamp } = JSON.parse(cachedData);
      
      // Vérifier si le cache n'est pas trop ancien (5 minutes)
      if (Date.now() - timestamp > 5 * 60 * 1000) {
        return null;
      }
      
      return data;
    } catch (error) {
      console.error('Erreur récupération cache wallet:', error);
      return null;
    }
  }

  /**
   * Synchronise les données avec le serveur
   */
  async syncData(phone: string): Promise<void> {
    try {
      // Récupérer les dernières données du serveur
      const [wallet, transactions] = await Promise.all([
        this.getWallet(phone),
        this.getTransactions(phone, 10),
      ]);

      // Mettre en cache
      await this.cacheWalletData(phone, wallet);
      
      // Cache des transactions
      const transactionsCacheKey = `transactions_${phone}`;
      await AsyncStorage.setItem(transactionsCacheKey, JSON.stringify({
        data: transactions,
        timestamp: Date.now(),
      }));

    } catch (error) {
      console.error('Erreur synchronisation:', error);
    }
  }

  /**
   * Formate un montant en NoweeCoins
   */
  static formatCoins(amount: number): string {
    return `${amount.toFixed(2)} NC`;
  }

  /**
   * Formate une durée en heures
   */
  static formatTime(hours: number): string {
    if (hours < 1) {
      return `${Math.round(hours * 60)}min`;
    }
    return `${hours.toFixed(1)}h`;
  }

  /**
   * Calcule le pourcentage de changement
   */
  static calculatePercentageChange(current: number, previous: number): number {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  }

  /**
   * Valide un montant de transfert
   */
  static validateTransferAmount(amount: number, balance: number): { valid: boolean; error?: string } {
    if (amount <= 0) {
      return { valid: false, error: 'Le montant doit être positif' };
    }
    
    if (amount > balance) {
      return { valid: false, error: 'Solde insuffisant' };
    }
    
    if (amount > 1000) {
      return { valid: false, error: 'Montant maximum: 1000 NoweeCoins' };
    }
    
    return { valid: true };
  }

  /**
   * Génère un QR code pour recevoir des paiements
   */
  static generatePaymentQR(phone: string, amount?: number, description?: string): string {
    const paymentData = {
      type: 'nowee_payment',
      phone,
      amount,
      description,
      timestamp: Date.now(),
    };
    
    return JSON.stringify(paymentData);
  }

  /**
   * Parse un QR code de paiement
   */
  static parsePaymentQR(qrData: string): { phone: string; amount?: number; description?: string } | null {
    try {
      const data = JSON.parse(qrData);
      
      if (data.type !== 'nowee_payment' || !data.phone) {
        return null;
      }
      
      return {
        phone: data.phone,
        amount: data.amount,
        description: data.description,
      };
    } catch (error) {
      console.error('Erreur parsing QR:', error);
      return null;
    }
  }
}

// Instance singleton
export const economyService = EconomyService.getInstance();
export default economyService;
