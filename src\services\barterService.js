/**
 * Service de troc pour Nowee
 * Gère les échanges directs d'objets, services et temps
 */

import { dbService } from './databaseServiceUnified.js';
import { EconomyService } from './economyService.js';

// Types d'échange
const EXCHANGE_TYPES = {
  DIRECT_BARTER: 'DIRECT_BARTER',         // Objet contre objet
  TIME_FOR_OBJECT: 'TIME_FOR_OBJECT',     // Temps contre objet
  SERVICE_FOR_OBJECT: 'SERVICE_FOR_OBJECT', // Service contre objet
  MIXED_EXCHANGE: 'MIXED_EXCHANGE'        // Échange mixte
};

/**
 * Service de troc principal
 */
export class BarterService {
  
  /**
   * Crée une proposition de troc
   */
  static async createBarterProposal(proposerUserId, targetUserId, proposalData) {
    try {
      const {
        offeredResourceId,
        requestedResourceId,
        exchangeType,
        offeredCoins = 0,
        offeredTimeHours = 0,
        requestedCoins = 0,
        requestedTimeHours = 0,
        conditions = '',
        proposalMessage = ''
      } = proposalData;
      
      // Valider les ressources
      const [offeredResource, requestedResource] = await Promise.all([
        this.getResourceById(offeredResourceId),
        this.getResourceById(requestedResourceId)
      ]);
      
      if (!offeredResource || !requestedResource) {
        throw new Error('Ressources invalides pour le troc');
      }
      
      // Vérifier que les utilisateurs sont différents
      if (proposerUserId === targetUserId) {
        throw new Error('Impossible de faire un troc avec soi-même');
      }
      
      if (dbService.isUsingSupabase()) {
        const { data: proposal, error } = await dbService.supabase
          .from('barter_proposals')
          .insert([{
            proposer_id: proposerUserId,
            target_user_id: targetUserId,
            offered_resource_id: offeredResourceId,
            requested_resource_id: requestedResourceId,
            exchange_type: exchangeType,
            offered_coins: offeredCoins,
            offered_time_hours: offeredTimeHours,
            requested_coins: requestedCoins,
            requested_time_hours: requestedTimeHours,
            conditions: conditions,
            proposal_message: proposalMessage
          }])
          .select()
          .single();
        
        if (error) throw error;
        return proposal;
      }
      
      // Mode fallback
      return this.createBarterProposalFallback(proposerUserId, targetUserId, proposalData);
      
    } catch (error) {
      console.error('Erreur création proposition troc:', error);
      throw error;
    }
  }
  
  /**
   * Fallback pour création de proposition de troc
   */
  static createBarterProposalFallback(proposerUserId, targetUserId, proposalData) {
    const memoryStorage = dbService.getMemoryStorage();
    
    if (!memoryStorage.barterProposals) {
      memoryStorage.barterProposals = new Map();
    }
    
    const proposal = {
      id: `barter_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      proposer_id: proposerUserId,
      target_user_id: targetUserId,
      offered_resource_id: proposalData.offeredResourceId,
      requested_resource_id: proposalData.requestedResourceId,
      exchange_type: proposalData.exchangeType,
      offered_coins: proposalData.offeredCoins || 0,
      offered_time_hours: proposalData.offeredTimeHours || 0,
      requested_coins: proposalData.requestedCoins || 0,
      requested_time_hours: proposalData.requestedTimeHours || 0,
      conditions: proposalData.conditions || '',
      proposal_message: proposalData.proposalMessage || '',
      status: 'PENDING',
      created_at: new Date(),
      updated_at: new Date(),
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 jours
    };
    
    memoryStorage.barterProposals.set(proposal.id, proposal);
    return proposal;
  }
  
  /**
   * Accepte une proposition de troc
   */
  static async acceptBarterProposal(proposalId, acceptingUserId) {
    try {
      const proposal = await this.getBarterProposal(proposalId);
      
      if (!proposal) {
        throw new Error('Proposition de troc introuvable');
      }
      
      if (proposal.target_user_id !== acceptingUserId) {
        throw new Error('Seul le destinataire peut accepter cette proposition');
      }
      
      if (proposal.status !== 'PENDING') {
        throw new Error('Cette proposition n\'est plus disponible');
      }
      
      // Vérifier les soldes si nécessaire
      if (proposal.requested_coins > 0 || proposal.requested_time_hours > 0) {
        const targetWallet = await EconomyService.getWallet(acceptingUserId);
        
        if (targetWallet.nowee_coins < proposal.requested_coins) {
          throw new Error('Solde insuffisant en NoweeCoins');
        }
        
        if (targetWallet.time_credits < proposal.requested_time_hours) {
          throw new Error('Crédits temps insuffisants');
        }
      }
      
      if (proposal.offered_coins > 0 || proposal.offered_time_hours > 0) {
        const proposerWallet = await EconomyService.getWallet(proposal.proposer_id);
        
        if (proposerWallet.nowee_coins < proposal.offered_coins) {
          throw new Error('Le proposant n\'a pas suffisamment de NoweeCoins');
        }
        
        if (proposerWallet.time_credits < proposal.offered_time_hours) {
          throw new Error('Le proposant n\'a pas suffisamment de crédits temps');
        }
      }
      
      // Effectuer l'échange
      await this.executeBarterExchange(proposal);
      
      // Mettre à jour le statut
      await this.updateBarterProposalStatus(proposalId, 'ACCEPTED');
      
      return await this.getBarterProposal(proposalId);
      
    } catch (error) {
      console.error('Erreur acceptation proposition troc:', error);
      throw error;
    }
  }
  
  /**
   * Exécute l'échange de troc
   */
  static async executeBarterExchange(proposal) {
    try {
      const { proposer_id, target_user_id, offered_coins, offered_time_hours, requested_coins, requested_time_hours } = proposal;
      
      // Transferts de NoweeCoins
      if (offered_coins > 0) {
        await EconomyService.transferCoins(
          proposer_id,
          target_user_id,
          offered_coins,
          `Troc: ${offered_coins} NoweeCoins`,
          { barter_proposal_id: proposal.id }
        );
      }
      
      if (requested_coins > 0) {
        await EconomyService.transferCoins(
          target_user_id,
          proposer_id,
          requested_coins,
          `Troc: ${requested_coins} NoweeCoins`,
          { barter_proposal_id: proposal.id }
        );
      }
      
      // Transferts de crédits temps
      if (offered_time_hours > 0) {
        await EconomyService.updateWalletBalance(proposer_id, 0, -offered_time_hours);
        await EconomyService.updateWalletBalance(target_user_id, 0, offered_time_hours);
      }
      
      if (requested_time_hours > 0) {
        await EconomyService.updateWalletBalance(target_user_id, 0, -requested_time_hours);
        await EconomyService.updateWalletBalance(proposer_id, 0, requested_time_hours);
      }
      
      // Enregistrer la transaction de troc
      await EconomyService.recordTransaction({
        from_user_id: proposer_id,
        to_user_id: target_user_id,
        transaction_type: 'BARTER_TRADE',
        nowee_coins: offered_coins - requested_coins,
        time_credits: offered_time_hours - requested_time_hours,
        description: `Troc: ${proposal.exchange_type}`,
        metadata: {
          barter_proposal_id: proposal.id,
          exchange_type: proposal.exchange_type,
          offered_resource_id: proposal.offered_resource_id,
          requested_resource_id: proposal.requested_resource_id
        },
        status: 'COMPLETED'
      });
      
      console.log(`🔄 Troc exécuté: ${proposal.id}`);
      
    } catch (error) {
      console.error('Erreur exécution troc:', error);
      throw error;
    }
  }
  
  /**
   * Rejette une proposition de troc
   */
  static async rejectBarterProposal(proposalId, rejectingUserId, reason = '') {
    try {
      const proposal = await this.getBarterProposal(proposalId);
      
      if (!proposal) {
        throw new Error('Proposition de troc introuvable');
      }
      
      if (proposal.target_user_id !== rejectingUserId) {
        throw new Error('Seul le destinataire peut rejeter cette proposition');
      }
      
      await this.updateBarterProposalStatus(proposalId, 'REJECTED');
      
      // Enregistrer la raison du rejet
      if (reason && dbService.isUsingSupabase()) {
        await dbService.supabase
          .from('barter_proposals')
          .update({ 
            conditions: (proposal.conditions || '') + `\nRejeté: ${reason}`,
            updated_at: new Date().toISOString()
          })
          .eq('id', proposalId);
      }
      
      return await this.getBarterProposal(proposalId);
      
    } catch (error) {
      console.error('Erreur rejet proposition troc:', error);
      throw error;
    }
  }
  
  /**
   * Crée une contre-proposition
   */
  static async createCounterProposal(originalProposalId, counterProposalData) {
    try {
      const originalProposal = await this.getBarterProposal(originalProposalId);
      
      if (!originalProposal) {
        throw new Error('Proposition originale introuvable');
      }
      
      // Inverser les rôles pour la contre-proposition
      const counterProposal = await this.createBarterProposal(
        originalProposal.target_user_id,
        originalProposal.proposer_id,
        {
          ...counterProposalData,
          proposalMessage: `Contre-proposition à ${originalProposalId}: ${counterProposalData.proposalMessage || ''}`
        }
      );
      
      // Marquer la proposition originale comme ayant une contre-proposition
      await this.updateBarterProposalStatus(originalProposalId, 'COUNTER');
      
      return counterProposal;
      
    } catch (error) {
      console.error('Erreur création contre-proposition:', error);
      throw error;
    }
  }
  
  /**
   * Obtient une proposition de troc
   */
  static async getBarterProposal(proposalId) {
    try {
      if (dbService.isUsingSupabase()) {
        const { data, error } = await dbService.supabase
          .from('barter_proposals')
          .select(`
            *,
            proposer:proposer_id(id, name, rating),
            target:target_user_id(id, name, rating),
            offered_resource:offered_resource_id(id, title, description),
            requested_resource:requested_resource_id(id, title, description)
          `)
          .eq('id', proposalId)
          .single();
        
        if (error) throw error;
        return data;
      }
      
      // Mode fallback
      const memoryStorage = dbService.getMemoryStorage();
      return memoryStorage.barterProposals?.get(proposalId);
      
    } catch (error) {
      console.error('Erreur récupération proposition troc:', error);
      return null;
    }
  }
  
  /**
   * Met à jour le statut d'une proposition
   */
  static async updateBarterProposalStatus(proposalId, status) {
    try {
      if (dbService.isUsingSupabase()) {
        const { error } = await dbService.supabase
          .from('barter_proposals')
          .update({ 
            status,
            updated_at: new Date().toISOString(),
            ...(status === 'COMPLETED' && { completed_at: new Date().toISOString() })
          })
          .eq('id', proposalId);
        
        if (error) throw error;
      } else {
        // Mode fallback
        const memoryStorage = dbService.getMemoryStorage();
        const proposal = memoryStorage.barterProposals?.get(proposalId);
        
        if (proposal) {
          proposal.status = status;
          proposal.updated_at = new Date();
          if (status === 'COMPLETED') {
            proposal.completed_at = new Date();
          }
        }
      }
      
    } catch (error) {
      console.error('Erreur mise à jour statut proposition:', error);
    }
  }
  
  /**
   * Obtient les propositions de troc d'un utilisateur
   */
  static async getUserBarterProposals(userId, type = 'all') {
    try {
      if (dbService.isUsingSupabase()) {
        let query = dbService.supabase
          .from('barter_proposals')
          .select(`
            *,
            proposer:proposer_id(id, name, rating),
            target:target_user_id(id, name, rating),
            offered_resource:offered_resource_id(id, title, description),
            requested_resource:requested_resource_id(id, title, description)
          `);
        
        if (type === 'sent') {
          query = query.eq('proposer_id', userId);
        } else if (type === 'received') {
          query = query.eq('target_user_id', userId);
        } else {
          query = query.or(`proposer_id.eq.${userId},target_user_id.eq.${userId}`);
        }
        
        const { data, error } = await query
          .order('created_at', { ascending: false })
          .limit(20);
        
        if (error) throw error;
        return data;
      }
      
      // Mode fallback
      const memoryStorage = dbService.getMemoryStorage();
      const proposals = Array.from(memoryStorage.barterProposals?.values() || []);
      
      return proposals
        .filter(p => {
          if (type === 'sent') return p.proposer_id === userId;
          if (type === 'received') return p.target_user_id === userId;
          return p.proposer_id === userId || p.target_user_id === userId;
        })
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 20);
      
    } catch (error) {
      console.error('Erreur récupération propositions utilisateur:', error);
      return [];
    }
  }
  
  /**
   * Suggère des trocs automatiques basés sur les ressources
   */
  static async suggestBarterMatches(userId) {
    try {
      // Récupérer les ressources de l'utilisateur
      const userResources = await this.getUserResources(userId);
      const userOffers = userResources.filter(r => r.type === 'OFFER');
      const userNeeds = userResources.filter(r => r.type === 'NEED');
      
      const suggestions = [];
      
      // Pour chaque besoin de l'utilisateur, chercher des offres compatibles
      for (const need of userNeeds) {
        const compatibleOffers = await this.findCompatibleOffers(need);
        
        for (const offer of compatibleOffers) {
          // Vérifier si l'offrant a des besoins que l'utilisateur peut satisfaire
          const offererNeeds = await this.getUserResources(offer.user_id, 'NEED');
          
          for (const offererNeed of offererNeeds) {
            const canSatisfy = userOffers.some(userOffer => 
              this.areResourcesCompatible(userOffer, offererNeed)
            );
            
            if (canSatisfy) {
              suggestions.push({
                type: 'MUTUAL_BARTER',
                userNeed: need,
                offeredResource: offer,
                offererNeed: offererNeed,
                matchScore: this.calculateBarterScore(need, offer, offererNeed)
              });
            }
          }
        }
      }
      
      // Trier par score de compatibilité
      return suggestions
        .sort((a, b) => b.matchScore - a.matchScore)
        .slice(0, 10);
      
    } catch (error) {
      console.error('Erreur suggestions troc:', error);
      return [];
    }
  }
  
  /**
   * Calcule un score de compatibilité pour le troc
   */
  static calculateBarterScore(need, offer, offererNeed) {
    let score = 0;
    
    // Compatibilité de catégorie
    if (need.category === offer.category) score += 40;
    
    // Urgence
    score += need.urgency * 5;
    score += offererNeed.urgency * 5;
    
    // Distance (si disponible)
    if (need.latitude && offer.latitude) {
      const distance = this.calculateDistance(need, offer);
      if (distance <= 5) score += 20;
      else if (distance <= 10) score += 10;
    }
    
    return Math.min(score, 100);
  }
  
  /**
   * Vérifie si deux ressources sont compatibles
   */
  static areResourcesCompatible(resource1, resource2) {
    return resource1.category === resource2.category ||
           resource1.tags?.some(tag => resource2.tags?.includes(tag));
  }
  
  /**
   * Trouve des offres compatibles avec un besoin
   */
  static async findCompatibleOffers(need) {
    try {
      if (dbService.isUsingSupabase()) {
        const { data, error } = await dbService.supabase
          .from('resources')
          .select('*, users!inner(id, name, rating)')
          .eq('type', 'OFFER')
          .eq('category', need.category)
          .eq('status', 'ACTIVE')
          .neq('user_id', need.user_id)
          .limit(10);
        
        if (error) throw error;
        return data;
      }
      
      // Mode fallback
      const memoryStorage = dbService.getMemoryStorage();
      const resources = Array.from(memoryStorage.resources?.values() || []);
      
      return resources
        .filter(r => 
          r.type === 'OFFER' && 
          r.category === need.category && 
          r.status === 'ACTIVE' &&
          r.user_id !== need.user_id
        )
        .slice(0, 10);
      
    } catch (error) {
      console.error('Erreur recherche offres compatibles:', error);
      return [];
    }
  }
  
  /**
   * Obtient les ressources d'un utilisateur
   */
  static async getUserResources(userId, type = null) {
    try {
      if (dbService.isUsingSupabase()) {
        let query = dbService.supabase
          .from('resources')
          .select('*')
          .eq('user_id', userId)
          .eq('status', 'ACTIVE');
        
        if (type) {
          query = query.eq('type', type);
        }
        
        const { data, error } = await query;
        if (error) throw error;
        return data;
      }
      
      // Mode fallback
      const memoryStorage = dbService.getMemoryStorage();
      const resources = Array.from(memoryStorage.resources?.values() || []);
      
      return resources.filter(r => 
        r.user_id === userId && 
        r.status === 'ACTIVE' &&
        (!type || r.type === type)
      );
      
    } catch (error) {
      console.error('Erreur récupération ressources utilisateur:', error);
      return [];
    }
  }
  
  /**
   * Obtient une ressource par ID
   */
  static async getResourceById(resourceId) {
    try {
      if (dbService.isUsingSupabase()) {
        const { data, error } = await dbService.supabase
          .from('resources')
          .select('*')
          .eq('id', resourceId)
          .single();
        
        if (error) throw error;
        return data;
      }
      
      // Mode fallback
      const memoryStorage = dbService.getMemoryStorage();
      return memoryStorage.resources?.get(resourceId);
      
    } catch (error) {
      console.error('Erreur récupération ressource:', error);
      return null;
    }
  }
  
  /**
   * Calcule la distance entre deux ressources
   */
  static calculateDistance(resource1, resource2) {
    if (!resource1.latitude || !resource2.latitude) return null;
    
    const R = 6371; // Rayon de la Terre en km
    const dLat = (resource2.latitude - resource1.latitude) * Math.PI / 180;
    const dLon = (resource2.longitude - resource1.longitude) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(resource1.latitude * Math.PI / 180) * Math.cos(resource2.latitude * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }
}

export default BarterService;
