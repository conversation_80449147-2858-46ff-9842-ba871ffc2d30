{"name": "nowee-mobile", "version": "1.0.0", "description": "Application mobile Nowee - Entraide locale révolutionnaire", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "node build-mobile.js", "build:android:release": "node build-mobile.js --release", "metro": "npx react-native start", "clean": "npx react-native clean", "setup": "npm install --legacy-peer-deps", "reset": "npx react-native clean && npm install --legacy-peer-deps"}, "dependencies": {"react": "18.3.1", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "react-native-vector-icons": "^10.0.3", "react-native-maps": "^1.8.0", "react-native-geolocation-service": "^5.3.1", "react-native-permissions": "^4.1.0", "react-native-linear-gradient": "^2.8.3", "react-native-animatable": "^1.4.0", "react-native-modal": "^13.0.1", "react-native-svg": "^14.1.0", "react-native-chart-kit": "^6.12.0", "react-native-progress": "^5.0.1", "react-native-device-info": "^10.11.0", "react-native-flash-message": "^0.4.2", "react-native-skeleton-placeholder": "^5.2.4", "react-native-fast-image": "^8.6.3", "@react-native-async-storage/async-storage": "^1.19.0", "axios": "^1.6.0", "moment": "^2.29.4", "lodash": "^4.17.21"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.0", "@react-native/metro-config": "^0.72.0", "@react-native-community/cli": "^11.3.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.3.1", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "mobile", "entraide", "local", "communauté", "troc", "économie", "nowee"], "author": "Nowee Team", "license": "MIT"}