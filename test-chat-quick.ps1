Write-Host "🤖 Test rapide Chat IA Nowee" -ForegroundColor Blue

$body = '{"message":"Salut Nowee ! Je cherche de l aide pour reparer mon telephone a Dakar","phone":"+221701234567","context":"test_rapide"}'

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/api/chat/ai" -Method POST -Body $body -ContentType "application/json"
    $result = $response.Content | ConvertFrom-Json
    
    Write-Host "✅ IA Nowee repond:" -ForegroundColor Green
    Write-Host $result.response -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📊 Tokens: $($result.tokens_used)" -ForegroundColor Yellow
    Write-Host "🧠 Modele: $($result.model)" -ForegroundColor Magenta
} catch {
    Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
}
