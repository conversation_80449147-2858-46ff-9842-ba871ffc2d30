/**
 * Configuration Metro pour Nowee Mobile
 * Support des nouvelles dépendances et optimisations
 */

const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

const defaultConfig = getDefaultConfig(__dirname);

const config = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
  resolver: {
    assetExts: [
      ...defaultConfig.resolver.assetExts,
      'bin',
      'txt',
      'jpg',
      'png',
      'json',
      'svg',
    ],
    sourceExts: [
      ...defaultConfig.resolver.sourceExts,
      'jsx',
      'js',
      'ts',
      'tsx',
      'json',
    ],
  },
  maxWorkers: 2,
};

module.exports = mergeConfig(defaultConfig, config);
