/**
 * Serveur de test Nowee
 * Version simplifiée pour démonstration
 */

const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Données de test
const mockData = {
  users: 1250,
  helps: 3420,
  coins: 125000,
  communities: 45,
  wallets: new Map(),
  transactions: [],
  needs: [
    {
      id: '1',
      phone: '+221701234567',
      title: 'Aide pour déménagement',
      description: 'Je cherche de l\'aide pour déménager ce weekend',
      category: 'SERVICE',
      urgency: 'medium',
      latitude: 14.6928,
      longitude: -17.4467,
      address: 'Dakar, Sénégal',
      status: 'active',
      created_at: new Date().toISOString()
    },
    {
      id: '2',
      phone: '+221701234568',
      title: 'Cours de français',
      description: 'J\'offre des cours de français pour débutants',
      category: 'EDUCATION',
      urgency: 'low',
      latitude: 14.6937,
      longitude: -17.4441,
      address: 'Plateau, Dakar',
      status: 'active',
      created_at: new Date().toISOString()
    }
  ]
};

// Initialiser quelques portefeuilles de test
mockData.wallets.set('+221701234567', {
  phone: '+221701234567',
  nowee_coins: 150,
  time_credits: 2.5,
  total_earned: 200,
  total_spent: 50,
  reputation_bonus: 10
});

mockData.wallets.set('+221701234568', {
  phone: '+221701234568',
  nowee_coins: 75,
  time_credits: 1.0,
  total_earned: 100,
  total_spent: 25,
  reputation_bonus: 5
});

// Routes de santé
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: 'test',
    features: {
      economy: true,
      barter: true,
      voice: false,
      maps: true,
      notifications: false
    },
    stats: {
      users: mockData.users,
      helps: mockData.helps,
      coins: mockData.coins,
      communities: mockData.communities
    }
  });
});

// API Économique
app.get('/api/economy/wallet/:phone', (req, res) => {
  try {
    const { phone } = req.params;
    
    let wallet = mockData.wallets.get(phone);
    
    if (!wallet) {
      // Créer un nouveau portefeuille
      wallet = {
        phone,
        nowee_coins: 100,
        time_credits: 0,
        total_earned: 100,
        total_spent: 0,
        reputation_bonus: 0
      };
      mockData.wallets.set(phone, wallet);
    }
    
    res.json({ success: true, wallet });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.get('/api/economy/transactions/:phone', (req, res) => {
  try {
    const { phone } = req.params;
    const limit = parseInt(req.query.limit) || 20;
    
    // Générer quelques transactions de test
    const transactions = [
      {
        id: '1',
        type: 'TRANSFER',
        amount: 25,
        time_credits: 0,
        description: 'Aide pour courses',
        status: 'completed',
        created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        is_incoming: true,
        from_phone: '+221701234568',
        to_phone: phone
      },
      {
        id: '2',
        type: 'REWARD',
        amount: 50,
        time_credits: 1,
        description: 'Bonus d\'inscription',
        status: 'completed',
        created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        is_incoming: true,
        from_phone: 'system',
        to_phone: phone
      }
    ].slice(0, limit);
    
    res.json({ success: true, transactions });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/economy/transfer', (req, res) => {
  try {
    const { fromPhone, toPhone, amount, description } = req.body;
    
    if (!fromPhone || !toPhone || !amount || amount <= 0) {
      return res.status(400).json({ 
        success: false, 
        error: 'Paramètres invalides' 
      });
    }
    
    // Vérifier le solde
    const fromWallet = mockData.wallets.get(fromPhone);
    if (!fromWallet || fromWallet.nowee_coins < amount) {
      return res.status(400).json({ 
        success: false, 
        error: 'Solde insuffisant' 
      });
    }
    
    // Effectuer le transfert
    fromWallet.nowee_coins -= amount;
    fromWallet.total_spent += amount;
    
    let toWallet = mockData.wallets.get(toPhone);
    if (!toWallet) {
      toWallet = {
        phone: toPhone,
        nowee_coins: amount,
        time_credits: 0,
        total_earned: amount,
        total_spent: 0,
        reputation_bonus: 0
      };
    } else {
      toWallet.nowee_coins += amount;
      toWallet.total_earned += amount;
    }
    mockData.wallets.set(toPhone, toWallet);
    
    // Enregistrer la transaction
    const transaction = {
      id: Date.now().toString(),
      type: 'TRANSFER',
      amount,
      time_credits: 0,
      description: description || 'Transfert',
      status: 'completed',
      created_at: new Date().toISOString(),
      from_phone: fromPhone,
      to_phone: toPhone
    };
    mockData.transactions.push(transaction);
    
    res.json({ success: true, message: 'Transfert effectué avec succès' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// API Besoins
app.get('/api/needs', (req, res) => {
  try {
    const { category } = req.query;
    
    let needs = [...mockData.needs];
    
    if (category) {
      needs = needs.filter(need => need.category === category);
    }
    
    res.json({ success: true, needs });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/needs', (req, res) => {
  try {
    const {
      phone,
      title,
      description,
      category,
      urgency,
      latitude,
      longitude,
      address
    } = req.body;
    
    const need = {
      id: Date.now().toString(),
      phone,
      title,
      description,
      category,
      urgency: urgency || 'medium',
      latitude,
      longitude,
      address,
      status: 'active',
      created_at: new Date().toISOString()
    };
    
    mockData.needs.push(need);
    
    res.json({ success: true, need });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// API Statistiques
app.get('/api/stats', (req, res) => {
  try {
    // Mettre à jour les stats dynamiquement
    mockData.users += Math.floor(Math.random() * 2);
    mockData.helps += Math.floor(Math.random() * 3);
    mockData.coins += Math.floor(Math.random() * 50);
    
    res.json({
      success: true,
      stats: {
        users: mockData.users,
        helps: mockData.helps,
        coins: mockData.coins,
        communities: mockData.communities,
        total_coins_in_circulation: mockData.coins,
        total_transactions: mockData.transactions.length,
        average_wallet_balance: 112.5
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// API de test pour WhatsApp (simulation)
app.post('/api/whatsapp/webhook', (req, res) => {
  try {
    const { Body, From } = req.body;
    
    // Simulation d'une réponse IA
    const responses = [
      "Salut ! Je suis Nowee, ton assistant d'entraide locale. Comment puis-je t'aider aujourd'hui ?",
      "J'ai trouvé plusieurs personnes près de toi qui peuvent t'aider ! Veux-tu que je te mette en contact ?",
      "Super ! J'ai enregistré ton besoin. Tu recevras une notification dès qu'une aide sera disponible.",
      "Merci d'aider ta communauté ! Tu as gagné 25 NoweeCoins pour ton aide."
    ];
    
    const response = responses[Math.floor(Math.random() * responses.length)];
    
    res.json({ 
      success: true, 
      message: 'Message traité',
      response: response
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Route 404
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route non trouvée',
    path: req.originalUrl,
    availableRoutes: [
      'GET /health',
      'GET /api/economy/wallet/:phone',
      'GET /api/economy/transactions/:phone',
      'POST /api/economy/transfer',
      'GET /api/needs',
      'POST /api/needs',
      'GET /api/stats',
      'POST /api/whatsapp/webhook'
    ]
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur de test Nowee démarré sur le port ${PORT}`);
  console.log(`📊 API de santé: http://localhost:${PORT}/health`);
  console.log(`💰 API économique: http://localhost:${PORT}/api/economy/wallet/+221701234567`);
  console.log(`📱 Simulation WhatsApp: http://localhost:${PORT}/api/whatsapp/webhook`);
  console.log(`🎯 Statistiques: http://localhost:${PORT}/api/stats`);
});

module.exports = app;
