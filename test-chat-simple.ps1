Write-Host "🤖 Test Chat IA Nowee Simple" -ForegroundColor Blue

$body = @{
    message = "Salut Nowee ! Comment ça va ?"
    phone = "+221701234567"
    context = "test"
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/api/chat/ai" -Method POST -Body $body -ContentType "application/json"
    $result = $response.Content | ConvertFrom-Json
    
    Write-Host "✅ IA Nowee répond:" -ForegroundColor Green
    Write-Host $result.response -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📊 Tokens: $($result.tokensUsed)" -ForegroundColor Yellow
    Write-Host "🧠 Modèle: $($result.model)" -ForegroundColor Magenta
} catch {
    Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
}
