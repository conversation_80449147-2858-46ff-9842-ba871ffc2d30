/**
 * Écran de chat de l'application Nowee
 * Interface de conversation avec l'IA incluant le support vocal
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import * as Animatable from 'react-native-animatable';

import { useTheme } from '../services/ThemeService';
import { useAuth } from '../services/AuthService';
import { ApiService } from '../services/ApiService';
import VoiceButton from '../components/VoiceButton';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  isVoice?: boolean;
}

const ChatScreen: React.FC = () => {
  const { colors } = useTheme();
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showVoiceButton, setShowVoiceButton] = useState(true);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    loadChatHistory();
    addWelcomeMessage();
  }, []);

  useEffect(() => {
    // Auto-scroll vers le bas quand de nouveaux messages arrivent
    scrollToBottom();
  }, [messages]);

  const loadChatHistory = async () => {
    try {
      const history = await ApiService.getChatHistory(20);
      const formattedMessages = history.map((item: any, index: number) => [
        {
          id: `user_${index}`,
          text: item.userMessage,
          isUser: true,
          timestamp: new Date(item.timestamp),
        },
        {
          id: `bot_${index}`,
          text: item.botResponse,
          isUser: false,
          timestamp: new Date(item.timestamp),
        }
      ]).flat();

      setMessages(formattedMessages);
    } catch (error) {
      console.error('Erreur chargement historique:', error);
    }
  };

  const addWelcomeMessage = () => {
    const welcomeMessage: Message = {
      id: 'welcome',
      text: `👋 Salut ${user?.name || 'ami'} ! Je suis Nowee, votre assistant d'entraide locale.\n\n💬 Écrivez votre besoin ou 🎤 utilisez le bouton vocal pour me parler !`,
      isUser: false,
      timestamp: new Date(),
    };

    setMessages(prev => [welcomeMessage, ...prev]);
  };

  const sendMessage = async (text: string, isVoice = false) => {
    if (!text.trim()) return;

    const userMessage: Message = {
      id: `user_${Date.now()}`,
      text: text.trim(),
      isUser: true,
      timestamp: new Date(),
      isVoice,
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    try {
      const response = await ApiService.sendMessage(text, { 
        isVoice,
        timestamp: new Date().toISOString()
      });

      const botMessage: Message = {
        id: `bot_${Date.now()}`,
        text: response.success ? response.data.response : 'Désolé, je n\'ai pas pu traiter votre demande.',
        isUser: false,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, botMessage]);

    } catch (error) {
      console.error('Erreur envoi message:', error);
      
      const errorMessage: Message = {
        id: `error_${Date.now()}`,
        text: 'Une erreur est survenue. Vérifiez votre connexion et réessayez.',
        isUser: false,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVoiceMessage = (userText: string, botResponse: string) => {
    const userMessage: Message = {
      id: `voice_user_${Date.now()}`,
      text: userText,
      isUser: true,
      timestamp: new Date(),
      isVoice: true,
    };

    const botMessage: Message = {
      id: `voice_bot_${Date.now()}`,
      text: botResponse,
      isUser: false,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage, botMessage]);
  };

  const handleVoiceError = (error: string) => {
    Alert.alert('Erreur Vocal', error, [{ text: 'OK' }]);
  };

  const scrollToBottom = () => {
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const renderMessage = (message: Message) => {
    const isUser = message.isUser;
    
    return (
      <Animatable.View
        key={message.id}
        animation="fadeInUp"
        duration={300}
        style={[
          styles.messageContainer,
          isUser ? styles.userMessageContainer : styles.botMessageContainer,
        ]}
      >
        <View
          style={[
            styles.messageBubble,
            isUser 
              ? [styles.userMessage, { backgroundColor: colors.primary }]
              : [styles.botMessage, { backgroundColor: colors.surface }],
          ]}
        >
          {message.isVoice && (
            <View style={styles.voiceIndicator}>
              <Icon name="mic" size={16} color={isUser ? '#FFFFFF' : colors.primary} />
              <Text style={[
                styles.voiceText,
                { color: isUser ? '#FFFFFF' : colors.primary }
              ]}>
                Message vocal
              </Text>
            </View>
          )}
          
          <Text
            style={[
              styles.messageText,
              { color: isUser ? '#FFFFFF' : colors.text },
            ]}
          >
            {message.text}
          </Text>
          
          <Text
            style={[
              styles.timestamp,
              { color: isUser ? 'rgba(255,255,255,0.7)' : colors.textSecondary },
            ]}
          >
            {message.timestamp.toLocaleTimeString('fr-FR', {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </Text>
        </View>
      </Animatable.View>
    );
  };

  const renderTypingIndicator = () => {
    if (!isLoading) return null;

    return (
      <Animatable.View
        animation="fadeIn"
        style={[styles.messageContainer, styles.botMessageContainer]}
      >
        <View style={[styles.messageBubble, styles.botMessage, { backgroundColor: colors.surface }]}>
          <Animatable.View
            animation="pulse"
            iterationCount="infinite"
            style={styles.typingIndicator}
          >
            <Text style={[styles.typingText, { color: colors.textSecondary }]}>
              Nowee écrit...
            </Text>
          </Animatable.View>
        </View>
      </Animatable.View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
        >
          {messages.map(renderMessage)}
          {renderTypingIndicator()}
        </ScrollView>

        {/* Zone de saisie */}
        <View style={[styles.inputContainer, { backgroundColor: colors.surface }]}>
          <TextInput
            style={[
              styles.textInput,
              { 
                backgroundColor: colors.background,
                color: colors.text,
                borderColor: colors.primary,
              },
            ]}
            value={inputText}
            onChangeText={setInputText}
            placeholder="Tapez votre message..."
            placeholderTextColor={colors.textSecondary}
            multiline
            maxLength={500}
            onSubmitEditing={() => sendMessage(inputText)}
            blurOnSubmit={false}
          />

          <View style={styles.buttonsContainer}>
            {/* Bouton vocal */}
            {showVoiceButton && (
              <VoiceButton
                size="small"
                onVoiceMessage={handleVoiceMessage}
                onError={handleVoiceError}
                disabled={isLoading}
                style={styles.voiceButton}
              />
            )}

            {/* Bouton d'envoi */}
            <TouchableOpacity
              style={[
                styles.sendButton,
                { 
                  backgroundColor: inputText.trim() ? colors.primary : colors.textSecondary,
                },
              ]}
              onPress={() => sendMessage(inputText)}
              disabled={!inputText.trim() || isLoading}
            >
              <Icon name="send" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  messagesContent: {
    paddingVertical: 16,
  },
  messageContainer: {
    marginVertical: 4,
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  botMessageContainer: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
  },
  userMessage: {
    borderBottomRightRadius: 4,
  },
  botMessage: {
    borderBottomLeftRadius: 4,
  },
  voiceIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  voiceText: {
    fontSize: 12,
    marginLeft: 4,
    fontStyle: 'italic',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  timestamp: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'right',
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 12,
    maxHeight: 100,
    fontSize: 16,
  },
  buttonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  voiceButton: {
    marginRight: 8,
  },
  sendButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ChatScreen;
