/**
 * Service de gestion des utilisateurs et sessions Nowee
 * Gère les profils, historiques et préférences utilisateur
 */

import { extractLocationFromMessage } from '../utils/locationUtils.js';

// Stockage temporaire (sera remplacé par une vraie DB)
const userSessions = new Map();
const userProfiles = new Map();
const conversationHistory = new Map();

/**
 * Structure d'un profil utilisateur
 */
class UserProfile {
  constructor(phone) {
    this.phone = phone;
    this.createdAt = new Date();
    this.lastActive = new Date();
    this.messageCount = 0;
    this.preferredLanguage = 'fr';
    this.location = null;
    this.preferences = {
      responseStyle: 'friendly', // friendly, professional, casual
      maxResponseLength: 'medium', // short, medium, long
      includeEmojis: true,
      culturalContext: 'auto' // auto, african, european, universal
    };
    this.reputation = {
      helpGiven: 0,
      helpReceived: 0,
      rating: 5.0,
      badges: []
    };
    this.needs = []; // Historique des besoins exprimés
    this.offers = []; // Historique des offres d'aide
  }
}

/**
 * Structure d'une session de conversation
 */
class ConversationSession {
  constructor(phone) {
    this.phone = phone;
    this.startedAt = new Date();
    this.lastMessageAt = new Date();
    this.messageCount = 0;
    this.currentContext = null;
    this.awaitingResponse = false;
    this.conversationFlow = 'initial'; // initial, need_clarification, matching, completed
  }
}

/**
 * Obtient ou crée un profil utilisateur
 */
export function getUserProfile(phone) {
  const cleanPhone = phone.replace('whatsapp:', '');
  
  if (!userProfiles.has(cleanPhone)) {
    userProfiles.set(cleanPhone, new UserProfile(cleanPhone));
  }
  
  const profile = userProfiles.get(cleanPhone);
  profile.lastActive = new Date();
  
  return profile;
}

/**
 * Obtient ou crée une session de conversation
 */
export function getConversationSession(phone) {
  const cleanPhone = phone.replace('whatsapp:', '');
  
  if (!userSessions.has(cleanPhone)) {
    userSessions.set(cleanPhone, new ConversationSession(cleanPhone));
  }
  
  const session = userSessions.get(cleanPhone);
  session.lastMessageAt = new Date();
  session.messageCount++;
  
  return session;
}

/**
 * Met à jour le profil utilisateur avec de nouvelles informations
 */
export function updateUserProfile(phone, updates) {
  const profile = getUserProfile(phone);
  
  // Mise à jour de la localisation si détectée
  if (updates.location) {
    profile.location = updates.location;
  }
  
  // Mise à jour des préférences
  if (updates.preferences) {
    profile.preferences = { ...profile.preferences, ...updates.preferences };
  }
  
  // Incrémentation du compteur de messages
  profile.messageCount++;
  
  return profile;
}

/**
 * Ajoute un message à l'historique de conversation
 */
export function addToConversationHistory(phone, message, response, analysis) {
  const cleanPhone = phone.replace('whatsapp:', '');
  
  if (!conversationHistory.has(cleanPhone)) {
    conversationHistory.set(cleanPhone, []);
  }
  
  const history = conversationHistory.get(cleanPhone);
  history.push({
    timestamp: new Date(),
    userMessage: message,
    botResponse: response,
    analysis: analysis,
    messageId: `${cleanPhone}_${Date.now()}`
  });
  
  // Garder seulement les 50 derniers messages
  if (history.length > 50) {
    history.splice(0, history.length - 50);
  }
  
  return history;
}

/**
 * Obtient l'historique de conversation d'un utilisateur
 */
export function getConversationHistory(phone, limit = 10) {
  const cleanPhone = phone.replace('whatsapp:', '');
  const history = conversationHistory.get(cleanPhone) || [];
  
  return history.slice(-limit);
}

/**
 * Enregistre un besoin exprimé par l'utilisateur
 */
export function recordUserNeed(phone, needData) {
  const profile = getUserProfile(phone);
  
  const need = {
    id: `need_${Date.now()}`,
    type: needData.type,
    description: needData.description,
    location: needData.location,
    urgency: needData.urgency,
    status: 'open', // open, matched, fulfilled, cancelled
    createdAt: new Date(),
    responses: []
  };
  
  profile.needs.push(need);
  
  return need;
}

/**
 * Enregistre une offre d'aide d'un utilisateur
 */
export function recordUserOffer(phone, offerData) {
  const profile = getUserProfile(phone);
  
  const offer = {
    id: `offer_${Date.now()}`,
    type: offerData.type,
    description: offerData.description,
    location: offerData.location,
    availability: offerData.availability,
    conditions: offerData.conditions,
    status: 'available', // available, matched, completed, withdrawn
    createdAt: new Date()
  };
  
  profile.offers.push(offer);
  
  return offer;
}

/**
 * Analyse les patterns de comportement d'un utilisateur
 */
export function analyzeUserBehavior(phone) {
  const profile = getUserProfile(phone);
  const history = getConversationHistory(phone, 20);
  
  const analysis = {
    activityLevel: calculateActivityLevel(profile),
    preferredNeedTypes: analyzePreferredNeedTypes(profile.needs),
    responsePatterns: analyzeResponsePatterns(history),
    helpfulness: calculateHelpfulness(profile),
    engagement: calculateEngagement(profile, history)
  };
  
  return analysis;
}

/**
 * Calcule le niveau d'activité d'un utilisateur
 */
function calculateActivityLevel(profile) {
  const daysSinceCreation = (new Date() - profile.createdAt) / (1000 * 60 * 60 * 24);
  const messagesPerDay = profile.messageCount / Math.max(daysSinceCreation, 1);
  
  if (messagesPerDay > 5) return 'high';
  if (messagesPerDay > 2) return 'medium';
  return 'low';
}

/**
 * Analyse les types de besoins préférés
 */
function analyzePreferredNeedTypes(needs) {
  const typeCounts = {};
  
  needs.forEach(need => {
    typeCounts[need.type] = (typeCounts[need.type] || 0) + 1;
  });
  
  return Object.entries(typeCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
    .map(([type]) => type);
}

/**
 * Analyse les patterns de réponse
 */
function analyzeResponsePatterns(history) {
  if (history.length === 0) return { avgLength: 0, sentiment: 'neutral' };
  
  const avgLength = history.reduce((sum, msg) => sum + msg.userMessage.length, 0) / history.length;
  
  const sentiments = history.map(msg => msg.analysis?.sentiment || 'neutral');
  const sentimentCounts = sentiments.reduce((acc, sentiment) => {
    acc[sentiment] = (acc[sentiment] || 0) + 1;
    return acc;
  }, {});
  
  const dominantSentiment = Object.entries(sentimentCounts)
    .sort(([,a], [,b]) => b - a)[0]?.[0] || 'neutral';
  
  return {
    avgLength: Math.round(avgLength),
    sentiment: dominantSentiment,
    consistency: calculateConsistency(history)
  };
}

/**
 * Calcule la consistance des interactions
 */
function calculateConsistency(history) {
  if (history.length < 3) return 'insufficient_data';
  
  const intervals = [];
  for (let i = 1; i < history.length; i++) {
    const interval = history[i].timestamp - history[i-1].timestamp;
    intervals.push(interval);
  }
  
  const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
  const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
  
  // Si la variance est faible, l'utilisateur est consistant
  return variance < avgInterval * 0.5 ? 'consistent' : 'variable';
}

/**
 * Calcule le score d'aide apportée
 */
function calculateHelpfulness(profile) {
  const { helpGiven, helpReceived, rating } = profile.reputation;
  
  if (helpGiven === 0 && helpReceived === 0) return 'new_user';
  
  const helpRatio = helpGiven / Math.max(helpReceived, 1);
  
  if (helpRatio > 2) return 'very_helpful';
  if (helpRatio > 1) return 'helpful';
  if (helpRatio > 0.5) return 'balanced';
  return 'needs_encouragement';
}

/**
 * Calcule le niveau d'engagement
 */
function calculateEngagement(profile, history) {
  const recentActivity = history.filter(msg => 
    (new Date() - msg.timestamp) < 7 * 24 * 60 * 60 * 1000 // 7 jours
  ).length;
  
  const totalNeeds = profile.needs.length;
  const totalOffers = profile.offers.length;
  
  const engagementScore = recentActivity + (totalNeeds * 2) + (totalOffers * 3);
  
  if (engagementScore > 20) return 'highly_engaged';
  if (engagementScore > 10) return 'engaged';
  if (engagementScore > 5) return 'moderately_engaged';
  return 'low_engagement';
}

/**
 * Obtient des statistiques globales
 */
export function getGlobalStats() {
  return {
    totalUsers: userProfiles.size,
    activeSessions: userSessions.size,
    totalMessages: Array.from(userProfiles.values()).reduce((sum, profile) => sum + profile.messageCount, 0),
    totalNeeds: Array.from(userProfiles.values()).reduce((sum, profile) => sum + profile.needs.length, 0),
    totalOffers: Array.from(userProfiles.values()).reduce((sum, profile) => sum + profile.offers.length, 0)
  };
}

/**
 * Nettoie les sessions inactives (à appeler périodiquement)
 */
export function cleanupInactiveSessions(maxAgeHours = 24) {
  const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);
  
  for (const [phone, session] of userSessions.entries()) {
    if (session.lastMessageAt < cutoffTime) {
      userSessions.delete(phone);
    }
  }
}

export default {
  getUserProfile,
  getConversationSession,
  updateUserProfile,
  addToConversationHistory,
  getConversationHistory,
  recordUserNeed,
  recordUserOffer,
  analyzeUserBehavior,
  getGlobalStats,
  cleanupInactiveSessions
};
