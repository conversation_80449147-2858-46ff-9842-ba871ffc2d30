/**
 * Interface Mobile Mesh Nowee
 * Écran de gestion du réseau maillé
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Switch,
  Animated,
  Dimensions
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import NoweeeMeshProtocol, { MeshNode, MeshMessage } from './MeshProtocol';

const { width, height } = Dimensions.get('window');

interface MeshStats {
  isConnected: boolean;
  nodeCount: number;
  messagesSent: number;
  messagesReceived: number;
  networkHealth: number;
  batteryOptimized: boolean;
}

const MeshScreen: React.FC = () => {
  const [meshEnabled, setMeshEnabled] = useState(false);
  const [meshStats, setMeshStats] = useState<MeshStats>({
    isConnected: false,
    nodeCount: 0,
    messagesSent: 0,
    messagesReceived: 0,
    networkHealth: 0,
    batteryOptimized: true
  });
  const [neighbors, setNeighbors] = useState<MeshNode[]>([]);
  const [emergencyMode, setEmergencyMode] = useState(false);
  const [offlineMessages, setOfflineMessages] = useState<MeshMessage[]>([]);
  
  const meshProtocol = useRef<NoweeeMeshProtocol | null>(null);
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const networkAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (meshEnabled) {
      initializeMesh();
      startPulseAnimation();
      startNetworkAnimation();
    } else {
      stopMesh();
    }
    
    return () => {
      if (meshProtocol.current) {
        meshProtocol.current.removeAllListeners();
      }
    };
  }, [meshEnabled]);

  const initializeMesh = async () => {
    try {
      const nodeId = `nowee-${Date.now()}`;
      meshProtocol.current = new NoweeeMeshProtocol(nodeId);
      
      // Écouter les événements mesh
      meshProtocol.current.on('helpRequest', handleHelpRequest);
      meshProtocol.current.on('helpOffer', handleHelpOffer);
      meshProtocol.current.on('transaction', handleTransaction);
      meshProtocol.current.on('messageSent', handleMessageSent);
      
      // Découvrir les voisins
      const discoveredNeighbors = await meshProtocol.current.discoverNeighbors();
      setNeighbors(discoveredNeighbors);
      
      // Mettre à jour les stats
      updateMeshStats();
      
      // Démarrer la mise à jour périodique
      const interval = setInterval(updateMeshStats, 5000);
      
      setMeshStats(prev => ({ ...prev, isConnected: true }));
      
    } catch (error) {
      console.error('Erreur initialisation mesh:', error);
      Alert.alert('Erreur', 'Impossible d\'initialiser le réseau mesh');
    }
  };

  const stopMesh = () => {
    if (meshProtocol.current) {
      meshProtocol.current.removeAllListeners();
      meshProtocol.current = null;
    }
    setMeshStats(prev => ({ ...prev, isConnected: false }));
    setNeighbors([]);
  };

  const updateMeshStats = () => {
    if (!meshProtocol.current) return;
    
    const stats = meshProtocol.current.getNetworkStats();
    const neighbors = meshProtocol.current.getNeighbors();
    
    setMeshStats(prev => ({
      ...prev,
      nodeCount: neighbors.length,
      networkHealth: calculateNetworkHealth(neighbors)
    }));
    
    setNeighbors(neighbors);
  };

  const calculateNetworkHealth = (neighbors: MeshNode[]): number => {
    if (neighbors.length === 0) return 0;
    
    const avgReputation = neighbors.reduce((sum, node) => sum + node.reputation, 0) / neighbors.length;
    const connectivityScore = Math.min(neighbors.length / 5, 1); // Max 5 voisins pour 100%
    
    return Math.round((avgReputation / 5 + connectivityScore) * 50);
  };

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const startNetworkAnimation = () => {
    Animated.loop(
      Animated.timing(networkAnim, {
        toValue: 1,
        duration: 3000,
        useNativeDriver: true,
      })
    ).start();
  };

  const handleHelpRequest = (request: any) => {
    setMeshStats(prev => ({ ...prev, messagesReceived: prev.messagesReceived + 1 }));
    
    Alert.alert(
      '🆘 Demande d\'aide reçue',
      `${request.title}\n\nUrgence: ${request.urgency}`,
      [
        { text: 'Ignorer', style: 'cancel' },
        { text: 'Aider', onPress: () => respondToHelp(request) }
      ]
    );
  };

  const handleHelpOffer = (offer: any) => {
    setMeshStats(prev => ({ ...prev, messagesReceived: prev.messagesReceived + 1 }));
  };

  const handleTransaction = (transaction: any) => {
    setMeshStats(prev => ({ ...prev, messagesReceived: prev.messagesReceived + 1 }));
  };

  const handleMessageSent = (event: any) => {
    if (event.success) {
      setMeshStats(prev => ({ ...prev, messagesSent: prev.messagesSent + 1 }));
    }
  };

  const respondToHelp = async (request: any) => {
    if (!meshProtocol.current) return;
    
    try {
      await meshProtocol.current.sendMessage({
        type: 'HELP_OFFER',
        source: meshProtocol.current.getNetworkStats().nodeId,
        destination: request.source,
        ttl: 5,
        timestamp: Date.now(),
        priority: 'medium',
        payload: {
          offerId: `offer-${Date.now()}`,
          needId: request.needId,
          helper: {
            phone: '+221701234567',
            name: 'Utilisateur Mesh',
            reputation: 4.5,
            skills: ['général']
          },
          availability: {
            startTime: Date.now(),
            endTime: Date.now() + 3600000, // 1 heure
            duration: 60
          },
          conditions: 'Disponible immédiatement'
        }
      });
      
      Alert.alert('✅', 'Votre offre d\'aide a été envoyée !');
    } catch (error) {
      Alert.alert('Erreur', 'Impossible d\'envoyer l\'offre d\'aide');
    }
  };

  const sendEmergencyAlert = async () => {
    if (!meshProtocol.current) {
      Alert.alert('Erreur', 'Réseau mesh non activé');
      return;
    }
    
    try {
      await meshProtocol.current.sendMessage({
        type: 'HELP_REQUEST',
        source: meshProtocol.current.getNetworkStats().nodeId,
        ttl: 10,
        timestamp: Date.now(),
        priority: 'emergency',
        payload: {
          needId: `emergency-${Date.now()}`,
          title: '🆘 URGENCE - Besoin d\'aide immédiate',
          description: 'Situation d\'urgence, aide requise',
          category: 'EMERGENCY',
          urgency: 'emergency',
          location: {
            latitude: 14.6928,
            longitude: -17.4467,
            accuracy: 10
          },
          requester: {
            phone: '+221701234567',
            name: 'Utilisateur Urgence',
            reputation: 4.5
          },
          reward: {
            noweeCoins: 100,
            timeCredits: 2
          }
        }
      });
      
      Alert.alert('🆘', 'Alerte d\'urgence diffusée dans le réseau mesh !');
      setEmergencyMode(true);
      
    } catch (error) {
      Alert.alert('Erreur', 'Impossible d\'envoyer l\'alerte d\'urgence');
    }
  };

  const toggleBatteryOptimization = () => {
    setMeshStats(prev => ({ 
      ...prev, 
      batteryOptimized: !prev.batteryOptimized 
    }));
  };

  const getStatusColor = () => {
    if (!meshEnabled) return '#666';
    if (meshStats.networkHealth > 70) return '#4CAF50';
    if (meshStats.networkHealth > 40) return '#FF9800';
    return '#F44336';
  };

  const getConnectionIcon = () => {
    if (!meshEnabled) return 'radio-outline';
    if (meshStats.networkHealth > 70) return 'radio';
    if (meshStats.networkHealth > 40) return 'radio-outline';
    return 'radio-button-off';
  };

  return (
    <LinearGradient
      colors={emergencyMode ? ['#FF5722', '#D32F2F'] : ['#667eea', '#764ba2']}
      style={styles.container}
    >
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>🕸️ Réseau Mesh Nowee</Text>
          <Text style={styles.subtitle}>
            Communication sans internet
          </Text>
        </View>

        {/* Status Card */}
        <View style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <Animated.View style={[styles.statusIcon, { transform: [{ scale: pulseAnim }] }]}>
              <Ionicons 
                name={getConnectionIcon()} 
                size={40} 
                color={getStatusColor()} 
              />
            </Animated.View>
            <View style={styles.statusInfo}>
              <Text style={styles.statusTitle}>
                {meshEnabled ? 'Réseau Actif' : 'Réseau Inactif'}
              </Text>
              <Text style={styles.statusSubtitle}>
                {meshStats.nodeCount} nœuds connectés
              </Text>
            </View>
            <Switch
              value={meshEnabled}
              onValueChange={setMeshEnabled}
              trackColor={{ false: '#767577', true: '#81b0ff' }}
              thumbColor={meshEnabled ? '#f5dd4b' : '#f4f3f4'}
            />
          </View>

          {meshEnabled && (
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{meshStats.networkHealth}%</Text>
                <Text style={styles.statLabel}>Santé Réseau</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{meshStats.messagesSent}</Text>
                <Text style={styles.statLabel}>Messages Envoyés</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{meshStats.messagesReceived}</Text>
                <Text style={styles.statLabel}>Messages Reçus</Text>
              </View>
            </View>
          )}
        </View>

        {/* Emergency Button */}
        {meshEnabled && (
          <TouchableOpacity 
            style={[styles.emergencyButton, emergencyMode && styles.emergencyActive]}
            onPress={sendEmergencyAlert}
          >
            <Ionicons name="warning" size={24} color="white" />
            <Text style={styles.emergencyText}>
              {emergencyMode ? 'Mode Urgence Actif' : 'Alerte d\'Urgence'}
            </Text>
          </TouchableOpacity>
        )}

        {/* Neighbors List */}
        {meshEnabled && neighbors.length > 0 && (
          <View style={styles.neighborsCard}>
            <Text style={styles.cardTitle}>🤝 Nœuds Voisins</Text>
            {neighbors.map((neighbor, index) => (
              <View key={neighbor.id} style={styles.neighborItem}>
                <View style={styles.neighborInfo}>
                  <Text style={styles.neighborName}>
                    Nœud {neighbor.id.slice(-6)}
                  </Text>
                  <Text style={styles.neighborDetails}>
                    ⭐ {neighbor.reputation.toFixed(1)} • {neighbor.capabilities.join(', ')}
                  </Text>
                </View>
                <View style={[styles.signalStrength, { opacity: neighbor.reputation / 5 }]}>
                  <Ionicons name="radio" size={20} color="#4CAF50" />
                </View>
              </View>
            ))}
          </View>
        )}

        {/* Settings */}
        {meshEnabled && (
          <View style={styles.settingsCard}>
            <Text style={styles.cardTitle}>⚙️ Paramètres Mesh</Text>
            
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Optimisation Batterie</Text>
                <Text style={styles.settingDescription}>
                  Réduit la consommation en mode mesh
                </Text>
              </View>
              <Switch
                value={meshStats.batteryOptimized}
                onValueChange={toggleBatteryOptimization}
                trackColor={{ false: '#767577', true: '#81b0ff' }}
                thumbColor={meshStats.batteryOptimized ? '#f5dd4b' : '#f4f3f4'}
              />
            </View>

            <TouchableOpacity style={styles.settingButton}>
              <Ionicons name="settings-outline" size={20} color="#667eea" />
              <Text style={styles.settingButtonText}>Paramètres Avancés</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Info Card */}
        <View style={styles.infoCard}>
          <Text style={styles.cardTitle}>ℹ️ À Propos du Mesh</Text>
          <Text style={styles.infoText}>
            Le réseau mesh Nowee permet de maintenir la communication et l'entraide 
            même sans connexion internet. Votre appareil devient un relais pour 
            renforcer la résilience de votre communauté.
          </Text>
          
          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <Ionicons name="shield-checkmark" size={16} color="#4CAF50" />
              <Text style={styles.featureText}>Communication sécurisée</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="battery-charging" size={16} color="#4CAF50" />
              <Text style={styles.featureText}>Optimisé pour la batterie</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="people" size={16} color="#4CAF50" />
              <Text style={styles.featureText}>Renforce la communauté</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.8)',
    textAlign: 'center',
  },
  statusCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  statusIcon: {
    marginRight: 15,
  },
  statusInfo: {
    flex: 1,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  statusSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#667eea',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  emergencyButton: {
    backgroundColor: '#FF5722',
    borderRadius: 15,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    shadowColor: '#FF5722',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  emergencyActive: {
    backgroundColor: '#D32F2F',
  },
  emergencyText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  neighborsCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  neighborItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  neighborInfo: {
    flex: 1,
  },
  neighborName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  neighborDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  signalStrength: {
    marginLeft: 10,
  },
  settingsCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  settingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
  },
  settingButtonText: {
    fontSize: 16,
    color: '#667eea',
    marginLeft: 10,
    fontWeight: '500',
  },
  infoCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    marginBottom: 40,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 15,
  },
  featuresList: {
    marginTop: 10,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
  },
});

export default MeshScreen;
