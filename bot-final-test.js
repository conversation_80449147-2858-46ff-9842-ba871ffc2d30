#!/usr/bin/env node

/**
 * <PERSON>t Nowee Final - Test Complet avec Économie
 */

import 'dotenv/config';
import express from 'express';
import bodyParser from 'body-parser';
import twilio from 'twilio';
import { dbService } from './src/services/databaseServiceUnified.js';
import { EconomyService } from './src/services/economyService.js';
import { BarterService } from './src/services/barterService.js';

const app = express();
const PORT = 3002; // Port différent pour éviter les conflits

// Middleware
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

// Route de santé
app.get('/health', async (req, res) => {
  try {
    const stats = await dbService.getGlobalStats();
    const economyStats = await EconomyService.getEconomyStats();
    
    res.json({
      status: 'OK',
      service: 'Nowee Bot Final',
      database: dbService.isUsingSupabase() ? 'Supabase' : 'Memory',
      stats: stats,
      economy: economyStats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      error: error.message
    });
  }
});

// Webhook WhatsApp avec toutes les commandes économiques
app.post('/webhook', async (req, res) => {
  try {
    const { Body: incomingMsg, From: userPhone } = req.body;
    
    console.log(`📱 Message reçu de ${userPhone}: ${incomingMsg}`);
    
    const twiml = new twilio.twiml.MessagingResponse();
    const command = incomingMsg.toLowerCase().trim();
    
    switch (command) {
      case '/aide':
      case '/help':
        twiml.message(`📚 *Commandes Nowee*

🔧 *Entraide:*
/aide - Affiche cette aide
/profil - Voir ton profil
/historique - Voir tes dernières demandes
/matches - Voir tes correspondances
/offrir - Proposer ton aide

🎤 *Vocal:*
/vocal - Guide des messages vocaux

💰 *Économie:*
/portefeuille - Voir ton portefeuille
/troc - Voir tes trocs en cours
/economie - Statistiques économiques

📊 *Communauté:*
/stats - Statistiques de la communauté
/effacer - Effacer ton historique
      
💬 Pour tout besoin, écris ou 🎤 enregistre ta demande !`);
        break;

      case '/portefeuille':
      case '/wallet':
        try {
          const user = await dbService.getUserProfile(userPhone);
          const wallet = await EconomyService.getWallet(user.id);
          
          twiml.message(`💰 *Votre Portefeuille Nowee*

🪙 *NoweeCoins:* ${wallet.nowee_coins.toFixed(2)}
⏰ *Crédits Temps:* ${wallet.time_credits.toFixed(1)}h

📊 *Statistiques:*
• Total gagné: ${wallet.total_earned.toFixed(2)} coins
• Total dépensé: ${wallet.total_spent.toFixed(2)} coins
• Bonus réputation: ${wallet.reputation_bonus.toFixed(2)} coins

💡 *Astuce:* Aidez d'autres personnes pour gagner plus de NoweeCoins !`);
        } catch (error) {
          console.error('Erreur portefeuille:', error);
          twiml.message("❌ Erreur lors de la récupération de votre portefeuille.");
        }
        break;

      case '/troc':
      case '/barter':
        try {
          const user = await dbService.getUserProfile(userPhone);
          const proposals = await BarterService.getUserBarterProposals(user.id);
          
          if (proposals.length === 0) {
            twiml.message(`🔄 *Système de Troc Nowee*

Aucune proposition de troc en cours.

💡 *Comment ça marche:*
• Échangez vos objets contre d'autres objets
• Échangez du temps contre des services
• Utilisez les NoweeCoins comme monnaie d'échange

📝 *Pour proposer un troc:*
Décrivez ce que vous offrez et ce que vous cherchez !`);
          } else {
            let trocText = `🔄 *Vos Trocs en Cours (${proposals.length})*\n\n`;
            
            proposals.slice(0, 5).forEach((proposal, index) => {
              const statusEmoji = {
                'PENDING': '⏳',
                'ACCEPTED': '✅',
                'REJECTED': '❌',
                'COMPLETED': '🎉',
                'COUNTER': '🔄'
              }[proposal.status] || '❓';
              
              const isProposer = proposal.proposer_id === user.id;
              const direction = isProposer ? 'Proposé à' : 'Reçu de';
              
              trocText += `${index + 1}. ${statusEmoji} ${proposal.exchange_type}\n`;
              trocText += `   ${direction} quelqu'un\n`;
              if (proposal.offered_coins > 0) {
                trocText += `   💰 ${proposal.offered_coins} coins offerts\n`;
              }
              if (proposal.requested_coins > 0) {
                trocText += `   💰 ${proposal.requested_coins} coins demandés\n`;
              }
              trocText += `\n`;
            });
            
            twiml.message(trocText);
          }
        } catch (error) {
          console.error('Erreur troc:', error);
          twiml.message("❌ Erreur lors de la récupération de vos trocs.");
        }
        break;

      case '/economie':
      case '/economy':
        try {
          const stats = await EconomyService.getEconomyStats();
          
          twiml.message(`📊 *Économie Nowee*

🪙 *Monnaie en Circulation:*
${stats.totalCoinsInCirculation.toFixed(0)} NoweeCoins

⏰ *Crédits Temps:*
${stats.totalTimeCredits.toFixed(1)} heures

📈 *Activité:*
• ${stats.totalTransactions} transactions
• ${stats.averageWalletBalance.toFixed(0)} coins/portefeuille en moyenne

💡 *Principe:* Plus vous aidez, plus vous gagnez !`);
        } catch (error) {
          console.error('Erreur économie:', error);
          twiml.message("❌ Erreur lors de la récupération des statistiques économiques.");
        }
        break;

      case '/profil':
        try {
          const user = await dbService.getUserProfile(userPhone);
          twiml.message(`👤 *Votre Profil*

📞 Téléphone: ${user.phone}
👤 Nom: ${user.name || 'Non défini'}
🏙️ Ville: ${user.city || 'Non définie'}
⭐ Note: ${user.rating || 0}/5
🤝 Aides données: ${user.help_given || 0}
🙏 Aides reçues: ${user.help_received || 0}`);
        } catch (error) {
          console.error('Erreur profil:', error);
          twiml.message("❌ Erreur lors de la récupération de votre profil.");
        }
        break;

      case '/stats':
        try {
          const stats = await dbService.getGlobalStats();
          twiml.message(`📊 *Statistiques Nowee*

👥 Utilisateurs: ${stats.totalUsers}
📋 Ressources: ${stats.totalResources || stats.totalNeeds + stats.totalOffers || 'N/A'}
🎯 Correspondances: ${stats.totalMatches}

🗄️ Mode: ${dbService.isUsingSupabase() ? 'Supabase' : 'Mémoire'}`);
        } catch (error) {
          console.error('Erreur stats:', error);
          twiml.message("❌ Erreur lors de la récupération des statistiques.");
        }
        break;

      default:
        // Traitement normal du message comme besoin
        try {
          const user = await dbService.getUserProfile(userPhone);
          
          // Créer un besoin
          const need = await dbService.recordUserNeed(userPhone, {
            type: 'GENERAL',
            description: incomingMsg,
            urgency: 1
          });
          
          // Chercher des correspondances
          const matches = await dbService.findMatches(need.id, 10);
          
          if (matches.length > 0) {
            let response = `🎯 *${matches.length} aide(s) trouvée(s) !*\n\n`;
            
            matches.slice(0, 3).forEach((match, index) => {
              response += `${index + 1}. ${match.title}\n`;
              if (match.distance_km) {
                response += `   📍 ${match.distance_km.toFixed(1)} km\n`;
              }
              response += `   ⭐ Score: ${match.match_score || 'N/A'}%\n\n`;
            });
            
            response += `💡 Quelqu'un peut vous aider ! Nous vous mettrons en contact bientôt.`;
            twiml.message(response);
          } else {
            twiml.message(`📝 *Demande enregistrée !*

"${incomingMsg}"

🔍 Nous cherchons quelqu'un pour vous aider...
📱 Vous serez notifié dès qu'une aide sera trouvée !

💰 En attendant, consultez votre /portefeuille pour voir vos NoweeCoins !`);
          }
        } catch (error) {
          console.error('Erreur traitement message:', error);
          twiml.message('❌ Une erreur est survenue. Réessayez plus tard.');
        }
        break;
    }
    
    res.type('text/xml').send(twiml.toString());
    
  } catch (error) {
    console.error('Erreur webhook:', error);
    const twiml = new twilio.twiml.MessagingResponse();
    twiml.message('❌ Une erreur est survenue. Réessayez plus tard.');
    res.type('text/xml').send(twiml.toString());
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Bot Nowee Final démarré sur le port ${PORT}`);
  console.log(`📊 Base de données: ${dbService.isUsingSupabase() ? 'Supabase' : 'Mémoire'}`);
  console.log(`🔗 Health: http://localhost:${PORT}/health`);
  console.log(`📱 Webhook: http://localhost:${PORT}/webhook`);
  console.log(`\n💰 Fonctionnalités économiques activées !`);
  console.log(`💡 Testez avec les commandes: /portefeuille, /troc, /economie`);
});
