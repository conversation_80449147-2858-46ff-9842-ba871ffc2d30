#!/usr/bin/env node

/**
 * Script de déploiement pour Nowee
 * Prépare et vérifie l'application avant le déploiement
 */

import 'dotenv/config';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.bold}${colors.blue}\n🚀 ${msg}${colors.reset}`)
};

async function checkEnvironment() {
  log.title('Vérification de l\'environnement de déploiement');
  
  const requiredVars = [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'TWILIO_ACCOUNT_SID',
    'TWILIO_AUTH_TOKEN'
  ];
  
  let allPresent = true;
  
  for (const varName of requiredVars) {
    if (process.env[varName]) {
      log.success(`${varName} configuré`);
    } else {
      log.error(`${varName} manquant`);
      allPresent = false;
    }
  }
  
  return allPresent;
}

async function checkFiles() {
  log.title('Vérification des fichiers requis');
  
  const requiredFiles = [
    'package.json',
    'Procfile',
    'railway.json',
    'src/bot/nowee-whatsapp-bot.js',
    'src/services/databaseServiceUnified.js',
    'src/services/economyService.js'
  ];
  
  let allPresent = true;
  
  for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
      log.success(`${file} présent`);
    } else {
      log.error(`${file} manquant`);
      allPresent = false;
    }
  }
  
  return allPresent;
}

async function runTests() {
  log.title('Exécution des tests de pré-déploiement');
  
  try {
    // Test de l'import des modules principaux
    log.info('Test d\'import des modules...');
    await import('./src/services/databaseServiceUnified.js');
    await import('./src/services/economyService.js');
    log.success('Tous les modules s\'importent correctement');
    
    // Test de la configuration Supabase
    log.info('Test de la configuration Supabase...');
    const { SupabaseService } = await import('./src/services/supabaseService.js');
    if (SupabaseService.isConfigured()) {
      log.success('Supabase configuré');
    } else {
      log.warning('Supabase non configuré - mode fallback activé');
    }
    
    return true;
  } catch (error) {
    log.error(`Erreur lors des tests: ${error.message}`);
    return false;
  }
}

async function generateDeploymentInfo() {
  log.title('Génération des informations de déploiement');
  
  const deployInfo = {
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    features: {
      whatsapp_bot: true,
      economy_system: true,
      barter_system: true,
      voice_messages: true,
      supabase_integration: true,
      fallback_mode: true
    },
    endpoints: [
      'POST /webhook - Webhook WhatsApp',
      'GET /health - Santé de l\'application',
      'GET /api/economy/stats - Statistiques économiques',
      'POST /api/economy/transfer - Transfert de coins',
      'GET /api/economy/wallet/:phone - Portefeuille utilisateur'
    ],
    commands: [
      '/aide - Aide générale',
      '/portefeuille - Voir son portefeuille',
      '/troc - Gérer ses trocs',
      '/economie - Statistiques économiques',
      '/profil - Voir son profil',
      '/stats - Statistiques communauté'
    ]
  };
  
  fs.writeFileSync('deployment-info.json', JSON.stringify(deployInfo, null, 2));
  log.success('Informations de déploiement générées');
  
  return deployInfo;
}

async function showDeploymentInstructions() {
  log.title('Instructions de déploiement');
  
  console.log(`
📋 ${colors.bold}INSTRUCTIONS DE DÉPLOIEMENT${colors.reset}

${colors.blue}🔧 Heroku:${colors.reset}
1. heroku create nowee-app
2. heroku config:set SUPABASE_URL=your_url
3. heroku config:set SUPABASE_ANON_KEY=your_key
4. heroku config:set TWILIO_ACCOUNT_SID=your_sid
5. heroku config:set TWILIO_AUTH_TOKEN=your_token
6. git push heroku main

${colors.blue}🚄 Railway:${colors.reset}
1. Connectez votre repo GitHub à Railway
2. Configurez les variables d'environnement
3. Railway déploiera automatiquement

${colors.blue}📱 Configuration WhatsApp:${colors.reset}
1. Webhook URL: https://your-app.herokuapp.com/webhook
2. Méthode: POST
3. Événements: messages

${colors.blue}🗄️ Supabase:${colors.reset}
1. Appliquez supabase-fix-timestamp.sql
2. Vérifiez que les tables existent
3. Configurez les politiques RLS

${colors.green}✅ L'application est prête pour le déploiement !${colors.reset}
`);
}

async function main() {
  try {
    log.title('Préparation du déploiement Nowee');
    
    // Vérifications
    const envOk = await checkEnvironment();
    const filesOk = await checkFiles();
    const testsOk = await runTests();
    
    if (!envOk || !filesOk || !testsOk) {
      log.error('Des problèmes ont été détectés. Corrigez-les avant de déployer.');
      process.exit(1);
    }
    
    // Génération des infos
    const deployInfo = await generateDeploymentInfo();
    
    // Instructions
    await showDeploymentInstructions();
    
    log.success('Préparation terminée avec succès !');
    log.info(`Version: ${deployInfo.version}`);
    log.info(`Fonctionnalités: ${Object.keys(deployInfo.features).length} activées`);
    
  } catch (error) {
    log.error(`Erreur: ${error.message}`);
    process.exit(1);
  }
}

main();
