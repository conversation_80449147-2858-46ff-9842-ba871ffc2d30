#!/usr/bin/env node

/**
 * Script de configuration de la base de données Nowee
 * Configure PostgreSQL et initialise le schéma
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import readline from 'readline';
import fs from 'fs/promises';

const execAsync = promisify(exec);

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.bold}${colors.blue}\n🗄️  ${msg}${colors.reset}`)
};

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (query) => new Promise((resolve) => rl.question(query, resolve));

// Vérifier si Docker est installé
async function checkDockerInstalled() {
  try {
    await execAsync('docker --version');
    return true;
  } catch (error) {
    return false;
  }
}

// Vérifier si PostgreSQL est déjà en cours d'exécution
async function checkPostgresRunning() {
  try {
    await execAsync('docker ps --filter "name=nowee-postgres" --format "{{.Names}}"');
    return true;
  } catch (error) {
    return false;
  }
}

// Démarrer PostgreSQL avec Docker
async function startPostgresDocker() {
  log.title('Configuration de PostgreSQL avec Docker');
  
  try {
    log.info('Démarrage du conteneur PostgreSQL...');
    
    const command = `docker run --name nowee-postgres \
      -e POSTGRES_PASSWORD=nowee123 \
      -e POSTGRES_DB=nowee \
      -e POSTGRES_USER=postgres \
      -p 5432:5432 \
      -d postgres:15`;
    
    await execAsync(command);
    log.success('PostgreSQL démarré avec succès !');
    
    // Attendre que PostgreSQL soit prêt
    log.info('Attente que PostgreSQL soit prêt...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    return true;
  } catch (error) {
    if (error.message.includes('already in use')) {
      log.warning('Le conteneur nowee-postgres existe déjà. Tentative de démarrage...');
      try {
        await execAsync('docker start nowee-postgres');
        log.success('Conteneur PostgreSQL redémarré !');
        return true;
      } catch (startError) {
        log.error('Impossible de démarrer le conteneur existant');
        return false;
      }
    } else {
      log.error(`Erreur lors du démarrage de PostgreSQL: ${error.message}`);
      return false;
    }
  }
}

// Configuration manuelle de la base de données
async function manualDatabaseSetup() {
  log.title('Configuration manuelle de la base de données');
  
  console.log('Vous pouvez utiliser:');
  console.log('1. 🐘 PostgreSQL local');
  console.log('2. ☁️  Service cloud (Supabase, Railway, Heroku)');
  console.log('3. 🐳 Docker (recommandé pour le développement)');
  
  const choice = await question('\nQuelle option préférez-vous ? (1/2/3): ');
  
  switch(choice) {
    case '1':
      console.log('\n📝 Pour PostgreSQL local:');
      console.log('1. Installez PostgreSQL sur votre système');
      console.log('2. Créez une base de données "nowee"');
      console.log('3. Configurez DATABASE_URL dans .env');
      console.log('   Exemple: postgresql://username:password@localhost:5432/nowee');
      break;
      
    case '2':
      console.log('\n☁️  Services cloud recommandés:');
      console.log('• Supabase: https://supabase.com (gratuit)');
      console.log('• Railway: https://railway.app (gratuit)');
      console.log('• Heroku Postgres: https://heroku.com');
      console.log('\nCopiez l\'URL de connexion dans votre fichier .env');
      break;
      
    case '3':
      if (await checkDockerInstalled()) {
        return await startPostgresDocker();
      } else {
        log.error('Docker n\'est pas installé. Installez Docker Desktop d\'abord.');
        console.log('Téléchargez Docker: https://www.docker.com/products/docker-desktop');
        return false;
      }
      
    default:
      log.warning('Option non reconnue');
      return false;
  }
  
  return true;
}

// Vérifier la connexion à la base de données
async function testDatabaseConnection() {
  log.title('Test de connexion à la base de données');
  
  try {
    await execAsync('npx prisma db push --accept-data-loss');
    log.success('Connexion à la base de données réussie !');
    return true;
  } catch (error) {
    log.error('Impossible de se connecter à la base de données');
    console.log('Vérifiez votre DATABASE_URL dans le fichier .env');
    return false;
  }
}

// Générer le client Prisma
async function generatePrismaClient() {
  log.title('Génération du client Prisma');
  
  try {
    await execAsync('npx prisma generate');
    log.success('Client Prisma généré avec succès !');
    return true;
  } catch (error) {
    log.error(`Erreur lors de la génération du client: ${error.message}`);
    return false;
  }
}

// Initialiser la base de données avec des données de test
async function seedDatabase() {
  log.title('Initialisation de la base de données');
  
  const shouldSeed = await question('Voulez-vous initialiser la base avec des données de test ? (o/n): ');
  
  if (shouldSeed.toLowerCase() === 'o') {
    try {
      await execAsync('npm run db:seed');
      log.success('Base de données initialisée avec des données de test !');
      return true;
    } catch (error) {
      log.error(`Erreur lors de l'initialisation: ${error.message}`);
      return false;
    }
  } else {
    log.info('Base de données créée sans données de test');
    return true;
  }
}

// Vérifier si le fichier .env existe
async function checkEnvFile() {
  try {
    await fs.access('.env');
    return true;
  } catch {
    return false;
  }
}

// Fonction principale
async function main() {
  console.log(`${colors.bold}${colors.blue}`);
  console.log('🗄️  Configuration de la base de données Nowee');
  console.log('===========================================');
  console.log(colors.reset);
  
  // Vérifier le fichier .env
  if (!await checkEnvFile()) {
    log.error('Fichier .env non trouvé !');
    console.log('Exécutez d\'abord: npm run setup');
    process.exit(1);
  }
  
  // Étape 1: Configuration de PostgreSQL
  const dockerInstalled = await checkDockerInstalled();
  
  if (dockerInstalled) {
    const useDocker = await question('Voulez-vous utiliser Docker pour PostgreSQL ? (o/n): ');
    
    if (useDocker.toLowerCase() === 'o') {
      const success = await startPostgresDocker();
      if (!success) {
        log.error('Impossible de configurer PostgreSQL avec Docker');
        process.exit(1);
      }
    } else {
      await manualDatabaseSetup();
    }
  } else {
    log.warning('Docker non détecté. Configuration manuelle requise.');
    await manualDatabaseSetup();
  }
  
  // Étape 2: Test de connexion
  if (!await testDatabaseConnection()) {
    log.error('Configuration de la base de données échouée');
    process.exit(1);
  }
  
  // Étape 3: Génération du client Prisma
  if (!await generatePrismaClient()) {
    log.error('Génération du client Prisma échouée');
    process.exit(1);
  }
  
  // Étape 4: Initialisation avec des données de test
  await seedDatabase();
  
  // Résumé final
  console.log(`${colors.bold}\n🎉 Configuration terminée avec succès !${colors.reset}`);
  console.log('\nCommandes utiles:');
  console.log('• npm run db:studio    - Interface graphique Prisma');
  console.log('• npm run db:seed      - Réinitialiser avec des données de test');
  console.log('• npm start            - Démarrer le bot Nowee');
  
  rl.close();
}

// Gestion des erreurs
process.on('unhandledRejection', (error) => {
  log.error(`Erreur non gérée: ${error.message}`);
  process.exit(1);
});

// Exécuter le script
main().catch(error => {
  log.error(`Erreur: ${error.message}`);
  process.exit(1);
});
