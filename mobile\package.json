{"name": "nowee-mobile", "version": "1.0.0", "description": "Application mobile Nowee - Entraide locale en temps réel", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace NoweeApp.xcworkspace -scheme NoweeApp -configuration Release archive", "clean": "react-native clean-project-auto"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "react-native-vector-icons": "^10.0.2", "react-native-maps": "^1.8.0", "react-native-geolocation-service": "^5.3.1", "react-native-permissions": "^3.10.1", "react-native-push-notification": "^8.1.1", "@react-native-async-storage/async-storage": "^1.19.5", "react-native-keychain": "^8.1.3", "react-native-image-picker": "^7.0.3", "@react-native-voice/voice": "^3.2.4", "react-native-tts": "^4.1.0", "react-native-sound": "^0.11.2", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "react-native-modal": "^13.0.1", "react-native-animatable": "^1.3.3", "react-native-linear-gradient": "^2.8.3", "react-native-svg": "^13.14.0", "react-native-qrcode-scanner": "^1.5.5", "react-native-share": "^10.0.2", "react-native-rate": "^1.2.12"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}