/**
 * Service Supabase pour Nowee
 * Gère toutes les interactions avec la base de données Supabase
 */

import { createClient } from '@supabase/supabase-js';

// Configuration Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.warn('⚠️ Configuration Supabase manquante, utilisation du mode fallback');
}

// Client Supabase
const supabase = supabaseUrl && supabaseKey ? 
  createClient(supabaseUrl, supabaseKey) : null;

/**
 * Service principal Supabase
 */
export class SupabaseService {
  
  /**
   * Vérifie si Supabase est configuré
   */
  static isConfigured() {
    return supabase !== null;
  }
  
  /**
   * Obtient ou crée un utilisateur
   */
  static async getOrCreateUser(phone, userData = {}) {
    if (!supabase) throw new Error('Supabase non configuré');
    
    try {
      // Chercher l'utilisateur existant
      let { data: user, error } = await supabase
        .from('users')
        .select('*')
        .eq('phone', phone)
        .single();
      
      if (error && error.code !== 'PGRST116') { // PGRST116 = not found
        throw error;
      }
      
      if (!user) {
        // Créer un nouvel utilisateur
        const { data: newUser, error: createError } = await supabase
          .from('users')
          .insert([{
            phone,
            name: userData.name || null,
            location: userData.location || null,
            latitude: userData.coordinates?.latitude || null,
            longitude: userData.coordinates?.longitude || null,
            city: userData.city || null,
            country: userData.country || 'Sénégal',
            preferences: userData.preferences || {},
            languages: userData.languages || ['fr']
          }])
          .select()
          .single();
        
        if (createError) throw createError;
        user = newUser;
      } else {
        // Mettre à jour last_active_at
        await supabase
          .from('users')
          .update({ last_active_at: new Date().toISOString() })
          .eq('id', user.id);
      }
      
      return user;
      
    } catch (error) {
      console.error('Erreur getOrCreateUser:', error);
      throw error;
    }
  }
  
  /**
   * Crée une ressource (besoin ou offre)
   */
  static async createResource(userId, resourceData) {
    if (!supabase) throw new Error('Supabase non configuré');
    
    try {
      const { data, error } = await supabase
        .from('resources')
        .insert([{
          user_id: userId,
          type: resourceData.type, // 'NEED' ou 'OFFER'
          category: resourceData.category || 'GENERAL',
          title: resourceData.title,
          description: resourceData.description,
          tags: resourceData.tags || [],
          location: resourceData.location || null,
          latitude: resourceData.coordinates?.latitude || null,
          longitude: resourceData.coordinates?.longitude || null,
          radius_km: resourceData.radius_km || 5,
          urgency: resourceData.urgency || 1,
          price_range: resourceData.price_range || null,
          availability: resourceData.availability || null,
          conditions: resourceData.conditions || null,
          expires_at: resourceData.expires_at || null
        }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
      
    } catch (error) {
      console.error('Erreur createResource:', error);
      throw error;
    }
  }
  
  /**
   * Trouve des correspondances pour une ressource
   */
  static async findMatches(resourceId, maxDistance = 10, limit = 10) {
    if (!supabase) throw new Error('Supabase non configuré');
    
    try {
      // Récupérer la ressource source
      const { data: sourceResource, error: sourceError } = await supabase
        .from('resources')
        .select('*')
        .eq('id', resourceId)
        .single();
      
      if (sourceError) throw sourceError;
      
      // Déterminer le type de ressource opposé
      const targetType = sourceResource.type === 'NEED' ? 'OFFER' : 'NEED';
      
      // Construire la requête de matching
      let query = supabase
        .from('resources')
        .select(`
          *,
          users!inner(id, name, rating, help_given)
        `)
        .eq('type', targetType)
        .eq('status', 'ACTIVE')
        .eq('category', sourceResource.category)
        .neq('user_id', sourceResource.user_id);
      
      // Ajouter filtre de distance si coordonnées disponibles
      if (sourceResource.latitude && sourceResource.longitude) {
        query = query.not('latitude', 'is', null).not('longitude', 'is', null);
      }
      
      const { data: potentialMatches, error } = await query
        .limit(limit * 2); // Récupérer plus pour filtrer ensuite
      
      if (error) throw error;
      
      // Calculer les scores de matching
      const matches = potentialMatches
        .map(match => ({
          ...match,
          match_score: this.calculateMatchScore(sourceResource, match),
          distance_km: sourceResource.latitude && sourceResource.longitude &&
                      match.latitude && match.longitude ?
            this.calculateDistance(
              { lat: sourceResource.latitude, lon: sourceResource.longitude },
              { lat: match.latitude, lon: match.longitude }
            ) : null
        }))
        .filter(match => !match.distance_km || match.distance_km <= maxDistance)
        .sort((a, b) => b.match_score - a.match_score)
        .slice(0, limit);
      
      return matches;
      
    } catch (error) {
      console.error('Erreur findMatches:', error);
      throw error;
    }
  }
  
  /**
   * Crée une correspondance
   */
  static async createMatch(needId, offerId, matchScore, matchFactors = {}) {
    if (!supabase) throw new Error('Supabase non configuré');
    
    try {
      // Récupérer les ressources pour obtenir les user_ids
      const { data: resources, error: resourceError } = await supabase
        .from('resources')
        .select('id, user_id, type')
        .in('id', [needId, offerId]);
      
      if (resourceError) throw resourceError;
      
      const need = resources.find(r => r.type === 'NEED');
      const offer = resources.find(r => r.type === 'OFFER');
      
      if (!need || !offer) {
        throw new Error('Ressources invalides pour le matching');
      }
      
      const { data, error } = await supabase
        .from('matches')
        .insert([{
          need_id: needId,
          offer_id: offerId,
          requester_id: need.user_id,
          provider_id: offer.user_id,
          match_score: matchScore,
          match_factors: matchFactors
        }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
      
    } catch (error) {
      console.error('Erreur createMatch:', error);
      throw error;
    }
  }
  
  /**
   * Met à jour le statut d'une correspondance
   */
  static async updateMatchStatus(matchId, status, userId) {
    if (!supabase) throw new Error('Supabase non configuré');
    
    try {
      const { data, error } = await supabase
        .from('matches')
        .update({ 
          status,
          updated_at: new Date().toISOString(),
          ...(status === 'COMPLETED' && { completed_at: new Date().toISOString() })
        })
        .eq('id', matchId)
        .or(`requester_id.eq.${userId},provider_id.eq.${userId}`)
        .select()
        .single();
      
      if (error) throw error;
      return data;
      
    } catch (error) {
      console.error('Erreur updateMatchStatus:', error);
      throw error;
    }
  }
  
  /**
   * Recherche de ressources par texte et localisation
   */
  static async searchResources(query, coordinates = null, radius = 10, type = null) {
    if (!supabase) throw new Error('Supabase non configuré');
    
    try {
      let supabaseQuery = supabase
        .from('resources')
        .select(`
          *,
          users!inner(id, name, rating, help_given)
        `)
        .eq('status', 'ACTIVE');
      
      // Filtre par type si spécifié
      if (type) {
        supabaseQuery = supabaseQuery.eq('type', type);
      }
      
      // Recherche textuelle
      if (query) {
        supabaseQuery = supabaseQuery.textSearch('search_vector', query);
      }
      
      const { data, error } = await supabaseQuery
        .order('created_at', { ascending: false })
        .limit(50);
      
      if (error) throw error;
      
      // Filtrer par distance si coordonnées fournies
      if (coordinates) {
        return data
          .map(resource => ({
            ...resource,
            distance_km: resource.coordinates ?
              this.calculateDistance(coordinates, resource.coordinates) : null
          }))
          .filter(resource => !resource.distance_km || resource.distance_km <= radius)
          .sort((a, b) => (a.distance_km || 999) - (b.distance_km || 999));
      }
      
      return data;
      
    } catch (error) {
      console.error('Erreur searchResources:', error);
      throw error;
    }
  }
  
  /**
   * Obtient les statistiques globales
   */
  static async getGlobalStats() {
    if (!supabase) throw new Error('Supabase non configuré');
    
    try {
      const [usersResult, resourcesResult, matchesResult] = await Promise.all([
        supabase.from('users').select('id', { count: 'exact', head: true }),
        supabase.from('resources').select('id', { count: 'exact', head: true }),
        supabase.from('matches').select('id', { count: 'exact', head: true })
      ]);
      
      return {
        totalUsers: usersResult.count || 0,
        totalResources: resourcesResult.count || 0,
        totalMatches: matchesResult.count || 0,
        activeUsers: usersResult.count || 0 // Simplification
      };
      
    } catch (error) {
      console.error('Erreur getGlobalStats:', error);
      return { totalUsers: 0, totalResources: 0, totalMatches: 0, activeUsers: 0 };
    }
  }
  
  /**
   * Calcule le score de matching entre deux ressources
   */
  static calculateMatchScore(source, target) {
    let score = 0;
    const factors = {};
    
    // Score de base pour la catégorie
    if (source.category === target.category) {
      score += 40;
      factors.category = 40;
    }
    
    // Score pour les tags communs
    const commonTags = source.tags?.filter(tag => 
      target.tags?.includes(tag)
    ) || [];
    const tagScore = Math.min(commonTags.length * 10, 30);
    score += tagScore;
    factors.tags = tagScore;
    
    // Score pour l'urgence (plus urgent = priorité)
    if (source.urgency >= 3 || target.urgency >= 3) {
      score += 15;
      factors.urgency = 15;
    }
    
    // Score pour la réputation de l'utilisateur
    const userRating = target.users?.rating || 0;
    const reputationScore = Math.min(userRating * 3, 15);
    score += reputationScore;
    factors.reputation = reputationScore;
    
    return Math.min(score, 100);
  }
  
  /**
   * Calcule la distance entre deux points (approximation simple)
   */
  static calculateDistance(point1, point2) {
    // Implémentation simplifiée - en production, utiliser PostGIS
    const R = 6371; // Rayon de la Terre en km
    const dLat = (point2.lat - point1.lat) * Math.PI / 180;
    const dLon = (point2.lon - point1.lon) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }
  
  /**
   * Obtient le client Supabase
   */
  static getClient() {
    return supabase;
  }
}

export default SupabaseService;
