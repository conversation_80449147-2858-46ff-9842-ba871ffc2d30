<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🕸️ Démonstration Mesh Networking Nowee</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: white;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .network-visualization {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .controls-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .network-canvas {
            width: 100%;
            height: 400px;
            background: rgba(0,0,0,0.2);
            border-radius: 15px;
            position: relative;
            overflow: hidden;
        }
        
        .node {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border: 3px solid white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        .node:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.4);
        }
        
        .node.active {
            background: linear-gradient(135deg, #FF9800, #F57C00);
            animation: pulse 2s infinite;
        }
        
        .node.offline {
            background: linear-gradient(135deg, #F44336, #D32F2F);
            opacity: 0.6;
        }
        
        .node.emergency {
            background: linear-gradient(135deg, #FF5722, #D32F2F);
            animation: emergency-pulse 1s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        @keyframes emergency-pulse {
            0%, 100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.7); }
            50% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(255, 87, 34, 0); }
        }
        
        .connection {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, #4CAF50, #2196F3);
            transform-origin: left center;
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        
        .connection.active {
            height: 4px;
            opacity: 1;
            animation: data-flow 2s infinite;
        }
        
        @keyframes data-flow {
            0% { background-position: 0% 50%; }
            100% { background-position: 100% 50%; }
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group h3 {
            margin-bottom: 10px;
            color: white;
        }
        
        .button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        .button.emergency {
            background: linear-gradient(135deg, #FF5722, #D32F2F);
        }
        
        .button.secondary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }
        
        .stats-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(10px);
            margin-top: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .message-log {
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
            margin-top: 15px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 5px;
        }
        
        .log-entry.info {
            background: rgba(33, 150, 243, 0.2);
        }
        
        .log-entry.success {
            background: rgba(76, 175, 80, 0.2);
        }
        
        .log-entry.warning {
            background: rgba(255, 152, 0, 0.2);
        }
        
        .log-entry.error {
            background: rgba(244, 67, 54, 0.2);
        }
        
        .scenario-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕸️ Démonstration Mesh Networking Nowee</h1>
            <p>Réseau d'entraide résilient sans internet</p>
        </div>
        
        <div class="demo-grid">
            <!-- Visualisation du réseau -->
            <div class="network-visualization">
                <h3>🌐 Réseau Maillé en Temps Réel</h3>
                <div class="network-canvas" id="networkCanvas">
                    <!-- Les nœuds seront générés dynamiquement -->
                </div>
                <div class="message-log" id="messageLog">
                    <div class="log-entry info">🚀 Réseau mesh initialisé</div>
                    <div class="log-entry success">✅ 5 nœuds connectés</div>
                    <div class="log-entry info">📡 Découverte de voisins en cours...</div>
                </div>
            </div>
            
            <!-- Panneau de contrôle -->
            <div class="controls-panel">
                <h3>🎮 Contrôles de Démonstration</h3>
                
                <div class="control-group">
                    <h4>📱 Scénarios d'Usage</h4>
                    <div class="scenario-buttons">
                        <button class="button" onclick="simulateHelpRequest()">🆘 Demande d'Aide</button>
                        <button class="button emergency" onclick="simulateEmergency()">🚨 Urgence</button>
                        <button class="button secondary" onclick="simulateTransaction()">💰 Transaction</button>
                        <button class="button secondary" onclick="simulateBarter()">🔄 Troc</button>
                    </div>
                </div>
                
                <div class="control-group">
                    <h4>🔧 Tests Réseau</h4>
                    <div class="scenario-buttons">
                        <button class="button" onclick="testConnectivity()">🔗 Test Connectivité</button>
                        <button class="button" onclick="testPerformance()">⚡ Test Performance</button>
                        <button class="button" onclick="simulateNodeFailure()">💥 Panne Nœud</button>
                        <button class="button" onclick="testRecovery()">🔄 Récupération</button>
                    </div>
                </div>
                
                <div class="control-group">
                    <h4>⚙️ Configuration</h4>
                    <div class="scenario-buttons">
                        <button class="button secondary" onclick="addNode()">➕ Ajouter Nœud</button>
                        <button class="button secondary" onclick="removeNode()">➖ Retirer Nœud</button>
                        <button class="button" onclick="resetNetwork()">🔄 Reset Réseau</button>
                        <button class="button" onclick="exportLogs()">📄 Export Logs</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statistiques -->
        <div class="stats-panel">
            <h3>📊 Statistiques du Réseau</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="nodeCount">5</div>
                    <div class="stat-label">Nœuds Actifs</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="messageCount">0</div>
                    <div class="stat-label">Messages Envoyés</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="latency">45ms</div>
                    <div class="stat-label">Latence Moyenne</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="throughput">12.5</div>
                    <div class="stat-label">Messages/sec</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="reliability">98%</div>
                    <div class="stat-label">Fiabilité</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="coverage">100%</div>
                    <div class="stat-label">Couverture</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // État du réseau
        let networkState = {
            nodes: [],
            connections: [],
            messageCount: 0,
            activeNodes: 5,
            isEmergencyMode: false
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initializeNetwork();
            startNetworkAnimation();
            updateStats();
        });

        function initializeNetwork() {
            const canvas = document.getElementById('networkCanvas');
            const canvasRect = canvas.getBoundingClientRect();
            
            // Créer 5 nœuds initiaux
            for (let i = 0; i < 5; i++) {
                createNode(i, canvasRect);
            }
            
            // Créer les connexions
            createConnections();
            
            logMessage('success', '✅ Réseau mesh initialisé avec 5 nœuds');
        }

        function createNode(id, canvasRect) {
            const node = document.createElement('div');
            node.className = 'node';
            node.id = `node-${id}`;
            node.textContent = `N${id}`;
            
            // Position aléatoire mais espacée
            const angle = (id / 5) * 2 * Math.PI;
            const radius = 120;
            const centerX = canvasRect.width / 2;
            const centerY = canvasRect.height / 2;
            
            const x = centerX + Math.cos(angle) * radius - 30;
            const y = centerY + Math.sin(angle) * radius - 30;
            
            node.style.left = `${x}px`;
            node.style.top = `${y}px`;
            
            node.onclick = () => selectNode(id);
            
            document.getElementById('networkCanvas').appendChild(node);
            
            networkState.nodes.push({
                id: id,
                element: node,
                x: x + 30,
                y: y + 30,
                active: true,
                reputation: 4.0 + Math.random()
            });
        }

        function createConnections() {
            const canvas = document.getElementById('networkCanvas');
            
            // Créer des connexions entre nœuds proches
            for (let i = 0; i < networkState.nodes.length; i++) {
                for (let j = i + 1; j < networkState.nodes.length; j++) {
                    const node1 = networkState.nodes[i];
                    const node2 = networkState.nodes[j];
                    
                    const distance = Math.sqrt(
                        Math.pow(node2.x - node1.x, 2) + 
                        Math.pow(node2.y - node1.y, 2)
                    );
                    
                    // Connecter si distance < 200px
                    if (distance < 200) {
                        createConnection(node1, node2);
                    }
                }
            }
        }

        function createConnection(node1, node2) {
            const connection = document.createElement('div');
            connection.className = 'connection';
            
            const dx = node2.x - node1.x;
            const dy = node2.y - node1.y;
            const length = Math.sqrt(dx * dx + dy * dy);
            const angle = Math.atan2(dy, dx) * 180 / Math.PI;
            
            connection.style.left = `${node1.x}px`;
            connection.style.top = `${node1.y}px`;
            connection.style.width = `${length}px`;
            connection.style.transform = `rotate(${angle}deg)`;
            
            document.getElementById('networkCanvas').appendChild(connection);
            
            networkState.connections.push({
                element: connection,
                node1: node1,
                node2: node2,
                active: false
            });
        }

        function selectNode(nodeId) {
            const node = networkState.nodes.find(n => n.id === nodeId);
            if (node) {
                node.element.classList.toggle('active');
                logMessage('info', `📱 Nœud N${nodeId} sélectionné`);
            }
        }

        function simulateHelpRequest() {
            const sourceNode = getRandomActiveNode();
            const targetNode = getRandomActiveNode();
            
            if (sourceNode && targetNode && sourceNode !== targetNode) {
                animateMessage(sourceNode, targetNode, '🆘');
                logMessage('info', `🆘 Demande d'aide: N${sourceNode.id} → N${targetNode.id}`);
                networkState.messageCount++;
                updateStats();
            }
        }

        function simulateEmergency() {
            networkState.isEmergencyMode = true;
            
            // Activer le mode urgence sur tous les nœuds
            networkState.nodes.forEach(node => {
                if (node.active) {
                    node.element.classList.add('emergency');
                }
            });
            
            // Diffuser l'alerte d'urgence
            const sourceNode = getRandomActiveNode();
            if (sourceNode) {
                networkState.nodes.forEach(node => {
                    if (node !== sourceNode && node.active) {
                        animateMessage(sourceNode, node, '🚨');
                    }
                });
                
                logMessage('warning', `🚨 ALERTE D'URGENCE diffusée depuis N${sourceNode.id}`);
                networkState.messageCount += networkState.nodes.length - 1;
            }
            
            // Désactiver le mode urgence après 5 secondes
            setTimeout(() => {
                networkState.isEmergencyMode = false;
                networkState.nodes.forEach(node => {
                    node.element.classList.remove('emergency');
                });
                logMessage('success', '✅ Mode urgence désactivé');
            }, 5000);
            
            updateStats();
        }

        function simulateTransaction() {
            const sourceNode = getRandomActiveNode();
            const targetNode = getRandomActiveNode();
            
            if (sourceNode && targetNode && sourceNode !== targetNode) {
                animateMessage(sourceNode, targetNode, '💰');
                logMessage('success', `💰 Transaction: N${sourceNode.id} → N${targetNode.id} (25 NoweeCoins)`);
                networkState.messageCount++;
                updateStats();
            }
        }

        function simulateBarter() {
            const sourceNode = getRandomActiveNode();
            const targetNode = getRandomActiveNode();
            
            if (sourceNode && targetNode && sourceNode !== targetNode) {
                animateMessage(sourceNode, targetNode, '🔄');
                logMessage('info', `🔄 Proposition de troc: N${sourceNode.id} ↔ N${targetNode.id}`);
                networkState.messageCount++;
                updateStats();
            }
        }

        function animateMessage(sourceNode, targetNode, emoji) {
            // Activer la connexion
            const connection = networkState.connections.find(c => 
                (c.node1 === sourceNode && c.node2 === targetNode) ||
                (c.node1 === targetNode && c.node2 === sourceNode)
            );
            
            if (connection) {
                connection.element.classList.add('active');
                setTimeout(() => {
                    connection.element.classList.remove('active');
                }, 2000);
            }
            
            // Animer les nœuds
            sourceNode.element.classList.add('active');
            setTimeout(() => {
                targetNode.element.classList.add('active');
                setTimeout(() => {
                    sourceNode.element.classList.remove('active');
                    targetNode.element.classList.remove('active');
                }, 1000);
            }, 500);
        }

        function getRandomActiveNode() {
            const activeNodes = networkState.nodes.filter(n => n.active);
            return activeNodes[Math.floor(Math.random() * activeNodes.length)];
        }

        function testConnectivity() {
            logMessage('info', '🔗 Test de connectivité en cours...');
            
            let testsCompleted = 0;
            const totalTests = 10;
            
            const testInterval = setInterval(() => {
                const sourceNode = getRandomActiveNode();
                const targetNode = getRandomActiveNode();
                
                if (sourceNode && targetNode && sourceNode !== targetNode) {
                    animateMessage(sourceNode, targetNode, '📡');
                    testsCompleted++;
                    
                    if (testsCompleted >= totalTests) {
                        clearInterval(testInterval);
                        logMessage('success', `✅ Test connectivité terminé: ${totalTests}/10 réussis`);
                        updateStats();
                    }
                }
            }, 300);
        }

        function testPerformance() {
            logMessage('info', '⚡ Test de performance en cours...');
            
            const startTime = Date.now();
            let messagesProcessed = 0;
            const targetMessages = 20;
            
            const performanceInterval = setInterval(() => {
                const sourceNode = getRandomActiveNode();
                const targetNode = getRandomActiveNode();
                
                if (sourceNode && targetNode && sourceNode !== targetNode) {
                    animateMessage(sourceNode, targetNode, '⚡');
                    messagesProcessed++;
                    networkState.messageCount++;
                    
                    if (messagesProcessed >= targetMessages) {
                        clearInterval(performanceInterval);
                        const duration = Date.now() - startTime;
                        const throughput = (messagesProcessed / duration * 1000).toFixed(1);
                        
                        logMessage('success', `✅ Performance: ${messagesProcessed} messages en ${duration}ms (${throughput} msg/s)`);
                        updateStats();
                    }
                }
            }, 100);
        }

        function simulateNodeFailure() {
            const activeNodes = networkState.nodes.filter(n => n.active);
            if (activeNodes.length > 1) {
                const failedNode = activeNodes[Math.floor(Math.random() * activeNodes.length)];
                failedNode.active = false;
                failedNode.element.classList.add('offline');
                
                networkState.activeNodes--;
                logMessage('error', `💥 Nœud N${failedNode.id} hors ligne`);
                updateStats();
                
                // Récupération automatique après 5 secondes
                setTimeout(() => {
                    failedNode.active = true;
                    failedNode.element.classList.remove('offline');
                    networkState.activeNodes++;
                    logMessage('success', `🔄 Nœud N${failedNode.id} récupéré`);
                    updateStats();
                }, 5000);
            }
        }

        function testRecovery() {
            logMessage('info', '🔄 Test de récupération réseau...');
            
            // Simuler plusieurs pannes simultanées
            const activeNodes = networkState.nodes.filter(n => n.active);
            const failureCount = Math.min(2, activeNodes.length - 1);
            
            for (let i = 0; i < failureCount; i++) {
                setTimeout(() => {
                    simulateNodeFailure();
                }, i * 1000);
            }
            
            setTimeout(() => {
                logMessage('success', '✅ Test de récupération terminé');
            }, 6000);
        }

        function addNode() {
            const newId = networkState.nodes.length;
            const canvas = document.getElementById('networkCanvas');
            const canvasRect = canvas.getBoundingClientRect();
            
            createNode(newId, canvasRect);
            createConnections();
            
            networkState.activeNodes++;
            logMessage('success', `➕ Nœud N${newId} ajouté au réseau`);
            updateStats();
        }

        function removeNode() {
            if (networkState.activeNodes > 1) {
                const lastNode = networkState.nodes[networkState.nodes.length - 1];
                lastNode.element.remove();
                
                // Supprimer les connexions associées
                networkState.connections = networkState.connections.filter(c => {
                    if (c.node1 === lastNode || c.node2 === lastNode) {
                        c.element.remove();
                        return false;
                    }
                    return true;
                });
                
                networkState.nodes.pop();
                networkState.activeNodes--;
                
                logMessage('warning', `➖ Nœud retiré du réseau`);
                updateStats();
            }
        }

        function resetNetwork() {
            // Supprimer tous les nœuds et connexions
            document.getElementById('networkCanvas').innerHTML = '';
            networkState = {
                nodes: [],
                connections: [],
                messageCount: 0,
                activeNodes: 5,
                isEmergencyMode: false
            };
            
            // Réinitialiser
            initializeNetwork();
            logMessage('info', '🔄 Réseau réinitialisé');
            updateStats();
        }

        function exportLogs() {
            const logs = document.getElementById('messageLog').innerText;
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `nowee-mesh-logs-${new Date().toISOString().slice(0,19)}.txt`;
            a.click();
            
            URL.revokeObjectURL(url);
            logMessage('success', '📄 Logs exportés');
        }

        function logMessage(type, message) {
            const log = document.getElementById('messageLog');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
            
            // Limiter à 50 entrées
            while (log.children.length > 50) {
                log.removeChild(log.firstChild);
            }
        }

        function updateStats() {
            document.getElementById('nodeCount').textContent = networkState.activeNodes;
            document.getElementById('messageCount').textContent = networkState.messageCount;
            
            // Simuler des métriques réalistes
            const latency = 30 + Math.random() * 30;
            const throughput = 8 + Math.random() * 10;
            const reliability = 95 + Math.random() * 5;
            const coverage = Math.min(100, (networkState.activeNodes / 5) * 100);
            
            document.getElementById('latency').textContent = `${Math.round(latency)}ms`;
            document.getElementById('throughput').textContent = throughput.toFixed(1);
            document.getElementById('reliability').textContent = `${Math.round(reliability)}%`;
            document.getElementById('coverage').textContent = `${Math.round(coverage)}%`;
        }

        function startNetworkAnimation() {
            // Animation périodique du réseau
            setInterval(() => {
                if (!networkState.isEmergencyMode && Math.random() > 0.7) {
                    // Activité réseau aléatoire
                    const sourceNode = getRandomActiveNode();
                    const targetNode = getRandomActiveNode();
                    
                    if (sourceNode && targetNode && sourceNode !== targetNode) {
                        animateMessage(sourceNode, targetNode, '📡');
                    }
                }
            }, 3000);
            
            // Mise à jour des stats
            setInterval(updateStats, 2000);
        }
    </script>
</body>
</html>
