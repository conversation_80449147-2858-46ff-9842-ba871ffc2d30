/**
 * NOWEE PRO - Composants UX Magiques
 * Bibliothèque de composants pour une expérience utilisateur exceptionnelle
 * 
 * @version 2.0.0
 * <AUTHOR> UX Team
 */

// ===== SYSTÈME DE NOTIFICATIONS AVANCÉ =====

class NotificationSystem {
  constructor() {
    this.container = null;
    this.notifications = new Map();
    this.maxNotifications = 5;
    this.defaultDuration = 5000;
    this.init();
  }
  
  init() {
    // Créer le conteneur de notifications
    this.container = document.createElement('div');
    this.container.className = 'notification-container';
    this.container.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      pointer-events: none;
      max-width: 400px;
    `;
    document.body.appendChild(this.container);
  }
  
  show(message, type = 'info', options = {}) {
    const id = this.generateId();
    const notification = this.createNotification(id, message, type, options);
    
    // Limiter le nombre de notifications
    if (this.notifications.size >= this.maxNotifications) {
      const oldestId = this.notifications.keys().next().value;
      this.hide(oldestId);
    }
    
    this.notifications.set(id, notification);
    this.container.appendChild(notification.element);
    
    // Animation d'entrée
    requestAnimationFrame(() => {
      notification.element.style.transform = 'translateX(0)';
      notification.element.style.opacity = '1';
    });
    
    // Auto-suppression
    if (options.duration !== 0) {
      notification.timeout = setTimeout(() => {
        this.hide(id);
      }, options.duration || this.defaultDuration);
    }
    
    return id;
  }
  
  createNotification(id, message, type, options) {
    const element = document.createElement('div');
    element.className = `notification notification-${type}`;
    element.style.cssText = `
      background: ${this.getTypeColor(type)};
      color: white;
      padding: 16px 20px;
      border-radius: 12px;
      margin-bottom: 12px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.12);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255,255,255,0.1);
      transform: translateX(100%);
      opacity: 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      pointer-events: auto;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    `;
    
    // Contenu
    const content = document.createElement('div');
    content.style.cssText = 'display: flex; align-items: center; gap: 12px;';
    
    // Icône
    const icon = document.createElement('div');
    icon.innerHTML = this.getTypeIcon(type);
    icon.style.cssText = 'flex-shrink: 0; font-size: 20px;';
    
    // Message
    const messageEl = document.createElement('div');
    messageEl.textContent = message;
    messageEl.style.cssText = 'flex: 1; font-weight: 500; line-height: 1.4;';
    
    // Bouton fermer
    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '×';
    closeBtn.style.cssText = `
      background: none;
      border: none;
      color: white;
      font-size: 20px;
      cursor: pointer;
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background-color 0.2s;
    `;
    
    closeBtn.onmouseover = () => closeBtn.style.backgroundColor = 'rgba(255,255,255,0.2)';
    closeBtn.onmouseout = () => closeBtn.style.backgroundColor = 'transparent';
    closeBtn.onclick = (e) => {
      e.stopPropagation();
      this.hide(id);
    };
    
    content.appendChild(icon);
    content.appendChild(messageEl);
    content.appendChild(closeBtn);
    element.appendChild(content);
    
    // Barre de progression
    if (options.duration !== 0) {
      const progressBar = document.createElement('div');
      progressBar.style.cssText = `
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        background: rgba(255,255,255,0.3);
        width: 100%;
        transform-origin: left;
        animation: notificationProgress ${options.duration || this.defaultDuration}ms linear;
      `;
      element.appendChild(progressBar);
    }
    
    // Clic pour fermer
    element.onclick = () => this.hide(id);
    
    return { element, timeout: null };
  }
  
  hide(id) {
    const notification = this.notifications.get(id);
    if (!notification) return;
    
    if (notification.timeout) {
      clearTimeout(notification.timeout);
    }
    
    // Animation de sortie
    notification.element.style.transform = 'translateX(100%)';
    notification.element.style.opacity = '0';
    
    setTimeout(() => {
      if (notification.element.parentNode) {
        notification.element.parentNode.removeChild(notification.element);
      }
      this.notifications.delete(id);
    }, 300);
  }
  
  getTypeColor(type) {
    const colors = {
      success: 'linear-gradient(135deg, #10b981, #059669)',
      error: 'linear-gradient(135deg, #ef4444, #dc2626)',
      warning: 'linear-gradient(135deg, #f59e0b, #d97706)',
      info: 'linear-gradient(135deg, #3b82f6, #2563eb)',
      mesh: 'linear-gradient(135deg, #8b5cf6, #7c3aed)'
    };
    return colors[type] || colors.info;
  }
  
  getTypeIcon(type) {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️',
      mesh: '🕸️'
    };
    return icons[type] || icons.info;
  }
  
  generateId() {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// ===== SYSTÈME DE MODAL AVANCÉ =====

class ModalSystem {
  constructor() {
    this.modals = new Map();
    this.zIndexBase = 1000;
    this.init();
  }
  
  init() {
    // Ajouter les styles CSS
    const style = document.createElement('style');
    style.textContent = `
      .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(8px);
        opacity: 0;
        transition: opacity 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }
      
      .modal-backdrop.show {
        opacity: 1;
      }
      
      .modal-content {
        background: white;
        border-radius: 20px;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
        transform: scale(0.9) translateY(20px);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        max-width: 90vw;
        max-height: 90vh;
        overflow: hidden;
        position: relative;
      }
      
      .modal-backdrop.show .modal-content {
        transform: scale(1) translateY(0);
      }
      
      .modal-header {
        padding: 24px 24px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      
      .modal-title {
        font-size: 24px;
        font-weight: 700;
        color: #1f2937;
        margin: 0;
      }
      
      .modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #6b7280;
        padding: 8px;
        border-radius: 50%;
        transition: all 0.2s;
      }
      
      .modal-close:hover {
        background: #f3f4f6;
        color: #374151;
      }
      
      .modal-body {
        padding: 24px;
      }
      
      .modal-footer {
        padding: 0 24px 24px;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
      }
    `;
    document.head.appendChild(style);
  }
  
  show(options = {}) {
    const id = this.generateId();
    const modal = this.createModal(id, options);
    
    this.modals.set(id, modal);
    document.body.appendChild(modal.backdrop);
    
    // Empêcher le scroll du body
    document.body.style.overflow = 'hidden';
    
    // Animation d'entrée
    requestAnimationFrame(() => {
      modal.backdrop.classList.add('show');
    });
    
    return id;
  }
  
  createModal(id, options) {
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop';
    backdrop.style.zIndex = this.zIndexBase + this.modals.size;
    
    const content = document.createElement('div');
    content.className = 'modal-content';
    content.style.width = options.width || 'auto';
    content.style.maxWidth = options.maxWidth || '500px';
    
    // Header
    if (options.title || options.closable !== false) {
      const header = document.createElement('div');
      header.className = 'modal-header';
      
      if (options.title) {
        const title = document.createElement('h2');
        title.className = 'modal-title';
        title.textContent = options.title;
        header.appendChild(title);
      }
      
      if (options.closable !== false) {
        const closeBtn = document.createElement('button');
        closeBtn.className = 'modal-close';
        closeBtn.innerHTML = '×';
        closeBtn.onclick = () => this.hide(id);
        header.appendChild(closeBtn);
      }
      
      content.appendChild(header);
    }
    
    // Body
    const body = document.createElement('div');
    body.className = 'modal-body';
    
    if (options.content) {
      if (typeof options.content === 'string') {
        body.innerHTML = options.content;
      } else {
        body.appendChild(options.content);
      }
    }
    
    content.appendChild(body);
    
    // Footer
    if (options.buttons && options.buttons.length > 0) {
      const footer = document.createElement('div');
      footer.className = 'modal-footer';
      
      options.buttons.forEach(button => {
        const btn = document.createElement('button');
        btn.textContent = button.text;
        btn.className = button.className || 'btn-secondary';
        btn.onclick = () => {
          if (button.onClick) {
            button.onClick();
          }
          if (button.close !== false) {
            this.hide(id);
          }
        };
        footer.appendChild(btn);
      });
      
      content.appendChild(footer);
    }
    
    backdrop.appendChild(content);
    
    // Fermer en cliquant sur le backdrop
    backdrop.onclick = (e) => {
      if (e.target === backdrop && options.closable !== false) {
        this.hide(id);
      }
    };
    
    // Fermer avec Escape
    const handleEscape = (e) => {
      if (e.key === 'Escape' && options.closable !== false) {
        this.hide(id);
      }
    };
    document.addEventListener('keydown', handleEscape);
    
    return { backdrop, content, body, handleEscape };
  }
  
  hide(id) {
    const modal = this.modals.get(id);
    if (!modal) return;
    
    // Animation de sortie
    modal.backdrop.classList.remove('show');
    
    setTimeout(() => {
      if (modal.backdrop.parentNode) {
        modal.backdrop.parentNode.removeChild(modal.backdrop);
      }
      document.removeEventListener('keydown', modal.handleEscape);
      this.modals.delete(id);
      
      // Restaurer le scroll si plus de modals
      if (this.modals.size === 0) {
        document.body.style.overflow = '';
      }
    }, 300);
  }
  
  generateId() {
    return `modal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// ===== SYSTÈME DE LOADING AVANCÉ =====

class LoadingSystem {
  constructor() {
    this.loaders = new Map();
    this.init();
  }
  
  init() {
    const style = document.createElement('style');
    style.textContent = `
      .loader-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(8px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      
      .loader-overlay.show {
        opacity: 1;
      }
      
      .loader-content {
        text-align: center;
        padding: 40px;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        max-width: 300px;
      }
      
      .loader-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }
      
      .loader-text {
        font-size: 16px;
        font-weight: 500;
        color: #374151;
        margin-bottom: 8px;
      }
      
      .loader-subtext {
        font-size: 14px;
        color: #6b7280;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loader-progress {
        width: 100%;
        height: 4px;
        background: #e5e7eb;
        border-radius: 2px;
        overflow: hidden;
        margin-top: 16px;
      }
      
      .loader-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        border-radius: 2px;
        transition: width 0.3s ease;
      }
    `;
    document.head.appendChild(style);
  }
  
  show(options = {}) {
    const id = this.generateId();
    const loader = this.createLoader(id, options);
    
    this.loaders.set(id, loader);
    document.body.appendChild(loader.overlay);
    
    requestAnimationFrame(() => {
      loader.overlay.classList.add('show');
    });
    
    return id;
  }
  
  createLoader(id, options) {
    const overlay = document.createElement('div');
    overlay.className = 'loader-overlay';
    
    const content = document.createElement('div');
    content.className = 'loader-content';
    
    // Spinner
    const spinner = document.createElement('div');
    spinner.className = 'loader-spinner';
    content.appendChild(spinner);
    
    // Texte principal
    const text = document.createElement('div');
    text.className = 'loader-text';
    text.textContent = options.text || 'Chargement...';
    content.appendChild(text);
    
    // Sous-texte
    if (options.subtext) {
      const subtext = document.createElement('div');
      subtext.className = 'loader-subtext';
      subtext.textContent = options.subtext;
      content.appendChild(subtext);
    }
    
    // Barre de progression
    if (options.progress !== undefined) {
      const progressContainer = document.createElement('div');
      progressContainer.className = 'loader-progress';
      
      const progressBar = document.createElement('div');
      progressBar.className = 'loader-progress-bar';
      progressBar.style.width = `${options.progress}%`;
      
      progressContainer.appendChild(progressBar);
      content.appendChild(progressContainer);
    }
    
    overlay.appendChild(content);
    
    return { overlay, content, text, progressBar: content.querySelector('.loader-progress-bar') };
  }
  
  updateProgress(id, progress, text) {
    const loader = this.loaders.get(id);
    if (!loader) return;
    
    if (loader.progressBar) {
      loader.progressBar.style.width = `${progress}%`;
    }
    
    if (text && loader.text) {
      loader.text.textContent = text;
    }
  }
  
  hide(id) {
    const loader = this.loaders.get(id);
    if (!loader) return;
    
    loader.overlay.classList.remove('show');
    
    setTimeout(() => {
      if (loader.overlay.parentNode) {
        loader.overlay.parentNode.removeChild(loader.overlay);
      }
      this.loaders.delete(id);
    }, 300);
  }
  
  generateId() {
    return `loader_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// ===== INSTANCES GLOBALES =====

window.NoweeNotifications = new NotificationSystem();
window.NoweeModals = new ModalSystem();
window.NoweeLoading = new LoadingSystem();

// ===== FONCTIONS UTILITAIRES UX =====

// Animation de compteur
function animateCounter(element, start, end, duration = 2000, suffix = '') {
  const startTime = performance.now();
  
  function updateCounter(currentTime) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    
    // Easing function
    const easeOutCubic = 1 - Math.pow(1 - progress, 3);
    const current = Math.floor(start + (end - start) * easeOutCubic);
    
    element.textContent = current.toLocaleString() + suffix;
    
    if (progress < 1) {
      requestAnimationFrame(updateCounter);
    }
  }
  
  requestAnimationFrame(updateCounter);
}

// Scroll fluide vers un élément
function smoothScrollTo(element, offset = 0) {
  const targetPosition = element.offsetTop - offset;
  const startPosition = window.pageYOffset;
  const distance = targetPosition - startPosition;
  const duration = 800;
  let start = null;
  
  function animation(currentTime) {
    if (start === null) start = currentTime;
    const timeElapsed = currentTime - start;
    const run = ease(timeElapsed, startPosition, distance, duration);
    window.scrollTo(0, run);
    if (timeElapsed < duration) requestAnimationFrame(animation);
  }
  
  function ease(t, b, c, d) {
    t /= d / 2;
    if (t < 1) return c / 2 * t * t + b;
    t--;
    return -c / 2 * (t * (t - 2) - 1) + b;
  }
  
  requestAnimationFrame(animation);
}

// Debounce function
function debounce(func, wait, immediate) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func(...args);
  };
}

// Throttle function
function throttle(func, limit) {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Ajouter les styles d'animation CSS
const animationStyles = document.createElement('style');
animationStyles.textContent = `
  @keyframes notificationProgress {
    from { transform: scaleX(1); }
    to { transform: scaleX(0); }
  }
  
  .fade-in {
    animation: fadeIn 0.5s ease-out;
  }
  
  .slide-up {
    animation: slideUp 0.5s ease-out;
  }
  
  .bounce-in {
    animation: bounceIn 0.6s ease-out;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slideUp {
    from { 
      opacity: 0; 
      transform: translateY(30px); 
    }
    to { 
      opacity: 1; 
      transform: translateY(0); 
    }
  }
  
  @keyframes bounceIn {
    0% { 
      opacity: 0; 
      transform: scale(0.3); 
    }
    50% { 
      opacity: 1; 
      transform: scale(1.05); 
    }
    70% { 
      transform: scale(0.9); 
    }
    100% { 
      opacity: 1; 
      transform: scale(1); 
    }
  }
`;
document.head.appendChild(animationStyles);

// Export pour utilisation modulaire
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    NotificationSystem,
    ModalSystem,
    LoadingSystem,
    animateCounter,
    smoothScrollTo,
    debounce,
    throttle
  };
}
