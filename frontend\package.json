{"name": "nowee-frontend-production", "version": "1.0.0", "description": "Interface web de production pour Nowee", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next export", "deploy": "vercel --prod"}, "dependencies": {"next": "14.0.4", "react": "18.2.0", "react-dom": "18.2.0", "@supabase/supabase-js": "^2.38.4", "@supabase/auth-ui-react": "^0.4.6", "@supabase/auth-ui-shared": "^0.1.8", "axios": "^1.6.2", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-leaflet": "^4.2.1", "leaflet": "^1.9.4", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/leaflet": "^1.9.8", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}, "engines": {"node": "18.x", "npm": "9.x"}, "keywords": ["nowee", "entraide", "local", "frontend", "production", "nextjs"], "author": "Nowee Team", "license": "MIT"}