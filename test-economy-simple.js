#!/usr/bin/env node

/**
 * Test simple du système économique en mode fallback
 */

import 'dotenv/config';
import { EconomyService } from './src/services/economyService.js';

async function testSimple() {
  console.log('💰 Test Simple du Système Économique Nowee\n');
  
  try {
    // Test d'initialisation de portefeuille
    console.log('1. Test d\'initialisation de portefeuille...');
    const wallet = EconomyService.initializeWalletFallback('user123');
    console.log(`✅ Portefeuille créé: ${wallet.nowee_coins} NoweeCoins\n`);
    
    // Test de calcul de coût
    console.log('2. Test de calcul de coût de service...');
    const cost = EconomyService.calculateServiceCost(50, 2, 3, 4.5, 'EDUCATION');
    console.log(`✅ Coût calculé: ${cost} NoweeCoins pour 2h d'éducation urgente\n`);
    
    // Test de transfert en mémoire
    console.log('3. Test de transfert en mémoire...');
    const wallet1 = EconomyService.initializeWalletFallback('alice');
    const wallet2 = EconomyService.initializeWalletFallback('bob');
    
    console.log(`Avant: Alice=${wallet1.nowee_coins}, Bob=${wallet2.nowee_coins}`);
    
    const transaction = EconomyService.transferCoinsFallback('alice', 'bob', 25, 'Test transfert');
    
    console.log(`Après: Alice=${wallet1.nowee_coins}, Bob=${wallet2.nowee_coins}`);
    console.log(`✅ Transfert réussi: ${transaction.nowee_coins} coins\n`);
    
    console.log('🎉 Système économique fonctionnel en mode fallback !');
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

testSimple();
