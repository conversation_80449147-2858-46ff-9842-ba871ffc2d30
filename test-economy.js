#!/usr/bin/env node

/**
 * Script de test du système économique Nowee
 * Teste les NoweeCoins, le troc, les récompenses et les transactions
 */

import 'dotenv/config';
import { dbService } from './src/services/databaseServiceUnified.js';
import { EconomyService } from './src/services/economyService.js';
import { BarterService } from './src/services/barterService.js';

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.bold}${colors.cyan}\n💰 ${msg}${colors.reset}`),
  coins: (msg) => console.log(`${colors.magenta}🪙 ${msg}${colors.reset}`)
};

async function testEconomySystem() {
  log.title('Test du Système Économique Nowee');

  try {
    // 1. Test d'initialisation des portefeuilles
    log.info('Test d\'initialisation des portefeuilles...');
    
    const testUsers = [
      { phone: '+221771234567', name: 'Alice' },
      { phone: '+221772345678', name: 'Bob' },
      { phone: '+221773456789', name: 'Charlie' }
    ];
    
    const users = [];
    for (const userData of testUsers) {
      const user = await dbService.getUserProfile(userData.phone);
      const wallet = await EconomyService.initializeWallet(user.id);
      users.push({ ...user, wallet });
      
      log.success(`Portefeuille créé pour ${userData.name}: ${wallet.nowee_coins} NoweeCoins`);
    }

    // 2. Test de transfert de coins
    log.info('Test de transfert de NoweeCoins...');
    
    const [alice, bob, charlie] = users;
    
    const transferResult = await EconomyService.transferCoins(
      alice.id,
      bob.id,
      25,
      'Test de transfert entre Alice et Bob'
    );
    
    log.success(`Transfert réussi: ${transferResult.nowee_coins} coins d'Alice vers Bob`);
    
    // Vérifier les soldes
    const aliceWalletAfter = await EconomyService.getWallet(alice.id);
    const bobWalletAfter = await EconomyService.getWallet(bob.id);
    
    log.coins(`Alice: ${aliceWalletAfter.nowee_coins} coins (était 100)`);
    log.coins(`Bob: ${bobWalletAfter.nowee_coins} coins (était 100)`);

    // 3. Test de calcul de coût de service
    log.info('Test de calcul de coût de service...');
    
    const serviceCosts = [
      { baseRate: 50, duration: 2, urgency: 1, skill: 'REPAIR' },
      { baseRate: 50, duration: 1, urgency: 3, skill: 'EDUCATION' },
      { baseRate: 50, duration: 0.5, urgency: 5, skill: 'MEDICAL' }
    ];
    
    for (const service of serviceCosts) {
      const cost = EconomyService.calculateServiceCost(
        service.baseRate,
        service.duration,
        service.urgency,
        4.5, // Bonne réputation
        service.skill
      );
      
      log.success(`Service ${service.skill} (${service.duration}h, urgence ${service.urgency}): ${cost} coins`);
    }

    // 4. Test de récompenses
    log.info('Test du système de récompenses...');
    
    await EconomyService.rewardUser(
      bob.id,
      'HELP_COMPLETED',
      25,
      'Récompense pour aide terminée',
      { test: true }
    );
    
    await EconomyService.rewardUser(
      bob.id,
      'GOOD_RATING',
      10,
      'Bonus pour excellente évaluation (5/5)',
      { rating: 5 }
    );
    
    const bobWalletAfterRewards = await EconomyService.getWallet(bob.id);
    log.success(`Bob après récompenses: ${bobWalletAfterRewards.nowee_coins} coins`);

    // 5. Test de création de ressources pour le troc
    log.info('Test de création de ressources pour le troc...');
    
    const aliceNeed = await dbService.recordUserNeed(alice.phone, {
      type: 'MATERIAL',
      description: 'J\'ai besoin d\'une perceuse électrique pour des travaux',
      urgency: 2,
      location: {
        city: 'Dakar',
        coordinates: { latitude: 14.7167, longitude: -17.4677 }
      }
    });
    
    const charlieOffer = await dbService.recordUserOffer(charlie.phone, {
      type: 'MATERIAL',
      description: 'Je peux prêter ma perceuse électrique professionnelle',
      location: {
        city: 'Dakar',
        coordinates: { latitude: 14.7200, longitude: -17.4700 }
      }
    });
    
    log.success(`Besoin créé pour Alice: ${aliceNeed.title}`);
    log.success(`Offre créée pour Charlie: ${charlieOffer.title}`);

    // 6. Test de proposition de troc
    log.info('Test de proposition de troc...');
    
    const barterProposal = await BarterService.createBarterProposal(
      alice.id,
      charlie.id,
      {
        offeredResourceId: aliceNeed.id,
        requestedResourceId: charlieOffer.id,
        exchangeType: 'DIRECT_BARTER',
        offeredCoins: 20,
        requestedCoins: 0,
        offeredTimeHours: 0,
        requestedTimeHours: 1,
        proposalMessage: 'Je propose 20 NoweeCoins + 1h de mon temps contre votre perceuse',
        conditions: 'Utilisation pour 2 jours maximum'
      }
    );
    
    log.success(`Proposition de troc créée: ${barterProposal.id}`);
    log.info(`Type: ${barterProposal.exchange_type}`);
    log.info(`Offert: ${barterProposal.offered_coins} coins + ${barterProposal.offered_time_hours}h`);
    log.info(`Demandé: ${barterProposal.requested_coins} coins + ${barterProposal.requested_time_hours}h`);

    // 7. Test d'acceptation de troc
    log.info('Test d\'acceptation de proposition de troc...');
    
    try {
      const acceptedProposal = await BarterService.acceptBarterProposal(
        barterProposal.id,
        charlie.id
      );
      
      log.success(`Proposition acceptée: ${acceptedProposal.status}`);
      
      // Vérifier les portefeuilles après le troc
      const aliceWalletAfterBarter = await EconomyService.getWallet(alice.id);
      const charlieWalletAfterBarter = await EconomyService.getWallet(charlie.id);
      
      log.coins(`Alice après troc: ${aliceWalletAfterBarter.nowee_coins} coins, ${aliceWalletAfterBarter.time_credits}h`);
      log.coins(`Charlie après troc: ${charlieWalletAfterBarter.nowee_coins} coins, ${charlieWalletAfterBarter.time_credits}h`);
      
    } catch (error) {
      log.warning(`Erreur acceptation troc: ${error.message}`);
    }

    // 8. Test de suggestions de troc
    log.info('Test de suggestions de troc...');
    
    const suggestions = await BarterService.suggestBarterMatches(alice.id);
    
    if (suggestions.length > 0) {
      log.success(`${suggestions.length} suggestion(s) de troc trouvée(s)`);
      
      suggestions.slice(0, 3).forEach((suggestion, index) => {
        log.info(`Suggestion ${index + 1}: ${suggestion.type} (score: ${suggestion.matchScore})`);
      });
    } else {
      log.warning('Aucune suggestion de troc trouvée');
    }

    // 9. Test de l'historique des transactions
    log.info('Test de l\'historique des transactions...');
    
    const aliceTransactions = await EconomyService.getTransactionHistory(alice.id, 10);
    
    log.success(`${aliceTransactions.length} transaction(s) trouvée(s) pour Alice`);
    
    aliceTransactions.forEach((tx, index) => {
      const direction = tx.from_user_id === alice.id ? 'Envoyé' : 'Reçu';
      log.info(`${index + 1}. ${direction}: ${tx.nowee_coins} coins - ${tx.description}`);
    });

    // 10. Test des statistiques économiques
    log.info('Test des statistiques économiques...');
    
    const economyStats = await EconomyService.getEconomyStats();
    
    log.success('Statistiques économiques:');
    log.coins(`Total en circulation: ${economyStats.totalCoinsInCirculation.toFixed(0)} NoweeCoins`);
    log.coins(`Crédits temps total: ${economyStats.totalTimeCredits.toFixed(1)} heures`);
    log.info(`Transactions totales: ${economyStats.totalTransactions}`);
    log.info(`Solde moyen: ${economyStats.averageWalletBalance.toFixed(0)} coins/portefeuille`);

    // 11. Test de simulation de fin d'aide
    log.info('Test de simulation de fin d\'aide avec récompenses...');
    
    // Créer un match fictif
    const mockMatch = {
      id: `match_${Date.now()}`,
      requester_id: alice.id,
      provider_id: bob.id,
      status: 'ACCEPTED'
    };
    
    // Simuler la fin d'aide avec bonne évaluation
    await EconomyService.processHelpCompletion(mockMatch.id, 5);
    
    const bobWalletFinal = await EconomyService.getWallet(bob.id);
    log.success(`Bob après simulation d'aide (note 5/5): ${bobWalletFinal.nowee_coins} coins`);

    log.title('🎉 Tous les tests économiques terminés avec succès !');
    
    // Résumé final
    console.log('\n📊 Résumé des Tests:');
    console.log('✅ Initialisation des portefeuilles');
    console.log('✅ Transferts de NoweeCoins');
    console.log('✅ Calcul de coûts de services');
    console.log('✅ Système de récompenses');
    console.log('✅ Propositions de troc');
    console.log('✅ Acceptation de troc');
    console.log('✅ Suggestions automatiques');
    console.log('✅ Historique des transactions');
    console.log('✅ Statistiques économiques');
    console.log('✅ Simulation de fin d\'aide');

  } catch (error) {
    log.error(`Erreur lors du test: ${error.message}`);
    console.error(error);
  }
}

// Fonction principale
async function main() {
  await testEconomySystem();
  process.exit(0);
}

// Gestion des erreurs
process.on('unhandledRejection', (error) => {
  log.error(`Erreur non gérée: ${error.message}`);
  process.exit(1);
});

// Exécuter les tests
main().catch(error => {
  log.error(`Erreur: ${error.message}`);
  process.exit(1);
});
