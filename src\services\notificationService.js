/**
 * Service de notifications pour Nowee
 * Gère les notifications push, WhatsApp et autres canaux
 */

import twilio from 'twilio';
import { dbService } from './databaseService.js';

// Configuration Twilio
const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

// Types de notifications
const NOTIFICATION_TYPES = {
  MATCH_FOUND: 'match_found',
  HELP_REQUEST: 'help_request',
  MATCH_ACCEPTED: 'match_accepted',
  MATCH_REJECTED: 'match_rejected',
  MATCH_COMPLETED: 'match_completed',
  URGENT_NEED: 'urgent_need',
  COMMUNITY_UPDATE: 'community_update',
  PROACTIVE_SUGGESTION: 'proactive_suggestion'
};

// Templates de messages
const MESSAGE_TEMPLATES = {
  [NOTIFICATION_TYPES.MATCH_FOUND]: {
    title: '🎯 Aide trouvée !',
    body: 'Quelqu\'un peut vous aider avec votre demande',
    whatsapp: '🎯 *Aide trouvée !*\n\n{message}\n\nRépondez "OUI" pour accepter ou "NON" pour refuser.'
  },
  [NOTIFICATION_TYPES.HELP_REQUEST]: {
    title: '🙋‍♂️ Demande d\'aide',
    body: 'Quelqu\'un a besoin de votre aide',
    whatsapp: '🙋‍♂️ *Demande d\'aide*\n\n{message}\n\nPouvez-vous aider ? Répondez "OUI" ou "NON".'
  },
  [NOTIFICATION_TYPES.MATCH_ACCEPTED]: {
    title: '✅ Aide acceptée',
    body: 'Votre aide a été acceptée',
    whatsapp: '✅ *Aide acceptée*\n\n{message}\n\nVous pouvez maintenant vous coordonner !'
  },
  [NOTIFICATION_TYPES.MATCH_COMPLETED]: {
    title: '🎉 Aide terminée',
    body: 'L\'aide a été marquée comme terminée',
    whatsapp: '🎉 *Aide terminée*\n\n{message}\n\nMerci pour votre participation à la communauté Nowee !'
  },
  [NOTIFICATION_TYPES.URGENT_NEED]: {
    title: '🚨 Besoin urgent',
    body: 'Quelqu\'un a un besoin urgent près de vous',
    whatsapp: '🚨 *URGENT*\n\n{message}\n\nPouvez-vous aider immédiatement ?'
  },
  [NOTIFICATION_TYPES.PROACTIVE_SUGGESTION]: {
    title: '💡 Suggestion Nowee',
    body: 'Nous avons une suggestion pour vous',
    whatsapp: '💡 *Suggestion Nowee*\n\n{message}'
  }
};

/**
 * Service de notifications principal
 */
export class NotificationService {
  
  /**
   * Envoie une notification de matching
   */
  static async sendMatchNotification(user, type, data) {
    try {
      const template = MESSAGE_TEMPLATES[type];
      if (!template) {
        throw new Error(`Type de notification inconnu: ${type}`);
      }

      const message = data.message || template.body;
      
      // Envoyer via WhatsApp si numéro disponible
      if (user.phone) {
        await this.sendWhatsAppNotification(user.phone, template.whatsapp, { message });
      }

      // Enregistrer la notification en base
      await this.logNotification(user.id, type, data);

      console.log(`📱 Notification ${type} envoyée à ${user.phone}`);
      
    } catch (error) {
      console.error('Erreur envoi notification:', error);
    }
  }

  /**
   * Envoie une notification WhatsApp
   */
  static async sendWhatsAppNotification(phone, template, data) {
    try {
      const message = this.formatMessage(template, data);
      
      await twilioClient.messages.create({
        from: process.env.TWILIO_WHATSAPP_NUMBER,
        to: `whatsapp:${phone}`,
        body: message
      });

    } catch (error) {
      console.error('Erreur WhatsApp:', error);
      throw error;
    }
  }

  /**
   * Formate un message avec les données
   */
  static formatMessage(template, data) {
    let message = template;
    
    for (const [key, value] of Object.entries(data)) {
      const placeholder = `{${key}}`;
      message = message.replace(new RegExp(placeholder, 'g'), value);
    }
    
    return message;
  }

  /**
   * Envoie des notifications de proximité pour besoins urgents
   */
  static async sendProximityNotifications(need, radiusKm = 5) {
    try {
      if (!need.location || !need.location.coordinates) {
        console.log('Pas de géolocalisation pour les notifications de proximité');
        return;
      }

      // Trouver les utilisateurs à proximité
      const nearbyUsers = await this.findNearbyUsers(
        need.location.coordinates.latitude,
        need.location.coordinates.longitude,
        radiusKm
      );

      // Filtrer les utilisateurs qui peuvent aider
      const potentialHelpers = nearbyUsers.filter(user => 
        user.id !== need.userId && 
        user.preferences?.receiveProximityAlerts !== false
      );

      // Envoyer les notifications
      for (const user of potentialHelpers) {
        await this.sendMatchNotification(user, NOTIFICATION_TYPES.URGENT_NEED, {
          message: `Besoin urgent à ${radiusKm}km : "${need.description}"`
        });
      }

      console.log(`📍 ${potentialHelpers.length} notifications de proximité envoyées`);

    } catch (error) {
      console.error('Erreur notifications proximité:', error);
    }
  }

  /**
   * Trouve les utilisateurs à proximité
   */
  static async findNearbyUsers(latitude, longitude, radiusKm) {
    try {
      // Requête approximative (en production, utiliser PostGIS)
      const users = await dbService.prisma.user.findMany({
        where: {
          location: {
            not: null
          }
        }
      });

      // Filtrer par distance (calcul approximatif)
      const nearbyUsers = users.filter(user => {
        if (!user.location || !user.location.coordinates) return false;
        
        const distance = this.calculateDistance(
          latitude, longitude,
          user.location.coordinates.latitude,
          user.location.coordinates.longitude
        );
        
        return distance <= radiusKm;
      });

      return nearbyUsers;

    } catch (error) {
      console.error('Erreur recherche utilisateurs proximité:', error);
      return [];
    }
  }

  /**
   * Calcule la distance entre deux points (formule de Haversine)
   */
  static calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371; // Rayon de la Terre en km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    
    return distance;
  }

  /**
   * Envoie des notifications communautaires
   */
  static async sendCommunityNotifications(message, targetUsers = null) {
    try {
      let users;
      
      if (targetUsers) {
        users = targetUsers;
      } else {
        // Tous les utilisateurs actifs
        users = await dbService.prisma.user.findMany({
          where: {
            lastActiveAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 jours
            }
          }
        });
      }

      for (const user of users) {
        await this.sendMatchNotification(user, NOTIFICATION_TYPES.COMMUNITY_UPDATE, {
          message
        });
      }

      console.log(`📢 Notification communautaire envoyée à ${users.length} utilisateurs`);

    } catch (error) {
      console.error('Erreur notification communautaire:', error);
    }
  }

  /**
   * Envoie des suggestions proactives
   */
  static async sendProactiveSuggestion(userId, suggestion) {
    try {
      const user = await dbService.prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) return;

      await this.sendMatchNotification(user, NOTIFICATION_TYPES.PROACTIVE_SUGGESTION, {
        message: suggestion
      });

    } catch (error) {
      console.error('Erreur suggestion proactive:', error);
    }
  }

  /**
   * Enregistre une notification en base
   */
  static async logNotification(userId, type, data) {
    try {
      await dbService.prisma.systemEvent.create({
        data: {
          type: `notification_${type}`,
          userId,
          data: {
            notificationType: type,
            ...data
          }
        }
      });
    } catch (error) {
      console.error('Erreur log notification:', error);
    }
  }

  /**
   * Gère les réponses aux notifications (OUI/NON)
   */
  static async handleNotificationResponse(userPhone, response, context) {
    try {
      const normalizedResponse = response.toLowerCase().trim();
      
      if (['oui', 'yes', 'o', 'y', '1'].includes(normalizedResponse)) {
        return await this.handlePositiveResponse(userPhone, context);
      } else if (['non', 'no', 'n', '0'].includes(normalizedResponse)) {
        return await this.handleNegativeResponse(userPhone, context);
      }
      
      return null; // Réponse non reconnue
      
    } catch (error) {
      console.error('Erreur traitement réponse notification:', error);
      return null;
    }
  }

  /**
   * Traite une réponse positive
   */
  static async handlePositiveResponse(userPhone, context) {
    // Logique selon le contexte (match, aide, etc.)
    console.log(`✅ Réponse positive de ${userPhone}:`, context);
    return "✅ Merci ! Nous vous mettons en relation.";
  }

  /**
   * Traite une réponse négative
   */
  static async handleNegativeResponse(userPhone, context) {
    console.log(`❌ Réponse négative de ${userPhone}:`, context);
    return "👍 Pas de problème, merci de nous avoir répondu !";
  }

  /**
   * Programme des notifications récurrentes
   */
  static async scheduleRecurringNotifications() {
    try {
      // Notifications quotidiennes de statistiques communautaires
      const stats = await dbService.getGlobalStats();
      
      if (stats.totalUsers > 10) { // Seulement si communauté active
        const message = `📊 Aujourd'hui dans votre communauté Nowee :\n• ${stats.totalNeeds} nouveaux besoins\n• ${stats.totalMatches} aides réalisées\n• ${stats.totalUsers} membres actifs`;
        
        // Envoyer aux utilisateurs actifs
        await this.sendCommunityNotifications(message);
      }

    } catch (error) {
      console.error('Erreur notifications récurrentes:', error);
    }
  }

  /**
   * Nettoie les anciennes notifications
   */
  static async cleanupOldNotifications(daysOld = 30) {
    try {
      const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);
      
      await dbService.prisma.systemEvent.deleteMany({
        where: {
          type: { startsWith: 'notification_' },
          createdAt: { lt: cutoffDate }
        }
      });

      console.log(`🧹 Notifications anciennes nettoyées (>${daysOld} jours)`);

    } catch (error) {
      console.error('Erreur nettoyage notifications:', error);
    }
  }
}

export default NotificationService;
