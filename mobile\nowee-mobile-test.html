
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nowee Mobile - Test Web</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .title {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .subtitle {
            color: #666;
            font-size: 0.9em;
        }
        .wallet-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            color: white;
            margin-bottom: 20px;
        }
        .balance {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .balance-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        .features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .feature {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .feature-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .feature-desc {
            font-size: 0.8em;
            color: #666;
        }
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 20px;
        }
        .tab {
            flex: 1;
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .tab.active {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        .status-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🤝</div>
            <div class="title">Nowee Mobile</div>
            <div class="subtitle">Entraide locale révolutionnaire</div>
        </div>
        
        <div class="wallet-card">
            <div class="balance-label">Mon Portefeuille</div>
            <div class="balance">100.00 NC</div>
            <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                <div>
                    <div style="font-size: 0.8em; opacity: 0.8;">Crédits Temps</div>
                    <div style="font-weight: bold;">2.5h</div>
                </div>
                <div style="text-align: right;">
                    <div style="font-size: 0.8em; opacity: 0.8;">Réputation</div>
                    <div style="font-weight: bold;">⭐ 4.8</div>
                </div>
            </div>
        </div>
        
        <div class="tabs">
            <div class="tab active">🏠 Accueil</div>
            <div class="tab">🗺️ Carte</div>
            <div class="tab">💰 Wallet</div>
            <div class="tab">🔄 Troc</div>
            <div class="tab">👤 Profil</div>
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">💰</div>
                <div class="feature-title">NoweeCoins</div>
                <div class="feature-desc">Monnaie locale</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🔄</div>
                <div class="feature-title">Troc</div>
                <div class="feature-desc">Échanges intelligents</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🗺️</div>
                <div class="feature-title">Carte</div>
                <div class="feature-desc">Aide de proximité</div>
            </div>
            <div class="feature">
                <div class="feature-icon">⏰</div>
                <div class="feature-title">Temps</div>
                <div class="feature-desc">Crédits horaires</div>
            </div>
        </div>
        
        <div class="status">
            <div class="status-icon">✅</div>
            <div style="font-weight: bold; color: #4caf50;">Application Prête !</div>
            <div style="font-size: 0.9em; color: #666; margin-top: 5px;">
                Interface mobile Nowee fonctionnelle avec système économique intégré
            </div>
        </div>
    </div>
    
    <script>
        // Simulation d'interactivité
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // Animation du solde
        let balance = 100;
        setInterval(() => {
            balance += Math.random() * 2 - 1;
            document.querySelector('.balance').textContent = balance.toFixed(2) + ' NC';
        }, 3000);
        
        console.log('🚀 Nowee Mobile - Version Web de Test');
        console.log('📱 Interface économique fonctionnelle');
        console.log('💰 Système de NoweeCoins actif');
        console.log('🔄 Troc intelligent disponible');
    </script>
</body>
</html>
