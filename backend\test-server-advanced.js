/**
 * Serveur de test avancé Nowee
 * Utilise les vraies APIs OpenAI et Twilio
 */

const express = require('express');
const cors = require('cors');
const OpenAI = require('openai');
const twilio = require('twilio');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Clients API réels
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Données de test enrichies
const mockData = {
  users: 1250,
  helps: 3420,
  coins: 125000,
  communities: 45,
  wallets: new Map(),
  transactions: [],
  barterProposals: [],
  needs: [
    {
      id: '1',
      phone: '+************',
      title: 'Aide pour déménagement',
      description: 'Je cherche de l\'aide pour déménager ce weekend à Dakar',
      category: 'SERVICE',
      urgency: 'medium',
      latitude: 14.6928,
      longitude: -17.4467,
      address: 'Plateau, Dakar, Sénégal',
      status: 'active',
      created_at: new Date().toISOString(),
      user_profile: {
        name: 'Aminata Diallo',
        phone: '+************',
        rating: 4.8
      }
    },
    {
      id: '2',
      phone: '+************',
      title: 'Cours de français',
      description: 'J\'offre des cours de français pour débutants et intermédiaires',
      category: 'EDUCATION',
      urgency: 'low',
      latitude: 14.6937,
      longitude: -17.4441,
      address: 'Médina, Dakar, Sénégal',
      status: 'active',
      created_at: new Date().toISOString(),
      user_profile: {
        name: 'Moussa Sow',
        phone: '+************',
        rating: 4.9
      }
    },
    {
      id: '3',
      phone: '+221701234569',
      title: 'Réparation électronique',
      description: 'Je répare téléphones, ordinateurs et appareils électroniques',
      category: 'TECHNICAL',
      urgency: 'high',
      latitude: 14.6892,
      longitude: -17.4492,
      address: 'Sandaga, Dakar, Sénégal',
      status: 'active',
      created_at: new Date().toISOString(),
      user_profile: {
        name: 'Fatou Ndiaye',
        phone: '+221701234569',
        rating: 4.7
      }
    }
  ],
  chatHistory: []
};

// Initialiser des portefeuilles de test
const initializeWallets = () => {
  mockData.wallets.set('+************', {
    phone: '+************',
    nowee_coins: 150,
    time_credits: 2.5,
    total_earned: 200,
    total_spent: 50,
    reputation_bonus: 10
  });

  mockData.wallets.set('+************', {
    phone: '+************',
    nowee_coins: 75,
    time_credits: 1.0,
    total_earned: 100,
    total_spent: 25,
    reputation_bonus: 5
  });

  mockData.wallets.set('+221701234569', {
    phone: '+221701234569',
    nowee_coins: 200,
    time_credits: 3.0,
    total_earned: 250,
    total_spent: 50,
    reputation_bonus: 15
  });
};

// Initialiser des propositions de troc
const initializeBarterProposals = () => {
  mockData.barterProposals = [
    {
      id: '1',
      proposer_phone: '+************',
      target_phone: '+************',
      offered_resource_id: '1',
      requested_resource_id: '2',
      exchange_type: 'TIME_FOR_SERVICE',
      offered_coins: 0,
      offered_time_hours: 2,
      requested_coins: 30,
      requested_time_hours: 0,
      proposal_message: 'Je propose 2h d\'aide déménagement contre 30 NoweeCoins',
      status: 'PENDING',
      created_at: new Date().toISOString(),
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: '2',
      proposer_phone: '+221701234569',
      target_phone: '+************',
      offered_resource_id: '3',
      requested_resource_id: '1',
      exchange_type: 'SERVICE_FOR_SERVICE',
      offered_coins: 0,
      offered_time_hours: 1,
      requested_coins: 0,
      requested_time_hours: 1,
      proposal_message: 'Échange : 1h réparation électronique contre 1h aide déménagement',
      status: 'ACCEPTED',
      created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      expires_at: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString()
    }
  ];
};

initializeWallets();
initializeBarterProposals();

// Routes de santé avancées
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV,
    apis: {
      openai: !!process.env.OPENAI_API_KEY,
      twilio: !!process.env.TWILIO_ACCOUNT_SID,
      database: 'mock'
    },
    features: {
      economy: true,
      barter: true,
      voice: true,
      maps: true,
      notifications: true,
      ai_chat: true
    },
    stats: {
      users: mockData.users,
      helps: mockData.helps,
      coins: mockData.coins,
      communities: mockData.communities,
      active_proposals: mockData.barterProposals.filter(p => p.status === 'PENDING').length
    }
  });
});

// Test de connectivité APIs
app.get('/api/test/connectivity', async (req, res) => {
  const results = {
    timestamp: new Date().toISOString(),
    tests: {}
  };

  // Test OpenAI
  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: "Dis juste 'OK' pour tester la connexion" }],
      max_tokens: 5
    });
    results.tests.openai = {
      status: 'SUCCESS',
      response: completion.choices[0].message.content,
      model: 'gpt-3.5-turbo'
    };
  } catch (error) {
    results.tests.openai = {
      status: 'ERROR',
      error: error.message
    };
  }

  // Test Twilio
  try {
    const account = await twilioClient.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
    results.tests.twilio = {
      status: 'SUCCESS',
      account_sid: account.sid,
      account_status: account.status
    };
  } catch (error) {
    results.tests.twilio = {
      status: 'ERROR',
      error: error.message
    };
  }

  res.json(results);
});

// API Chat IA Avancée
app.post('/api/chat/ai', async (req, res) => {
  try {
    const { message, phone, context } = req.body;

    if (!message) {
      return res.status(400).json({ success: false, error: 'Message requis' });
    }

    // Contexte Nowee pour l'IA
    const systemPrompt = `Tu es Nowee, un assistant d'entraide locale au Sénégal. 

CONTEXTE NOWEE:
- Plateforme d'entraide locale avec système économique (NoweeCoins)
- Système de troc intelligent (objets, services, temps)
- Géolocalisation pour aide de proximité
- Communauté solidaire sénégalaise

FONCTIONNALITÉS:
- NoweeCoins: monnaie locale virtuelle
- Crédits temps: valorisation du temps d'aide
- Troc: échanges équitables automatisés
- Carte: localisation des aides
- WhatsApp: interface accessible

STYLE DE RÉPONSE:
- Chaleureux et bienveillant
- Utilise des expressions sénégalaises appropriées
- Encourage l'entraide et la solidarité
- Propose des solutions concrètes
- Maximum 200 mots

UTILISATEUR: ${phone || 'Anonyme'}
CONTEXTE: ${context || 'Conversation générale'}`;

    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: message }
      ],
      max_tokens: 300,
      temperature: 0.7
    });

    const response = completion.choices[0].message.content;

    // Enregistrer la conversation
    mockData.chatHistory.push({
      id: Date.now().toString(),
      phone: phone || 'anonymous',
      message,
      response,
      timestamp: new Date().toISOString(),
      context: context || 'general'
    });

    res.json({
      success: true,
      response,
      tokens_used: completion.usage.total_tokens,
      model: 'gpt-3.5-turbo'
    });

  } catch (error) {
    console.error('Erreur IA:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      fallback_response: "Désolé, je rencontre un problème technique. Peux-tu reformuler ta demande ?"
    });
  }
});

// API WhatsApp avec IA
app.post('/api/whatsapp/webhook', async (req, res) => {
  try {
    const { Body, From, MessageSid } = req.body;

    if (!Body || !From) {
      return res.status(400).json({ error: 'Message invalide' });
    }

    console.log(`📱 Message WhatsApp reçu de ${From}: ${Body}`);

    // Traitement IA du message
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: `Tu es Nowee, assistant d'entraide locale au Sénégal via WhatsApp.

INSTRUCTIONS SPÉCIALES:
- Réponds en français avec des expressions sénégalaises
- Sois chaleureux et encourage l'entraide
- Propose des actions concrètes (créer besoin, chercher aide, etc.)
- Mentionne les NoweeCoins et le système de troc quand pertinent
- Maximum 160 caractères (limite SMS)

FONCTIONNALITÉS DISPONIBLES:
- Créer un besoin d'aide
- Chercher de l'aide près de soi
- Gagner des NoweeCoins en aidant
- Échanger services/objets par troc
- Voir sa réputation communautaire`
        },
        {
          role: "user",
          content: Body
        }
      ],
      max_tokens: 100,
      temperature: 0.8
    });

    const response = completion.choices[0].message.content;

    // Simuler l'envoi via Twilio (en mode test)
    console.log(`📤 Réponse Nowee: ${response}`);

    // Enregistrer l'interaction
    mockData.chatHistory.push({
      id: MessageSid || Date.now().toString(),
      phone: From,
      message: Body,
      response: response,
      timestamp: new Date().toISOString(),
      platform: 'whatsapp'
    });

    res.json({
      success: true,
      message: 'Message traité',
      response: response,
      tokens_used: completion.usage.total_tokens
    });

  } catch (error) {
    console.error('Erreur WhatsApp:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      fallback_response: "Salut ! Je suis Nowee 🤝 Comment puis-je t'aider aujourd'hui ?"
    });
  }
});

// API Économique Avancée
app.get('/api/economy/wallet/:phone', (req, res) => {
  try {
    const { phone } = req.params;
    
    let wallet = mockData.wallets.get(phone);
    
    if (!wallet) {
      wallet = {
        phone,
        nowee_coins: 100,
        time_credits: 0,
        total_earned: 100,
        total_spent: 0,
        reputation_bonus: 0
      };
      mockData.wallets.set(phone, wallet);
    }
    
    // Ajouter des statistiques calculées
    const stats = {
      net_balance: wallet.total_earned - wallet.total_spent,
      reputation_level: wallet.reputation_bonus >= 15 ? 'Expert' : 
                       wallet.reputation_bonus >= 10 ? 'Avancé' :
                       wallet.reputation_bonus >= 5 ? 'Intermédiaire' : 'Débutant',
      total_value: wallet.nowee_coins + (wallet.time_credits * 50), // 1h = 50 coins
      rank: Math.floor(Math.random() * 100) + 1 // Rang simulé
    };
    
    res.json({ 
      success: true, 
      wallet: { ...wallet, stats }
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// API Troc Avancée
app.get('/api/economy/barter/:phone', (req, res) => {
  try {
    const { phone } = req.params;
    const type = req.query.type || 'all';
    
    let proposals = [...mockData.barterProposals];
    
    if (type === 'sent') {
      proposals = proposals.filter(p => p.proposer_phone === phone);
    } else if (type === 'received') {
      proposals = proposals.filter(p => p.target_phone === phone);
    } else {
      proposals = proposals.filter(p => 
        p.proposer_phone === phone || p.target_phone === phone
      );
    }
    
    // Enrichir avec des informations calculées
    proposals = proposals.map(proposal => ({
      ...proposal,
      is_proposer: proposal.proposer_phone === phone,
      is_expired: new Date(proposal.expires_at) < new Date(),
      time_remaining: Math.max(0, new Date(proposal.expires_at) - new Date()),
      total_offered_value: proposal.offered_coins + (proposal.offered_time_hours * 50),
      total_requested_value: proposal.requested_coins + (proposal.requested_time_hours * 50)
    }));
    
    res.json({ success: true, proposals });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// API Suggestions de Troc IA
app.get('/api/economy/barter/:phone/suggestions', async (req, res) => {
  try {
    const { phone } = req.params;
    
    // Simuler des suggestions intelligentes
    const userWallet = mockData.wallets.get(phone);
    const userNeeds = mockData.needs.filter(n => n.phone === phone);
    const availableOffers = mockData.needs.filter(n => n.phone !== phone);
    
    const suggestions = [];
    
    // Générer des suggestions basées sur l'IA
    for (let i = 0; i < Math.min(3, availableOffers.length); i++) {
      const offer = availableOffers[i];
      
      try {
        const completion = await openai.chat.completions.create({
          model: "gpt-3.5-turbo",
          messages: [
            {
              role: "system",
              content: "Tu es un expert en matching de troc. Génère une suggestion d'échange équitable en 1 phrase."
            },
            {
              role: "user",
              content: `Utilisateur avec ${userWallet?.nowee_coins || 100} NoweeCoins veut échanger avec: ${offer.title}`
            }
          ],
          max_tokens: 50
        });
        
        suggestions.push({
          id: `suggestion_${i}`,
          type: 'AI_MATCH',
          match_score: Math.floor(Math.random() * 30) + 70, // 70-100%
          target_offer: offer,
          suggested_exchange: completion.choices[0].message.content,
          estimated_value: Math.floor(Math.random() * 100) + 50
        });
      } catch (error) {
        // Suggestion par défaut si l'IA échoue
        suggestions.push({
          id: `suggestion_${i}`,
          type: 'BASIC_MATCH',
          match_score: Math.floor(Math.random() * 20) + 60,
          target_offer: offer,
          suggested_exchange: `Échange recommandé pour: ${offer.title}`,
          estimated_value: Math.floor(Math.random() * 100) + 50
        });
      }
    }
    
    res.json({ success: true, suggestions });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// API Historique Chat
app.get('/api/chat/history/:phone', (req, res) => {
  try {
    const { phone } = req.params;
    const limit = parseInt(req.query.limit) || 10;
    
    const history = mockData.chatHistory
      .filter(chat => chat.phone === phone)
      .slice(-limit)
      .reverse();
    
    res.json({ success: true, history });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// API Statistiques Avancées
app.get('/api/stats/advanced', (req, res) => {
  try {
    const stats = {
      timestamp: new Date().toISOString(),
      community: {
        total_users: mockData.users,
        active_users_24h: Math.floor(mockData.users * 0.3),
        total_helps: mockData.helps,
        helps_today: Math.floor(Math.random() * 50) + 20,
        communities: mockData.communities
      },
      economy: {
        total_coins: mockData.coins,
        coins_in_circulation: Math.floor(mockData.coins * 0.85),
        average_wallet: Math.floor(mockData.coins / mockData.users),
        total_transactions: mockData.transactions.length,
        transactions_today: Math.floor(Math.random() * 100) + 50
      },
      barter: {
        total_proposals: mockData.barterProposals.length,
        active_proposals: mockData.barterProposals.filter(p => p.status === 'PENDING').length,
        success_rate: 0.78,
        average_exchange_value: 125
      },
      ai: {
        total_conversations: mockData.chatHistory.length,
        conversations_today: mockData.chatHistory.filter(c => 
          new Date(c.timestamp).toDateString() === new Date().toDateString()
        ).length,
        average_response_time: '1.2s',
        satisfaction_rate: 0.92
      }
    };
    
    res.json({ success: true, stats });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Route de test pour toutes les fonctionnalités
app.get('/api/test/all-features', async (req, res) => {
  try {
    const testResults = {
      timestamp: new Date().toISOString(),
      tests: {}
    };

    // Test 1: Portefeuille
    const testPhone = '+************';
    const wallet = mockData.wallets.get(testPhone);
    testResults.tests.wallet = {
      status: wallet ? 'SUCCESS' : 'FAILED',
      data: wallet
    };

    // Test 2: Besoins
    testResults.tests.needs = {
      status: mockData.needs.length > 0 ? 'SUCCESS' : 'FAILED',
      count: mockData.needs.length
    };

    // Test 3: Propositions de troc
    testResults.tests.barter = {
      status: mockData.barterProposals.length > 0 ? 'SUCCESS' : 'FAILED',
      count: mockData.barterProposals.length
    };

    // Test 4: IA (si disponible)
    try {
      const aiTest = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [{ role: "user", content: "Test" }],
        max_tokens: 5
      });
      testResults.tests.ai = {
        status: 'SUCCESS',
        model: 'gpt-3.5-turbo'
      };
    } catch (error) {
      testResults.tests.ai = {
        status: 'FAILED',
        error: error.message
      };
    }

    res.json(testResults);
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur de test avancé Nowee démarré sur le port ${PORT}`);
  console.log(`📊 Santé: http://localhost:${PORT}/health`);
  console.log(`🔗 Test connectivité: http://localhost:${PORT}/api/test/connectivity`);
  console.log(`🤖 Chat IA: POST http://localhost:${PORT}/api/chat/ai`);
  console.log(`📱 WhatsApp: POST http://localhost:${PORT}/api/whatsapp/webhook`);
  console.log(`💰 Portefeuille: http://localhost:${PORT}/api/economy/wallet/+************`);
  console.log(`🔄 Troc: http://localhost:${PORT}/api/economy/barter/+************`);
  console.log(`📈 Stats: http://localhost:${PORT}/api/stats/advanced`);
  console.log(`🧪 Test complet: http://localhost:${PORT}/api/test/all-features`);
});

module.exports = app;
