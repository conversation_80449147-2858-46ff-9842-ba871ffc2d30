/**
 * Service API pour l'application mobile Nowee
 * Gère toutes les communications avec le backend
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

// Configuration de l'API
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000' // Développement local
  : 'https://your-production-api.com'; // Production

// Types
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

interface User {
  id: string;
  phone: string;
  name?: string;
  location?: {
    city: string;
    country: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
  };
  preferences: any;
  messageCount: number;
  helpGiven: number;
  helpReceived: number;
  rating: number;
}

interface Need {
  id: string;
  type: string;
  title: string;
  description: string;
  location?: any;
  urgency: number;
  status: string;
  createdAt: string;
  user: User;
}

interface Offer {
  id: string;
  type: string;
  title: string;
  description: string;
  location?: any;
  availability: any;
  conditions?: string;
  status: string;
  createdAt: string;
  user: User;
}

class ApiServiceClass {
  private api: AxiosInstance;
  private authToken: string | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
    this.loadAuthToken();
  }

  private setupInterceptors() {
    // Intercepteur de requête pour ajouter le token d'authentification
    this.api.interceptors.request.use(
      (config) => {
        if (this.authToken) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Intercepteur de réponse pour gérer les erreurs globalement
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          this.handleAuthError();
        } else if (error.response?.status >= 500) {
          Alert.alert(
            'Erreur serveur',
            'Une erreur est survenue. Veuillez réessayer plus tard.'
          );
        } else if (!error.response) {
          Alert.alert(
            'Erreur de connexion',
            'Vérifiez votre connexion internet et réessayez.'
          );
        }
        return Promise.reject(error);
      }
    );
  }

  private async loadAuthToken() {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (token) {
        this.authToken = token;
      }
    } catch (error) {
      console.error('Erreur lors du chargement du token:', error);
    }
  }

  private async saveAuthToken(token: string) {
    try {
      await AsyncStorage.setItem('auth_token', token);
      this.authToken = token;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du token:', error);
    }
  }

  private async removeAuthToken() {
    try {
      await AsyncStorage.removeItem('auth_token');
      this.authToken = null;
    } catch (error) {
      console.error('Erreur lors de la suppression du token:', error);
    }
  }

  private handleAuthError() {
    this.removeAuthToken();
    // Rediriger vers l'écran de connexion
    // Cette logique dépend de votre système de navigation
  }

  // Méthodes d'authentification
  async login(phone: string, verificationCode?: string): Promise<ApiResponse<{ user: User; token: string }>> {
    try {
      const response = await this.api.post('/auth/login', {
        phone,
        verificationCode,
      });

      if (response.data.token) {
        await this.saveAuthToken(response.data.token);
      }

      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion',
      };
    }
  }

  async logout(): Promise<void> {
    await this.removeAuthToken();
  }

  async sendVerificationCode(phone: string): Promise<ApiResponse> {
    try {
      const response = await this.api.post('/auth/send-code', { phone });
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur lors de l\'envoi du code',
      };
    }
  }

  // Méthodes pour les données d'accueil
  async getHomeData(location?: any): Promise<any> {
    try {
      const response = await this.api.get('/home', {
        params: { location: JSON.stringify(location) },
      });
      return response.data;
    } catch (error) {
      throw new Error('Impossible de charger les données d\'accueil');
    }
  }

  // Méthodes pour les besoins
  async getNeeds(filters?: any): Promise<Need[]> {
    try {
      const response = await this.api.get('/needs', { params: filters });
      return response.data.needs || [];
    } catch (error) {
      throw new Error('Impossible de charger les besoins');
    }
  }

  async createNeed(needData: Partial<Need>): Promise<ApiResponse<Need>> {
    try {
      const response = await this.api.post('/needs', needData);
      return {
        success: true,
        data: response.data.need,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur lors de la création du besoin',
      };
    }
  }

  async updateNeed(needId: string, updates: Partial<Need>): Promise<ApiResponse<Need>> {
    try {
      const response = await this.api.put(`/needs/${needId}`, updates);
      return {
        success: true,
        data: response.data.need,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur lors de la mise à jour du besoin',
      };
    }
  }

  async deleteNeed(needId: string): Promise<ApiResponse> {
    try {
      await this.api.delete(`/needs/${needId}`);
      return { success: true };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur lors de la suppression du besoin',
      };
    }
  }

  // Méthodes pour les offres
  async getOffers(filters?: any): Promise<Offer[]> {
    try {
      const response = await this.api.get('/offers', { params: filters });
      return response.data.offers || [];
    } catch (error) {
      throw new Error('Impossible de charger les offres');
    }
  }

  async createOffer(offerData: Partial<Offer>): Promise<ApiResponse<Offer>> {
    try {
      const response = await this.api.post('/offers', offerData);
      return {
        success: true,
        data: response.data.offer,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur lors de la création de l\'offre',
      };
    }
  }

  // Méthodes pour le chat
  async sendMessage(message: string, context?: any): Promise<ApiResponse<{ response: string }>> {
    try {
      const response = await this.api.post('/chat', {
        message,
        context,
      });
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur lors de l\'envoi du message',
      };
    }
  }

  async getChatHistory(limit = 20): Promise<any[]> {
    try {
      const response = await this.api.get('/chat/history', {
        params: { limit },
      });
      return response.data.messages || [];
    } catch (error) {
      throw new Error('Impossible de charger l\'historique du chat');
    }
  }

  // Méthodes pour le profil utilisateur
  async getUserProfile(): Promise<User> {
    try {
      const response = await this.api.get('/profile');
      return response.data.user;
    } catch (error) {
      throw new Error('Impossible de charger le profil utilisateur');
    }
  }

  async updateUserProfile(updates: Partial<User>): Promise<ApiResponse<User>> {
    try {
      const response = await this.api.put('/profile', updates);
      return {
        success: true,
        data: response.data.user,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur lors de la mise à jour du profil',
      };
    }
  }

  // Méthodes pour les correspondances
  async getMatches(): Promise<any[]> {
    try {
      const response = await this.api.get('/matches');
      return response.data.matches || [];
    } catch (error) {
      throw new Error('Impossible de charger les correspondances');
    }
  }

  async acceptMatch(matchId: string): Promise<ApiResponse> {
    try {
      await this.api.post(`/matches/${matchId}/accept`);
      return { success: true };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur lors de l\'acceptation de la correspondance',
      };
    }
  }

  async rejectMatch(matchId: string): Promise<ApiResponse> {
    try {
      await this.api.post(`/matches/${matchId}/reject`);
      return { success: true };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur lors du rejet de la correspondance',
      };
    }
  }

  // Méthodes utilitaires
  async getStats(): Promise<any> {
    try {
      const response = await this.api.get('/stats');
      return response.data;
    } catch (error) {
      throw new Error('Impossible de charger les statistiques');
    }
  }

  async reportIssue(issue: { type: string; description: string; context?: any }): Promise<ApiResponse> {
    try {
      await this.api.post('/report', issue);
      return { success: true };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur lors du signalement',
      };
    }
  }
}

export const ApiService = new ApiServiceClass();
export type { User, Need, Offer, ApiResponse };
