# 🔑 Guide de Configuration des Clés API pour Nowee

Ce guide vous explique étape par étape comment obtenir et configurer toutes les clés API nécessaires pour faire fonctionner votre bot Nowee.

## 📋 Checklist rapide

- [ ] Clé OpenAI configurée
- [ ] Compte Twilio cré<PERSON>
- [ ] WhatsApp Sandbox activé
- [ ] Webhook configuré avec ngrok
- [ ] Bot testé avec un message

---

## 🤖 1. Configuration OpenAI

### Étape 1: Créer un compte OpenAI
1. Allez sur [https://platform.openai.com](https://platform.openai.com)
2. Cliquez sur "Sign up" si vous n'avez pas de compte
3. Vérifiez votre email et connectez-vous

### Étape 2: Obtenir votre clé API
1. Une fois connecté, allez dans [API Keys](https://platform.openai.com/api-keys)
2. C<PERSON>z sur "Create new secret key"
3. Donnez un nom à votre clé (ex: "Now<PERSON> Bot")
4. **IMPORTANT**: Co<PERSON>z immédiatement la clé, elle ne sera plus visible !

### Étape 3: Ajouter des crédits
1. Allez dans [Billing](https://platform.openai.com/account/billing)
2. Ajoutez une méthode de paiement
3. Ajoutez au minimum 5-10$ de crédit pour commencer

### Étape 4: Configurer dans .env
```env
OPENAI_API_KEY=sk-proj-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

---

## 📱 2. Configuration Twilio (WhatsApp)

### Étape 1: Créer un compte Twilio
1. Allez sur [https://www.twilio.com/try-twilio](https://www.twilio.com/try-twilio)
2. Inscrivez-vous avec votre email
3. Vérifiez votre numéro de téléphone
4. Complétez le questionnaire d'onboarding

### Étape 2: Obtenir vos identifiants
1. Dans le [Console Dashboard](https://console.twilio.com/)
2. Notez votre **Account SID** (commence par AC...)
3. Cliquez sur "Show" pour révéler votre **Auth Token**
4. Copiez ces deux valeurs

### Étape 3: Activer WhatsApp Sandbox
1. Dans la console Twilio, allez dans **Messaging** > **Try it out** > **Send a WhatsApp message**
2. Ou directement: [WhatsApp Sandbox](https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn)
3. Notez le numéro de sandbox (généralement ****** 523 8886)
4. Notez le code de jointure (ex: "join xxxxx-xxxxx")

### Étape 4: Configurer dans .env
```env
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
```

---

## 🌐 3. Configuration du Webhook avec ngrok

### Étape 1: Installer ngrok
1. Téléchargez ngrok: [https://ngrok.com/download](https://ngrok.com/download)
2. Décompressez et placez ngrok.exe dans votre PATH
3. Ou installez via npm: `npm install -g ngrok`

### Étape 2: Créer un compte ngrok (optionnel mais recommandé)
1. Créez un compte sur [https://ngrok.com](https://ngrok.com)
2. Obtenez votre authtoken
3. Configurez: `ngrok authtoken YOUR_AUTHTOKEN`

### Étape 3: Démarrer le tunnel
```bash
# Dans un terminal séparé
ngrok http 3000
```

### Étape 4: Copier l'URL HTTPS
- ngrok affichera quelque chose comme: `https://abc123.ngrok.io`
- Copiez cette URL HTTPS (pas HTTP !)

### Étape 5: Configurer le webhook Twilio
1. Retournez dans la console Twilio
2. Allez dans **Messaging** > **Settings** > **WhatsApp sandbox settings**
3. Dans "When a message comes in", collez: `https://abc123.ngrok.io/webhook`
4. Méthode: POST
5. Sauvegardez

---

## 🧪 4. Test de Configuration

### Étape 1: Démarrer le bot
```bash
npm start
```

### Étape 2: Rejoindre le sandbox WhatsApp
1. Envoyez un message WhatsApp au numéro sandbox (****** 523 8886)
2. Message: `join xxxxx-xxxxx` (remplacez par votre code)
3. Vous devriez recevoir une confirmation

### Étape 3: Tester Nowee
1. Envoyez: `Bonjour`
2. Vous devriez recevoir le message d'accueil de Nowee
3. Testez avec: `J'ai besoin d'une perceuse à Dakar`

---

## 🔧 Dépannage

### Problème: "Invalid API Key" (OpenAI)
- ✅ Vérifiez que la clé commence par `sk-proj-` ou `sk-`
- ✅ Vérifiez qu'il n'y a pas d'espaces avant/après
- ✅ Vérifiez que vous avez des crédits sur votre compte

### Problème: "Authentication failed" (Twilio)
- ✅ Vérifiez Account SID (commence par AC)
- ✅ Vérifiez Auth Token (32 caractères)
- ✅ Vérifiez que le compte Twilio est actif

### Problème: "Webhook not receiving messages"
- ✅ Vérifiez que ngrok est démarré
- ✅ Vérifiez l'URL webhook dans Twilio (HTTPS obligatoire)
- ✅ Vérifiez que le bot est démarré sur le port 3000
- ✅ Testez l'URL: `https://votre-url.ngrok.io/health`

### Problème: "No response from bot"
- ✅ Vérifiez les logs du serveur
- ✅ Vérifiez que vous avez rejoint le sandbox
- ✅ Testez avec un message simple comme "bonjour"

---

## 💰 Coûts estimés

### OpenAI
- **GPT-4o-mini**: ~0.15$ pour 1000 messages
- **Budget recommandé**: 10$ pour commencer (≈ 65,000 messages)

### Twilio
- **WhatsApp Sandbox**: Gratuit pour les tests
- **Messages WhatsApp**: ~0.005$ par message en production
- **Numéro dédié**: ~1$ par mois

### Total mensuel estimé (100 utilisateurs actifs)
- **Phase test**: ~5-15$ par mois
- **Production**: ~50-100$ par mois

---

## 🔒 Sécurité

### ⚠️ IMPORTANT
- **Jamais** commiter le fichier `.env` dans git
- Utilisez des clés API différentes pour dev/prod
- Régénérez les clés si elles sont compromises
- Surveillez l'usage pour détecter les abus

### Bonnes pratiques
- Limitez les permissions des clés API
- Configurez des alertes de facturation
- Loggez les accès pour audit
- Chiffrez les données sensibles

---

## 📞 Support

Si vous rencontrez des problèmes:

1. **Vérifiez les logs** du serveur
2. **Testez les endpoints** individuellement
3. **Consultez la documentation** Twilio/OpenAI
4. **Contactez le support** si nécessaire

Bon développement avec Nowee ! 🚀
