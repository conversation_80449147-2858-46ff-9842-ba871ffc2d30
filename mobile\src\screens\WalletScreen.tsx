/**
 * Écran de Portefeuille Nowee
 * Interface visuelle pour gérer les NoweeCoins et crédits temps
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import { <PERSON><PERSON><PERSON>, PieChart } from 'react-native-chart-kit';
import { useTheme } from '../services/ThemeService';
import { EconomyService } from '../services/EconomyService';
import { useAuth } from '../services/AuthService';

const { width: screenWidth } = Dimensions.get('window');

interface WalletData {
  nowee_coins: number;
  time_credits: number;
  total_earned: number;
  total_spent: number;
  reputation_bonus: number;
}

interface Transaction {
  id: string;
  type: string;
  amount: number;
  description: string;
  created_at: string;
  is_incoming: boolean;
}

const WalletScreen: React.FC = () => {
  const { colors } = useTheme();
  const { user } = useAuth();
  const [wallet, setWallet] = useState<WalletData | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'transactions' | 'stats'>('overview');
  
  // Animations
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);

  useEffect(() => {
    loadWalletData();
    
    // Animation d'entrée
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const loadWalletData = async () => {
    try {
      setLoading(true);
      
      if (!user?.phone) {
        throw new Error('Utilisateur non connecté');
      }

      // Charger le portefeuille
      const walletData = await EconomyService.getWallet(user.phone);
      setWallet(walletData);

      // Charger les transactions
      const transactionData = await EconomyService.getTransactions(user.phone);
      setTransactions(transactionData);

    } catch (error) {
      console.error('Erreur chargement portefeuille:', error);
      Alert.alert('Erreur', 'Impossible de charger les données du portefeuille');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadWalletData();
  };

  const handleTransfer = () => {
    // TODO: Naviguer vers l'écran de transfert
    Alert.alert('Transfert', 'Fonctionnalité de transfert à venir');
  };

  const handleReceive = () => {
    // TODO: Afficher QR code pour recevoir des coins
    Alert.alert('Recevoir', 'Fonctionnalité de réception à venir');
  };

  const renderWalletCard = () => (
    <Animated.View 
      style={[
        styles.walletCard,
        { 
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}
    >
      <LinearGradient
        colors={[colors.primary, colors.secondary]}
        style={styles.walletGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.walletHeader}>
          <Text style={styles.walletTitle}>Mon Portefeuille Nowee</Text>
          <Icon name="account-balance-wallet" size={24} color="#FFFFFF" />
        </View>
        
        <View style={styles.balanceContainer}>
          <View style={styles.mainBalance}>
            <Text style={styles.balanceLabel}>NoweeCoins</Text>
            <Text style={styles.balanceAmount}>
              {wallet?.nowee_coins.toFixed(2) || '0.00'}
            </Text>
          </View>
          
          <View style={styles.timeCredits}>
            <Text style={styles.timeLabel}>Crédits Temps</Text>
            <Text style={styles.timeAmount}>
              {wallet?.time_credits.toFixed(1) || '0.0'}h
            </Text>
          </View>
        </View>

        <View style={styles.walletActions}>
          <TouchableOpacity style={styles.actionButton} onPress={handleTransfer}>
            <Icon name="send" size={20} color="#FFFFFF" />
            <Text style={styles.actionText}>Envoyer</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={handleReceive}>
            <Icon name="qr-code" size={20} color="#FFFFFF" />
            <Text style={styles.actionText}>Recevoir</Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </Animated.View>
  );

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {[
        { key: 'overview', label: 'Vue d\'ensemble', icon: 'dashboard' },
        { key: 'transactions', label: 'Transactions', icon: 'list' },
        { key: 'stats', label: 'Statistiques', icon: 'bar-chart' },
      ].map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tabItem,
            selectedTab === tab.key && styles.tabItemActive
          ]}
          onPress={() => setSelectedTab(tab.key as any)}
        >
          <Icon 
            name={tab.icon} 
            size={20} 
            color={selectedTab === tab.key ? colors.primary : colors.textSecondary} 
          />
          <Text 
            style={[
              styles.tabLabel,
              { color: selectedTab === tab.key ? colors.primary : colors.textSecondary }
            ]}
          >
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderOverview = () => (
    <View style={styles.overviewContainer}>
      <View style={styles.statsGrid}>
        <View style={[styles.statCard, { backgroundColor: colors.surface }]}>
          <Icon name="trending-up" size={24} color={colors.success} />
          <Text style={[styles.statValue, { color: colors.text }]}>
            {wallet?.total_earned.toFixed(0) || '0'}
          </Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
            Total Gagné
          </Text>
        </View>
        
        <View style={[styles.statCard, { backgroundColor: colors.surface }]}>
          <Icon name="trending-down" size={24} color={colors.warning} />
          <Text style={[styles.statValue, { color: colors.text }]}>
            {wallet?.total_spent.toFixed(0) || '0'}
          </Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
            Total Dépensé
          </Text>
        </View>
        
        <View style={[styles.statCard, { backgroundColor: colors.surface }]}>
          <Icon name="star" size={24} color={colors.accent} />
          <Text style={[styles.statValue, { color: colors.text }]}>
            {wallet?.reputation_bonus.toFixed(1) || '0.0'}
          </Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
            Bonus Réputation
          </Text>
        </View>
      </View>
    </View>
  );

  const renderTransactions = () => (
    <View style={styles.transactionsContainer}>
      {transactions.length === 0 ? (
        <View style={styles.emptyState}>
          <Icon name="receipt" size={48} color={colors.textSecondary} />
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            Aucune transaction pour le moment
          </Text>
        </View>
      ) : (
        transactions.map((transaction) => (
          <View key={transaction.id} style={[styles.transactionItem, { backgroundColor: colors.surface }]}>
            <View style={styles.transactionIcon}>
              <Icon 
                name={transaction.is_incoming ? 'arrow-downward' : 'arrow-upward'} 
                size={20} 
                color={transaction.is_incoming ? colors.success : colors.warning} 
              />
            </View>
            
            <View style={styles.transactionDetails}>
              <Text style={[styles.transactionDescription, { color: colors.text }]}>
                {transaction.description}
              </Text>
              <Text style={[styles.transactionDate, { color: colors.textSecondary }]}>
                {new Date(transaction.created_at).toLocaleDateString('fr-FR')}
              </Text>
            </View>
            
            <Text 
              style={[
                styles.transactionAmount,
                { color: transaction.is_incoming ? colors.success : colors.warning }
              ]}
            >
              {transaction.is_incoming ? '+' : '-'}{transaction.amount.toFixed(2)}
            </Text>
          </View>
        ))
      )}
    </View>
  );

  const renderStats = () => {
    if (!wallet) return null;

    const chartData = {
      labels: ['Gagné', 'Dépensé', 'Bonus'],
      datasets: [{
        data: [
          wallet.total_earned,
          wallet.total_spent,
          wallet.reputation_bonus
        ]
      }]
    };

    return (
      <View style={styles.statsContainer}>
        <Text style={[styles.chartTitle, { color: colors.text }]}>
          Répartition des NoweeCoins
        </Text>
        
        <LineChart
          data={chartData}
          width={screenWidth - 40}
          height={220}
          chartConfig={{
            backgroundColor: colors.surface,
            backgroundGradientFrom: colors.surface,
            backgroundGradientTo: colors.surface,
            decimalPlaces: 0,
            color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
            labelColor: (opacity = 1) => colors.text,
            style: {
              borderRadius: 16
            },
            propsForDots: {
              r: '6',
              strokeWidth: '2',
              stroke: colors.primary
            }
          }}
          bezier
          style={styles.chart}
        />
      </View>
    );
  };

  const renderContent = () => {
    switch (selectedTab) {
      case 'overview':
        return renderOverview();
      case 'transactions':
        return renderTransactions();
      case 'stats':
        return renderStats();
      default:
        return renderOverview();
    }
  };

  if (loading && !wallet) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Icon name="account-balance-wallet" size={48} color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Chargement du portefeuille...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderWalletCard()}
        {renderTabBar()}
        {renderContent()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  walletCard: {
    margin: 20,
    borderRadius: 16,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  walletGradient: {
    padding: 24,
    borderRadius: 16,
  },
  walletHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  walletTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  balanceContainer: {
    marginBottom: 24,
  },
  mainBalance: {
    marginBottom: 12,
  },
  balanceLabel: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.8,
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  timeCredits: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeLabel: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.8,
  },
  timeAmount: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  walletActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
  },
  actionText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontWeight: '600',
  },
  tabBar: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 4,
  },
  tabItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  tabItemActive: {
    backgroundColor: '#FFFFFF',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tabLabel: {
    marginLeft: 6,
    fontSize: 12,
    fontWeight: '600',
  },
  overviewContainer: {
    paddingHorizontal: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 4,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
  },
  transactionsContainer: {
    paddingHorizontal: 20,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 8,
    borderRadius: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 14,
    fontWeight: '600',
  },
  transactionDate: {
    fontSize: 12,
    marginTop: 2,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  statsContainer: {
    paddingHorizontal: 20,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
});

export default WalletScreen;
