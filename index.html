<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nowee - Entraide Locale Intelligente</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo {
            font-size: 3em;
            margin-bottom: 10px;
        }
        
        .tagline {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature {
            text-align: center;
            padding: 20px;
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .feature h3 {
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .feature p {
            opacity: 0.8;
            font-size: 0.9em;
        }
        
        .test-section {
            margin-top: 30px;
        }
        
        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
        }
        
        .btn-primary:hover {
            background: white;
        }
        
        .result-area {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            opacity: 0.7;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🤝 Nowee</div>
            <div class="tagline">Entraide Locale Intelligente avec IA</div>
            <div class="status">
                <div class="status-dot"></div>
                <span id="status-text">Connexion au serveur...</span>
            </div>
        </div>

        <!-- Features -->
        <div class="card">
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🤖</div>
                    <h3>IA Contextuelle</h3>
                    <p>Intelligence artificielle adaptée à la culture sénégalaise</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">💰</div>
                    <h3>NoweeCoins</h3>
                    <p>Économie alternative basée sur l'entraide</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🗺️</div>
                    <h3>Géolocalisation</h3>
                    <p>Trouvez de l'aide près de chez vous</p>
                </div>
            </div>
        </div>

        <!-- Test Section -->
        <div class="card test-section">
            <h2 style="text-align: center; margin-bottom: 20px;">🧪 Testez Nowee</h2>
            
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testAI()">🤖 Tester l'IA</button>
                <button class="btn" onclick="testEconomy()">💰 Voir Économie</button>
                <button class="btn" onclick="testStats()">📊 Statistiques</button>
                <button class="btn" onclick="testHealth()">🏥 Santé API</button>
            </div>
            
            <div class="result-area" id="results">
                Cliquez sur un bouton pour tester les fonctionnalités...
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Nowee - Révolutionnons l'entraide ensemble 🌍</p>
            <p>Version originale simple et fonctionnelle</p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        
        // Vérification initiale
        document.addEventListener('DOMContentLoaded', () => {
            checkServerStatus();
        });
        
        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (data.status === 'OK') {
                    document.getElementById('status-text').textContent = 'Serveur connecté ✅';
                    log('✅ Serveur Nowee connecté et opérationnel');
                } else {
                    throw new Error('Serveur non disponible');
                }
            } catch (error) {
                document.getElementById('status-text').textContent = 'Serveur déconnecté ❌';
                log('❌ Impossible de se connecter au serveur Nowee');
            }
        }
        
        function log(message) {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }
        
        async function testAI() {
            log('🤖 Test de l\'IA Nowee...');
            
            try {
                const response = await fetch(`${API_BASE}/api/chat/ai`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: "Salut Nowee ! J'ai besoin d'aide pour déménager ce weekend",
                        phone: '+221701234567'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`💬 IA: ${data.response.substring(0, 100)}...`);
                    log(`📊 Modèle: ${data.model} | Temps: ${data.processingTime}ms`);
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                log(`❌ Erreur IA: ${error.message}`);
            }
        }
        
        async function testEconomy() {
            log('💰 Test de l\'économie NoweeCoins...');
            
            try {
                const response = await fetch(`${API_BASE}/api/economy/wallet/+221701234567`);
                const data = await response.json();
                
                if (data.success) {
                    log(`💰 Portefeuille: ${data.noweeCoins} coins + ${data.timeCredits}h`);
                    log(`⭐ Réputation: ${data.reputation}/5`);
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                log(`❌ Erreur économie: ${error.message}`);
            }
        }
        
        async function testStats() {
            log('📊 Récupération des statistiques...');
            
            try {
                const response = await fetch(`${API_BASE}/api/stats/advanced`);
                const data = await response.json();
                
                if (data.success) {
                    log(`👥 Utilisateurs: ${data.stats.totalUsers} (${data.stats.activeUsers} actifs)`);
                    log(`🤝 Aides: ${data.stats.totalHelps} réalisées`);
                    log(`💰 Économie: ${data.stats.totalCoins} NoweeCoins en circulation`);
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                log(`❌ Erreur stats: ${error.message}`);
            }
        }
        
        async function testHealth() {
            log('🏥 Vérification de la santé du système...');
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                log(`✅ Statut: ${data.status}`);
                log(`🔧 Version: ${data.version}`);
                log(`🌍 Environnement: ${data.environment}`);
                log(`🤖 OpenAI: ${data.apis.openai ? 'Configuré' : 'Non configuré'}`);
                log(`📱 Twilio: ${data.apis.twilio ? 'Configuré' : 'Non configuré'}`);
            } catch (error) {
                log(`❌ Erreur santé: ${error.message}`);
            }
        }
    </script>
</body>
</html>
