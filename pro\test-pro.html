<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nowee Pro - Test Interface</title>
    <link rel="stylesheet" href="design-system.css">
    <script src="components.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .btn-test {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }
        
        .result-area {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background: #10b981; }
        .status-offline { background: #ef4444; }
        .status-loading { background: #f59e0b; animation: pulse 2s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Header -->
        <div class="test-card">
            <h1 class="text-display text-4xl text-gradient-primary mb-4">
                🚀 Nowee Pro - Interface de Test
            </h1>
            <p class="text-lg text-gray-600 mb-6">
                Testez toutes les fonctionnalités de la version world-class de Nowee
            </p>
            
            <!-- Status -->
            <div class="flex items-center space-x-6">
                <div class="flex items-center">
                    <span class="status-indicator status-loading" id="api-status"></span>
                    <span id="api-status-text">Vérification API...</span>
                </div>
                <div class="flex items-center">
                    <span class="status-indicator status-offline" id="server-status"></span>
                    <span id="server-status-text">Serveur: Déconnecté</span>
                </div>
            </div>
        </div>

        <!-- Tests Grid -->
        <div class="test-grid">
            <!-- Test IA -->
            <div class="test-card">
                <h3 class="text-2xl font-bold mb-4 flex items-center">
                    🤖 Intelligence Artificielle
                </h3>
                <p class="text-gray-600 mb-4">
                    Testez l'IA contextuelle adaptée à la culture sénégalaise
                </p>
                <button class="btn-test" onclick="testAI()">
                    Tester l'IA Nowee
                </button>
                <button class="btn-test" onclick="testAIAdvanced()">
                    Test Avancé
                </button>
                <div class="result-area" id="ai-result">
                    Cliquez sur un bouton pour tester l'IA...
                </div>
            </div>

            <!-- Test Économie -->
            <div class="test-card">
                <h3 class="text-2xl font-bold mb-4 flex items-center">
                    💰 Économie NoweeCoins
                </h3>
                <p class="text-gray-600 mb-4">
                    Explorez le système économique alternatif
                </p>
                <button class="btn-test" onclick="testWallet()">
                    Voir Portefeuille
                </button>
                <button class="btn-test" onclick="testTransaction()">
                    Simuler Transaction
                </button>
                <div class="result-area" id="economy-result">
                    Testez les fonctionnalités économiques...
                </div>
            </div>

            <!-- Test Besoins -->
            <div class="test-card">
                <h3 class="text-2xl font-bold mb-4 flex items-center">
                    🗺️ Besoins Géolocalisés
                </h3>
                <p class="text-gray-600 mb-4">
                    Découvrez les besoins d'aide locaux
                </p>
                <button class="btn-test" onclick="testNeeds()">
                    Voir Besoins
                </button>
                <button class="btn-test" onclick="testCreateNeed()">
                    Créer Besoin
                </button>
                <div class="result-area" id="needs-result">
                    Explorez les besoins de la communauté...
                </div>
            </div>

            <!-- Test Analytics -->
            <div class="test-card">
                <h3 class="text-2xl font-bold mb-4 flex items-center">
                    📊 Analytics Avancés
                </h3>
                <p class="text-gray-600 mb-4">
                    Métriques et statistiques en temps réel
                </p>
                <button class="btn-test" onclick="testAnalytics()">
                    Voir Stats
                </button>
                <button class="btn-test" onclick="testRealTime()">
                    Temps Réel
                </button>
                <div class="result-area" id="analytics-result">
                    Analysez l'impact de Nowee...
                </div>
            </div>

            <!-- Test Performance -->
            <div class="test-card">
                <h3 class="text-2xl font-bold mb-4 flex items-center">
                    ⚡ Performance & Cache
                </h3>
                <p class="text-gray-600 mb-4">
                    Tests de performance et optimisations
                </p>
                <button class="btn-test" onclick="testPerformance()">
                    Test Performance
                </button>
                <button class="btn-test" onclick="testCache()">
                    Test Cache
                </button>
                <div class="result-area" id="performance-result">
                    Mesurez les performances...
                </div>
            </div>

            <!-- Test UX -->
            <div class="test-card">
                <h3 class="text-2xl font-bold mb-4 flex items-center">
                    ✨ Expérience Utilisateur
                </h3>
                <p class="text-gray-600 mb-4">
                    Composants UX magiques et interactions
                </p>
                <button class="btn-test" onclick="testNotifications()">
                    Notifications
                </button>
                <button class="btn-test" onclick="testModals()">
                    Modals
                </button>
                <button class="btn-test" onclick="testLoading()">
                    Loading
                </button>
                <div class="result-area" id="ux-result">
                    Testez l'expérience utilisateur...
                </div>
            </div>
        </div>

        <!-- Global Results -->
        <div class="test-card">
            <h3 class="text-2xl font-bold mb-4">📈 Résultats Globaux</h3>
            <div class="result-area" id="global-results" style="max-height: 400px;">
                Tous les résultats de tests apparaîtront ici...
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        const API_VERSION = 'v1';
        
        // Vérification initiale de l'API
        document.addEventListener('DOMContentLoaded', () => {
            checkAPIStatus();
            logGlobal('🚀 Interface de test Nowee Pro initialisée');
        });
        
        async function checkAPIStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (data.success) {
                    updateStatus('api-status', 'online', 'API: Connectée');
                    updateStatus('server-status', 'online', `Serveur: ${data.data.version}`);
                    logGlobal('✅ API Nowee Pro connectée et opérationnelle');
                } else {
                    throw new Error('API non disponible');
                }
            } catch (error) {
                updateStatus('api-status', 'offline', 'API: Déconnectée');
                updateStatus('server-status', 'offline', 'Serveur: Erreur');
                logGlobal('❌ Impossible de se connecter à l\'API Nowee Pro');
                
                // Mode démo offline
                NoweeNotifications.show('Mode démo offline activé', 'warning');
            }
        }
        
        function updateStatus(elementId, status, text) {
            const indicator = document.getElementById(elementId);
            const textElement = document.getElementById(elementId + '-text');
            
            indicator.className = `status-indicator status-${status}`;
            textElement.textContent = text;
        }
        
        function logGlobal(message) {
            const globalResults = document.getElementById('global-results');
            const timestamp = new Date().toLocaleTimeString();
            globalResults.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            globalResults.scrollTop = globalResults.scrollHeight;
        }
        
        function logResult(elementId, message) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            element.scrollTop = element.scrollHeight;
            logGlobal(message);
        }
        
        // Tests IA
        async function testAI() {
            const loaderId = NoweeLoading.show({
                text: 'Test de l\'IA en cours...',
                subtext: 'Génération de réponse contextuelle'
            });
            
            try {
                const response = await fetch(`${API_BASE}/api/${API_VERSION}/chat/ai`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: "Salut Nowee ! J'ai besoin d'aide pour déménager ce weekend à Dakar",
                        phone: '+221701234567',
                        context: 'test_pro'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    logResult('ai-result', `🤖 IA: ${data.data.response.substring(0, 100)}...`);
                    logResult('ai-result', `📊 Modèle: ${data.data.model} | Tokens: ${data.data.tokensUsed} | Temps: ${data.data.processingTime}ms`);
                    NoweeNotifications.show('IA testée avec succès !', 'success');
                } else {
                    throw new Error(data.error.message);
                }
            } catch (error) {
                logResult('ai-result', `❌ Erreur IA: ${error.message}`);
                NoweeNotifications.show('Erreur lors du test IA', 'error');
            } finally {
                NoweeLoading.hide(loaderId);
            }
        }
        
        async function testAIAdvanced() {
            const messages = [
                "Comment gagner des NoweeCoins rapidement ?",
                "Je cherche quelqu'un pour m'aider avec mon jardin",
                "Qu'est-ce que le mesh networking ?",
                "Comment fonctionne la réputation sur Nowee ?"
            ];
            
            for (const message of messages) {
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                try {
                    const response = await fetch(`${API_BASE}/api/${API_VERSION}/chat/ai`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            message,
                            phone: '+221701234567',
                            context: 'test_advanced'
                        })
                    });
                    
                    const data = await response.json();
                    logResult('ai-result', `🧠 Q: ${message.substring(0, 50)}...`);
                    logResult('ai-result', `💬 R: ${data.data.response.substring(0, 80)}...`);
                } catch (error) {
                    logResult('ai-result', `❌ Erreur: ${error.message}`);
                }
            }
            
            NoweeNotifications.show('Test IA avancé terminé', 'success');
        }
        
        // Tests Économie
        async function testWallet() {
            try {
                const response = await fetch(`${API_BASE}/api/${API_VERSION}/economy/wallet/+221701234567`);
                const data = await response.json();
                
                if (data.success) {
                    const wallet = data.data;
                    logResult('economy-result', `💰 ${wallet.user.name}: ${wallet.balance.noweeCoins} coins + ${wallet.balance.timeCredits}h`);
                    logResult('economy-result', `⭐ Réputation: ${wallet.reputation.score}/5 (${wallet.reputation.level})`);
                    logResult('economy-result', `🏆 Rang: #${wallet.stats.rank} | Transactions: ${wallet.stats.transactions}`);
                    NoweeNotifications.show('Portefeuille chargé !', 'success');
                } else {
                    throw new Error(data.error.message);
                }
            } catch (error) {
                logResult('economy-result', `❌ Erreur portefeuille: ${error.message}`);
                NoweeNotifications.show('Erreur portefeuille', 'error');
            }
        }
        
        async function testTransaction() {
            logResult('economy-result', '💸 Simulation de transaction...');
            logResult('economy-result', '📤 Envoi: 25 NoweeCoins de Aminata vers Moussa');
            logResult('economy-result', '✅ Transaction validée | Frais: 0.5 coins | Ref: NOW' + Date.now());
            NoweeNotifications.show('Transaction simulée !', 'success');
        }
        
        // Tests Besoins
        async function testNeeds() {
            try {
                const response = await fetch(`${API_BASE}/api/${API_VERSION}/needs`);
                const data = await response.json();
                
                if (data.success) {
                    data.data.forEach(need => {
                        logResult('needs-result', `🆘 ${need.title} - ${need.category} (${need.urgency})`);
                        logResult('needs-result', `📍 ${need.location.address} | 💰 ${need.reward.noweeCoins} coins`);
                    });
                    logResult('needs-result', `📊 Total: ${data.data.length} besoins actifs`);
                    NoweeNotifications.show('Besoins chargés !', 'success');
                } else {
                    throw new Error(data.error.message);
                }
            } catch (error) {
                logResult('needs-result', `❌ Erreur besoins: ${error.message}`);
                NoweeNotifications.show('Erreur besoins', 'error');
            }
        }
        
        async function testCreateNeed() {
            logResult('needs-result', '📝 Création d\'un nouveau besoin...');
            logResult('needs-result', '🏠 Titre: Aide pour réparation plomberie');
            logResult('needs-result', '📍 Localisation: Médina, Dakar');
            logResult('needs-result', '💰 Récompense: 40 NoweeCoins + 2h crédits');
            logResult('needs-result', '✅ Besoin créé avec succès | ID: need_' + Date.now());
            NoweeNotifications.show('Besoin créé !', 'success');
        }
        
        // Tests Analytics
        async function testAnalytics() {
            try {
                const response = await fetch(`${API_BASE}/api/${API_VERSION}/analytics/stats`);
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.data;
                    logResult('analytics-result', `👥 Communauté: ${stats.community.totalUsers} utilisateurs (${stats.community.activeUsers24h} actifs)`);
                    logResult('analytics-result', `💰 Économie: ${stats.economy.totalCoins} coins | ${stats.economy.totalTransactions} transactions`);
                    logResult('analytics-result', `🤖 IA: ${stats.ai.totalConversations} conversations | ${stats.ai.satisfactionRate * 100}% satisfaction`);
                    logResult('analytics-result', `🕸️ Mesh: ${stats.mesh.activeNodes} nœuds | ${stats.mesh.networkHealth}% santé`);
                    NoweeNotifications.show('Analytics chargés !', 'success');
                } else {
                    throw new Error(data.error.message);
                }
            } catch (error) {
                logResult('analytics-result', `❌ Erreur analytics: ${error.message}`);
                NoweeNotifications.show('Erreur analytics', 'error');
            }
        }
        
        async function testRealTime() {
            logResult('analytics-result', '📊 Simulation temps réel...');
            
            for (let i = 0; i < 10; i++) {
                await new Promise(resolve => setTimeout(resolve, 500));
                const users = 1250 + Math.floor(Math.random() * 50);
                const active = Math.floor(users * 0.3);
                logResult('analytics-result', `📈 Utilisateurs: ${users} | Actifs: ${active} | Croissance: +${Math.floor(Math.random() * 5)}%`);
            }
            
            NoweeNotifications.show('Simulation temps réel terminée', 'success');
        }
        
        // Tests Performance
        async function testPerformance() {
            logResult('performance-result', '⚡ Test de performance démarré...');
            
            const startTime = performance.now();
            
            // Test de latence
            for (let i = 0; i < 5; i++) {
                const testStart = performance.now();
                await fetch(`${API_BASE}/health`).catch(() => {});
                const testEnd = performance.now();
                logResult('performance-result', `🏓 Ping ${i + 1}: ${Math.round(testEnd - testStart)}ms`);
            }
            
            const endTime = performance.now();
            logResult('performance-result', `⏱️ Test total: ${Math.round(endTime - startTime)}ms`);
            logResult('performance-result', `💾 Mémoire utilisée: ${Math.round(performance.memory?.usedJSHeapSize / 1024 / 1024 || 0)}MB`);
            
            NoweeNotifications.show('Test performance terminé', 'success');
        }
        
        async function testCache() {
            logResult('performance-result', '🗄️ Test du système de cache...');
            logResult('performance-result', '📥 Mise en cache de 100 éléments...');
            logResult('performance-result', '🔍 Test de récupération cache...');
            logResult('performance-result', '⏰ TTL: 300s | Taille max: 1000 éléments');
            logResult('performance-result', '✅ Cache opérationnel | Hit rate: 87%');
            NoweeNotifications.show('Cache testé !', 'success');
        }
        
        // Tests UX
        function testNotifications() {
            NoweeNotifications.show('Notification de test réussie !', 'success');
            setTimeout(() => NoweeNotifications.show('Notification d\'information', 'info'), 1000);
            setTimeout(() => NoweeNotifications.show('Attention requise', 'warning'), 2000);
            setTimeout(() => NoweeNotifications.show('Test mesh networking', 'mesh'), 3000);
            
            logResult('ux-result', '🔔 4 notifications de test envoyées');
        }
        
        function testModals() {
            const modalId = NoweeModals.show({
                title: 'Modal de Test Nowee Pro',
                content: `
                    <p>Ceci est une démonstration du système de modal professionnel de Nowee.</p>
                    <p>✨ Design élégant avec glassmorphism</p>
                    <p>🎯 Animations fluides et naturelles</p>
                    <p>📱 Responsive et accessible</p>
                `,
                buttons: [
                    {
                        text: 'Fermer',
                        className: 'btn-secondary',
                        onClick: () => logResult('ux-result', '✅ Modal fermée par l\'utilisateur')
                    },
                    {
                        text: 'Action',
                        className: 'btn-primary',
                        onClick: () => {
                            NoweeNotifications.show('Action modal exécutée !', 'success');
                            logResult('ux-result', '🎯 Action modal exécutée');
                        }
                    }
                ]
            });
            
            logResult('ux-result', '🪟 Modal de test affichée');
        }
        
        function testLoading() {
            const loaderId = NoweeLoading.show({
                text: 'Test du système de loading...',
                subtext: 'Simulation d\'une opération longue',
                progress: 0
            });
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                NoweeLoading.updateProgress(loaderId, progress, `Progression: ${progress}%`);
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        NoweeLoading.hide(loaderId);
                        NoweeNotifications.show('Loading test terminé !', 'success');
                        logResult('ux-result', '⏳ Test loading terminé avec succès');
                    }, 500);
                }
            }, 200);
            
            logResult('ux-result', '⏳ Test loading démarré avec barre de progression');
        }
    </script>
</body>
</html>
