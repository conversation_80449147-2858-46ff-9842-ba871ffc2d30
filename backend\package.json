{"name": "nowee-api-production", "version": "1.0.0", "description": "API de production pour Nowee", "main": "server-nowee.js", "scripts": {"start": "node server-nowee.js", "dev": "nodemon server-nowee.js", "build": "echo 'Build completed'", "test": "jest"}, "engines": {"node": "18.x", "npm": "9.x"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.2.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "openai": "^4.20.1", "redis": "^5.6.0", "socket.io": "^4.7.4", "twilio": "^4.19.0", "winston": "^3.17.0"}, "keywords": ["nowee", "entraide", "api", "production"], "author": "Nowee Team", "license": "MIT"}