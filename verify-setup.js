#!/usr/bin/env node

/**
 * Script de vérification de la configuration Nowee
 * Teste les clés API et la connectivité
 * Exécutez avec: node verify-setup.js
 */

import 'dotenv/config';
import OpenAI from 'openai';
import twilio from 'twilio';

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.bold}${colors.blue}\n🔍 ${msg}${colors.reset}`)
};

// Fonction pour vérifier les variables d'environnement
const checkEnvironmentVariables = () => {
  log.title('Vérification des variables d\'environnement');
  
  const requiredVars = [
    'OPENAI_API_KEY',
    'TWILIO_ACCOUNT_SID',
    'TWILIO_AUTH_TOKEN',
    'TWILIO_WHATSAPP_NUMBER'
  ];
  
  const optionalVars = [
    'PORT',
    'NODE_ENV',
    'WEBHOOK_URL'
  ];
  
  let allRequired = true;
  
  // Vérifier les variables requises
  for (const varName of requiredVars) {
    const value = process.env[varName];
    if (value && value !== `your-${varName.toLowerCase().replace(/_/g, '-')}-here` && !value.includes('your-')) {
      log.success(`${varName}: Configuré`);
    } else {
      log.error(`${varName}: Non configuré ou valeur par défaut`);
      allRequired = false;
    }
  }
  
  // Vérifier les variables optionnelles
  for (const varName of optionalVars) {
    const value = process.env[varName];
    if (value) {
      log.info(`${varName}: ${value}`);
    } else {
      log.warning(`${varName}: Non configuré (optionnel)`);
    }
  }
  
  return allRequired;
};

// Fonction pour tester l'API OpenAI
const testOpenAI = async () => {
  log.title('Test de l\'API OpenAI');
  
  try {
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    
    // Test simple avec un prompt minimal
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [{ role: 'user', content: 'Dis simplement "OK" si tu me reçois.' }],
      max_tokens: 10,
      temperature: 0,
    });
    
    const response = completion.choices[0].message.content.trim();
    log.success(`OpenAI répond: "${response}"`);
    log.success('Connexion OpenAI fonctionnelle');
    
    // Vérifier les informations du compte
    try {
      // Note: L'API OpenAI ne fournit plus d'endpoint pour vérifier le solde directement
      log.info('Pour vérifier votre solde, visitez: https://platform.openai.com/account/usage');
    } catch (error) {
      log.warning('Impossible de vérifier le solde du compte');
    }
    
    return true;
  } catch (error) {
    log.error(`Erreur OpenAI: ${error.message}`);
    
    if (error.message.includes('401')) {
      log.error('Clé API OpenAI invalide ou expirée');
    } else if (error.message.includes('429')) {
      log.error('Limite de taux atteinte ou crédits insuffisants');
    } else if (error.message.includes('quota')) {
      log.error('Quota dépassé - ajoutez des crédits à votre compte');
    }
    
    return false;
  }
};

// Fonction pour tester l'API Twilio
const testTwilio = async () => {
  log.title('Test de l\'API Twilio');
  
  try {
    const client = twilio(
      process.env.TWILIO_ACCOUNT_SID,
      process.env.TWILIO_AUTH_TOKEN
    );
    
    // Récupérer les informations du compte
    const account = await client.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
    log.success(`Compte Twilio: ${account.friendlyName}`);
    log.success(`Statut: ${account.status}`);
    
    // Vérifier le numéro WhatsApp
    const whatsappNumber = process.env.TWILIO_WHATSAPP_NUMBER;
    if (whatsappNumber) {
      log.success(`Numéro WhatsApp configuré: ${whatsappNumber}`);
      
      if (whatsappNumber.includes('+***********')) {
        log.info('Vous utilisez le Sandbox WhatsApp (parfait pour les tests)');
        log.info('Pour activer le Sandbox, envoyez "join <code>" au numéro WhatsApp');
      } else {
        log.info('Vous utilisez un numéro WhatsApp dédié');
      }
    }
    
    return true;
  } catch (error) {
    log.error(`Erreur Twilio: ${error.message}`);
    
    if (error.code === 20003) {
      log.error('Account SID ou Auth Token invalide');
    } else if (error.code === 20404) {
      log.error('Compte Twilio non trouvé');
    }
    
    return false;
  }
};

// Fonction pour tester la connectivité réseau
const testNetworkConnectivity = async () => {
  log.title('Test de connectivité réseau');
  
  const testUrls = [
    { name: 'OpenAI API', url: 'https://api.openai.com' },
    { name: 'Twilio API', url: 'https://api.twilio.com' }
  ];
  
  let allConnected = true;
  
  for (const { name, url } of testUrls) {
    try {
      const response = await fetch(url, { method: 'HEAD', timeout: 5000 });
      if (response.ok || response.status === 401) { // 401 est normal sans auth
        log.success(`${name}: Accessible`);
      } else {
        log.warning(`${name}: Réponse inattendue (${response.status})`);
      }
    } catch (error) {
      log.error(`${name}: Non accessible (${error.message})`);
      allConnected = false;
    }
  }
  
  return allConnected;
};

// Fonction pour afficher les prochaines étapes
const showNextSteps = (envOk, openaiOk, twilioOk, networkOk) => {
  log.title('Prochaines étapes');
  
  if (!envOk) {
    log.error('Configurez d\'abord vos variables d\'environnement:');
    console.log('   node setup-keys.js');
    return;
  }
  
  if (!networkOk) {
    log.error('Vérifiez votre connexion internet et vos paramètres de proxy/firewall');
    return;
  }
  
  if (!openaiOk) {
    log.error('Problème avec OpenAI:');
    console.log('   - Vérifiez votre clé API sur https://platform.openai.com/api-keys');
    console.log('   - Ajoutez des crédits sur https://platform.openai.com/account/billing');
  }
  
  if (!twilioOk) {
    log.error('Problème avec Twilio:');
    console.log('   - Vérifiez vos identifiants sur https://console.twilio.com/');
    console.log('   - Activez le WhatsApp Sandbox si nécessaire');
  }
  
  if (envOk && openaiOk && twilioOk && networkOk) {
    log.success('🎉 Tout est configuré correctement !');
    console.log('\nPour démarrer le bot:');
    console.log('   npm start');
    console.log('\nPour exposer le webhook:');
    console.log('   npx ngrok http 3000');
    console.log('\nPour tester le bot:');
    console.log('   1. Rejoignez le Sandbox WhatsApp');
    console.log('   2. Envoyez "bonjour" au bot');
    console.log('   3. Testez avec "J\'ai besoin d\'aide à [votre ville]"');
  }
};

// Fonction principale
const main = async () => {
  console.log(`${colors.bold}${colors.blue}`);
  console.log('🔍 Vérification de la configuration Nowee');
  console.log('==========================================');
  console.log(colors.reset);
  
  // Tests séquentiels
  const envOk = checkEnvironmentVariables();
  const networkOk = await testNetworkConnectivity();
  const openaiOk = envOk ? await testOpenAI() : false;
  const twilioOk = envOk ? await testTwilio() : false;
  
  // Résumé
  console.log(`${colors.bold}\n📊 Résumé de la vérification${colors.reset}`);
  console.log('================================');
  
  const status = (ok) => ok ? `${colors.green}✅ OK${colors.reset}` : `${colors.red}❌ Erreur${colors.reset}`;
  
  console.log(`Variables d'environnement: ${status(envOk)}`);
  console.log(`Connectivité réseau:       ${status(networkOk)}`);
  console.log(`API OpenAI:               ${status(openaiOk)}`);
  console.log(`API Twilio:               ${status(twilioOk)}`);
  
  // Prochaines étapes
  showNextSteps(envOk, openaiOk, twilioOk, networkOk);
};

// Exécuter la vérification
main().catch(error => {
  log.error(`Erreur lors de la vérification: ${error.message}`);
  process.exit(1);
});
