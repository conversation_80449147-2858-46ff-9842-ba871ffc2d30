-- Schéma Économique pour Nowee - Système de Troc et Monnaie Alternative
-- Extension du schéma principal avec les fonctionnalités économiques

-- Table des portefeuilles utilisateurs
CREATE TABLE wallets (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE UNIQUE,
  
  -- Monnai<PERSON>
  nowee_coins DECIMAL(12,2) DEFAULT 100.0, -- Monnaie interne (100 coins de départ)
  time_credits DECIMAL(8,2) DEFAULT 0.0,   -- Crédits temps en heures
  
  -- Statistiques
  total_earned DECIMAL(12,2) DEFAULT 0.0,
  total_spent DECIMAL(12,2) DEFAULT 0.0,
  reputation_bonus DECIMAL(5,2) DEFAULT 0.0, -- Bonus basé sur la réputation
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des transactions économiques
CREATE TABLE transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Participants
  from_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  to_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  
  -- Type de transaction
  transaction_type VARCHAR(30) NOT NULL CHECK (transaction_type IN (
    'COIN_TRANSFER',     -- Transfert de NoweeCoins
    'TIME_EXCHANGE',     -- Échange de temps
    'BARTER_TRADE',      -- Troc direct
    'SERVICE_PAYMENT',   -- Paiement de service
    'REWARD',            -- Récompense système
    'PENALTY',           -- Pénalité
    'INITIAL_BONUS'      -- Bonus initial
  )),
  
  -- Montants
  nowee_coins DECIMAL(12,2) DEFAULT 0.0,
  time_credits DECIMAL(8,2) DEFAULT 0.0,
  
  -- Contexte
  match_id UUID REFERENCES matches(id) ON DELETE SET NULL,
  resource_id UUID REFERENCES resources(id) ON DELETE SET NULL,
  
  -- Description et métadonnées
  description TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  
  -- Statut
  status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED')),
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Table des propositions de troc
CREATE TABLE barter_proposals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Participants
  proposer_id UUID REFERENCES users(id) ON DELETE CASCADE,
  target_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Ressources échangées
  offered_resource_id UUID REFERENCES resources(id) ON DELETE CASCADE,
  requested_resource_id UUID REFERENCES resources(id) ON DELETE CASCADE,
  
  -- Conditions du troc
  exchange_type VARCHAR(20) NOT NULL CHECK (exchange_type IN (
    'DIRECT_BARTER',     -- Troc direct objet contre objet
    'TIME_FOR_OBJECT',   -- Temps contre objet
    'SERVICE_FOR_OBJECT', -- Service contre objet
    'MIXED_EXCHANGE'     -- Échange mixte
  )),
  
  -- Valeurs proposées
  offered_coins DECIMAL(12,2) DEFAULT 0.0,
  offered_time_hours DECIMAL(6,2) DEFAULT 0.0,
  requested_coins DECIMAL(12,2) DEFAULT 0.0,
  requested_time_hours DECIMAL(6,2) DEFAULT 0.0,
  
  -- Conditions et description
  conditions TEXT,
  proposal_message TEXT,
  
  -- Statut
  status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN (
    'PENDING',     -- En attente de réponse
    'ACCEPTED',    -- Accepté
    'REJECTED',    -- Refusé
    'COUNTER',     -- Contre-proposition
    'COMPLETED',   -- Échange terminé
    'CANCELLED'    -- Annulé
  )),
  
  -- Évaluation post-échange
  proposer_rating INTEGER CHECK (proposer_rating BETWEEN 1 AND 5),
  target_rating INTEGER CHECK (target_rating BETWEEN 1 AND 5),
  proposer_feedback TEXT,
  target_feedback TEXT,
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Table des services temporels
CREATE TABLE time_services (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Détails du service
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  category VARCHAR(50) NOT NULL, -- EDUCATION, REPAIR, TRANSPORT, CARE, etc.
  
  -- Tarification temporelle
  hourly_rate_coins DECIMAL(8,2), -- Tarif en NoweeCoins par heure
  min_duration_hours DECIMAL(4,2) DEFAULT 1.0,
  max_duration_hours DECIMAL(4,2) DEFAULT 8.0,
  
  -- Disponibilité
  availability_schedule JSONB, -- Planning de disponibilité
  location_type VARCHAR(20) CHECK (location_type IN ('AT_HOME', 'AT_CLIENT', 'REMOTE', 'FLEXIBLE')),
  
  -- Compétences et certifications
  skills TEXT[],
  certifications JSONB DEFAULT '[]',
  experience_years INTEGER DEFAULT 0,
  
  -- Statut
  is_active BOOLEAN DEFAULT true,
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des réservations de services temporels
CREATE TABLE time_bookings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Participants
  client_id UUID REFERENCES users(id) ON DELETE CASCADE,
  provider_id UUID REFERENCES users(id) ON DELETE CASCADE,
  service_id UUID REFERENCES time_services(id) ON DELETE CASCADE,
  
  -- Détails de la réservation
  scheduled_start TIMESTAMP WITH TIME ZONE NOT NULL,
  scheduled_end TIMESTAMP WITH TIME ZONE NOT NULL,
  duration_hours DECIMAL(4,2) NOT NULL,
  
  -- Coût
  total_coins DECIMAL(10,2) NOT NULL,
  total_time_credits DECIMAL(6,2) DEFAULT 0.0,
  
  -- Statut
  status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN (
    'PENDING',     -- En attente de confirmation
    'CONFIRMED',   -- Confirmé
    'IN_PROGRESS', -- En cours
    'COMPLETED',   -- Terminé
    'CANCELLED',   -- Annulé
    'NO_SHOW'      -- Absence
  )),
  
  -- Évaluation
  client_rating INTEGER CHECK (client_rating BETWEEN 1 AND 5),
  provider_rating INTEGER CHECK (provider_rating BETWEEN 1 AND 5),
  client_feedback TEXT,
  provider_feedback TEXT,
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Table des taux de change et valeurs
CREATE TABLE exchange_rates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Taux de change
  coins_per_hour DECIMAL(8,2) DEFAULT 50.0, -- 1 heure = 50 NoweeCoins
  base_hourly_rate DECIMAL(8,2) DEFAULT 50.0, -- Tarif horaire de base
  
  -- Facteurs d'ajustement
  skill_multipliers JSONB DEFAULT '{}', -- Multiplicateurs par compétence
  urgency_multipliers JSONB DEFAULT '{"1": 1.0, "2": 1.2, "3": 1.5, "4": 2.0, "5": 3.0}',
  reputation_bonus_rate DECIMAL(4,3) DEFAULT 0.1, -- 10% bonus par point de réputation
  
  -- Métadonnées
  effective_from TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by VARCHAR(50) DEFAULT 'SYSTEM',
  is_active BOOLEAN DEFAULT true
);

-- Table des récompenses et pénalités
CREATE TABLE rewards_penalties (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Type d'action
  action_type VARCHAR(30) NOT NULL CHECK (action_type IN (
    'HELP_COMPLETED',    -- Aide terminée avec succès
    'GOOD_RATING',       -- Bonne évaluation reçue
    'COMMUNITY_CONTRIBUTION', -- Contribution communautaire
    'REFERRAL',          -- Parrainage
    'DAILY_LOGIN',       -- Connexion quotidienne
    'LATE_COMPLETION',   -- Retard dans l'aide
    'BAD_RATING',        -- Mauvaise évaluation
    'NO_SHOW',           -- Absence non justifiée
    'SPAM_REPORT'        -- Signalement de spam
  )),
  
  -- Récompense/Pénalité
  coins_amount DECIMAL(10,2) NOT NULL, -- Positif = récompense, Négatif = pénalité
  time_credits_amount DECIMAL(6,2) DEFAULT 0.0,
  
  -- Contexte
  related_match_id UUID REFERENCES matches(id) ON DELETE SET NULL,
  related_transaction_id UUID REFERENCES transactions(id) ON DELETE SET NULL,
  
  -- Description
  description TEXT NOT NULL,
  reason TEXT,
  
  -- Métadonnées
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour performance
CREATE INDEX idx_wallets_user_id ON wallets(user_id);
CREATE INDEX idx_transactions_users ON transactions(from_user_id, to_user_id);
CREATE INDEX idx_transactions_type_status ON transactions(transaction_type, status);
CREATE INDEX idx_transactions_created_at ON transactions(created_at DESC);

CREATE INDEX idx_barter_proposals_users ON barter_proposals(proposer_id, target_user_id);
CREATE INDEX idx_barter_proposals_status ON barter_proposals(status);
CREATE INDEX idx_barter_proposals_expires ON barter_proposals(expires_at);

CREATE INDEX idx_time_services_user_category ON time_services(user_id, category);
CREATE INDEX idx_time_services_active ON time_services(is_active);

CREATE INDEX idx_time_bookings_participants ON time_bookings(client_id, provider_id);
CREATE INDEX idx_time_bookings_schedule ON time_bookings(scheduled_start, scheduled_end);
CREATE INDEX idx_time_bookings_status ON time_bookings(status);

CREATE INDEX idx_rewards_penalties_user ON rewards_penalties(user_id, created_at DESC);
CREATE INDEX idx_rewards_penalties_type ON rewards_penalties(action_type);

-- Triggers pour mise à jour automatique
CREATE TRIGGER update_wallets_updated_at BEFORE UPDATE ON wallets
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_barter_proposals_updated_at BEFORE UPDATE ON barter_proposals
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_time_services_updated_at BEFORE UPDATE ON time_services
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_time_bookings_updated_at BEFORE UPDATE ON time_bookings
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Fonctions économiques

-- Fonction de calcul du coût d'un service
CREATE OR REPLACE FUNCTION calculate_service_cost(
  base_rate DECIMAL,
  duration_hours DECIMAL,
  urgency INTEGER DEFAULT 1,
  provider_rating DECIMAL DEFAULT 0
)
RETURNS DECIMAL AS $$
DECLARE
  urgency_multiplier DECIMAL;
  reputation_bonus DECIMAL;
  total_cost DECIMAL;
BEGIN
  -- Multiplicateur d'urgence
  urgency_multiplier := CASE urgency
    WHEN 1 THEN 1.0
    WHEN 2 THEN 1.2
    WHEN 3 THEN 1.5
    WHEN 4 THEN 2.0
    WHEN 5 THEN 3.0
    ELSE 1.0
  END;
  
  -- Bonus de réputation (10% par point au-dessus de 3)
  reputation_bonus := GREATEST(0, (provider_rating - 3.0) * 0.1);
  
  -- Calcul du coût total
  total_cost := base_rate * duration_hours * urgency_multiplier * (1 + reputation_bonus);
  
  RETURN ROUND(total_cost, 2);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Fonction de mise à jour du portefeuille
CREATE OR REPLACE FUNCTION update_wallet_balance(
  p_user_id UUID,
  p_coins_delta DECIMAL,
  p_time_delta DECIMAL DEFAULT 0
)
RETURNS BOOLEAN AS $$
DECLARE
  current_coins DECIMAL;
  current_time DECIMAL;
BEGIN
  -- Récupérer les soldes actuels
  SELECT nowee_coins, time_credits 
  INTO current_coins, current_time
  FROM wallets 
  WHERE user_id = p_user_id;
  
  -- Vérifier si l'utilisateur a suffisamment de fonds
  IF current_coins + p_coins_delta < 0 THEN
    RETURN FALSE; -- Solde insuffisant
  END IF;
  
  IF current_time + p_time_delta < 0 THEN
    RETURN FALSE; -- Crédits temps insuffisants
  END IF;
  
  -- Mettre à jour le portefeuille
  UPDATE wallets 
  SET 
    nowee_coins = nowee_coins + p_coins_delta,
    time_credits = time_credits + p_time_delta,
    total_earned = total_earned + GREATEST(0, p_coins_delta),
    total_spent = total_spent + GREATEST(0, -p_coins_delta),
    updated_at = NOW()
  WHERE user_id = p_user_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Insérer les taux de change par défaut
INSERT INTO exchange_rates (coins_per_hour, base_hourly_rate, skill_multipliers) 
VALUES (
  50.0, 
  50.0,
  '{
    "EDUCATION": 1.2,
    "REPAIR": 1.1,
    "TRANSPORT": 1.0,
    "CARE": 1.3,
    "CLEANING": 0.9,
    "COOKING": 1.0,
    "TECHNOLOGY": 1.4,
    "LEGAL": 1.5,
    "MEDICAL": 1.6
  }'::jsonb
);

-- Politiques de sécurité (temporairement permissives)
ALTER TABLE wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE barter_proposals ENABLE ROW LEVEL SECURITY;
ALTER TABLE time_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE time_bookings ENABLE ROW LEVEL SECURITY;

-- Politiques permissives pour les tests
CREATE POLICY "Allow all operations on wallets" ON wallets FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on transactions" ON transactions FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on barter_proposals" ON barter_proposals FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on time_services" ON time_services FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on time_bookings" ON time_bookings FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on rewards_penalties" ON rewards_penalties FOR ALL USING (true) WITH CHECK (true);
