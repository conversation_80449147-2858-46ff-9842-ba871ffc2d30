#!/usr/bin/env node

/**
 * Script de test du système de matching Nowee
 * Teste les fonctionnalités de correspondance automatique
 */

import 'dotenv/config';
import { dbService } from './src/services/databaseService.js';
import { matchingService } from './src/services/matchingService.js';
import { NotificationService } from './src/services/notificationService.js';

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.bold}${colors.blue}\n🔍 ${msg}${colors.reset}`)
};

async function testMatching() {
  log.title('Test du Système de Matching Nowee');

  try {
    // 1. Créer des utilisateurs de test
    log.info('Création des utilisateurs de test...');
    
    const user1 = await dbService.prisma.user.upsert({
      where: { phone: '+221771111111' },
      update: {},
      create: {
        phone: '+221771111111',
        name: 'Alice Diallo',
        location: {
          city: 'Dakar',
          country: 'Sénégal',
          coordinates: { latitude: 14.7167, longitude: -17.4677 }
        },
        rating: 4.8,
        helpGiven: 5
      }
    });

    const user2 = await dbService.prisma.user.upsert({
      where: { phone: '+221772222222' },
      update: {},
      create: {
        phone: '+221772222222',
        name: 'Mamadou Ba',
        location: {
          city: 'Dakar',
          country: 'Sénégal',
          coordinates: { latitude: 14.7200, longitude: -17.4700 }
        },
        rating: 4.5,
        helpGiven: 3
      }
    });

    log.success(`Utilisateurs créés: ${user1.name} et ${user2.name}`);

    // 2. Créer une offre
    log.info('Création d\'une offre d\'aide...');
    
    const offer = await dbService.prisma.offer.create({
      data: {
        userId: user2.id,
        type: 'MATERIAL',
        title: 'Perceuse disponible',
        description: 'J\'ai une perceuse électrique que je peux prêter',
        location: {
          city: 'Dakar',
          country: 'Sénégal',
          coordinates: { latitude: 14.7200, longitude: -17.4700 }
        },
        availability: {
          weekdays: true,
          weekends: true,
          hours: '8h-18h'
        },
        conditions: 'Retour dans les 24h',
        status: 'AVAILABLE'
      }
    });

    log.success(`Offre créée: "${offer.title}"`);

    // 3. Créer un besoin compatible
    log.info('Création d\'un besoin compatible...');
    
    const need = await dbService.prisma.need.create({
      data: {
        userId: user1.id,
        type: 'MATERIAL',
        title: 'Besoin d\'une perceuse',
        description: 'J\'ai besoin d\'une perceuse pour des travaux urgents',
        location: {
          city: 'Dakar',
          country: 'Sénégal',
          coordinates: { latitude: 14.7167, longitude: -17.4677 }
        },
        urgency: 3,
        status: 'OPEN'
      }
    });

    log.success(`Besoin créé: "${need.title}"`);

    // 4. Tester le matching
    log.info('Lancement du matching automatique...');
    
    const matches = await matchingService.findMatches(need.id);
    
    if (matches.length > 0) {
      log.success(`${matches.length} correspondance(s) trouvée(s) !`);
      
      for (const match of matches) {
        console.log(`\n📊 Match Score: ${match.metadata?.score || 'N/A'}`);
        console.log(`   Distance: ${match.metadata?.factors?.distance || 'N/A'}`);
        console.log(`   Réputation: ${match.metadata?.factors?.reputation || 'N/A'}`);
        console.log(`   Disponibilité: ${match.metadata?.factors?.availability || 'N/A'}`);
      }
    } else {
      log.warning('Aucune correspondance trouvée');
    }

    // 5. Tester l'acceptation d'un match
    if (matches.length > 0) {
      log.info('Test d\'acceptation de correspondance...');
      
      const acceptedMatch = await matchingService.acceptMatch(matches[0].id, user1.id);
      log.success(`Match accepté: ${acceptedMatch.status}`);
    }

    // 6. Tester les notifications de proximité
    log.info('Test des notifications de proximité...');
    
    const urgentNeed = await dbService.prisma.need.create({
      data: {
        userId: user1.id,
        type: 'EMERGENCY',
        title: 'Aide urgente',
        description: 'J\'ai besoin d\'aide immédiatement',
        location: {
          city: 'Dakar',
          country: 'Sénégal',
          coordinates: { latitude: 14.7167, longitude: -17.4677 }
        },
        urgency: 4,
        status: 'OPEN'
      }
    });

    // Simuler l'envoi de notifications (sans vraiment envoyer)
    console.log('📱 Simulation d\'envoi de notifications de proximité...');
    const nearbyUsers = await NotificationService.findNearbyUsers(
      14.7167, -17.4677, 5
    );
    
    log.success(`${nearbyUsers.length} utilisateur(s) à proximité trouvé(s)`);

    // 7. Statistiques finales
    log.info('Statistiques finales...');
    
    const stats = await dbService.getGlobalStats();
    console.log(`\n📊 Statistiques:
    • Utilisateurs: ${stats.totalUsers}
    • Besoins: ${stats.totalNeeds}
    • Offres: ${stats.totalOffers}
    • Correspondances: ${stats.totalMatches}`);

    log.success('Test du matching terminé avec succès !');

  } catch (error) {
    log.error(`Erreur lors du test: ${error.message}`);
    console.error(error);
  }
}

async function cleanupTestData() {
  log.info('Nettoyage des données de test...');
  
  try {
    // Supprimer les données de test
    await dbService.prisma.match.deleteMany({
      where: {
        OR: [
          { need: { user: { phone: { in: ['+221771111111', '+221772222222'] } } } },
          { offer: { user: { phone: { in: ['+221771111111', '+221772222222'] } } } }
        ]
      }
    });

    await dbService.prisma.need.deleteMany({
      where: {
        user: { phone: { in: ['+221771111111', '+221772222222'] } }
      }
    });

    await dbService.prisma.offer.deleteMany({
      where: {
        user: { phone: { in: ['+221771111111', '+221772222222'] } }
      }
    });

    await dbService.prisma.user.deleteMany({
      where: {
        phone: { in: ['+221771111111', '+221772222222'] }
      }
    });

    log.success('Données de test nettoyées');

  } catch (error) {
    log.error(`Erreur nettoyage: ${error.message}`);
  }
}

// Fonction principale
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--clean')) {
    await cleanupTestData();
  } else {
    await testMatching();
    
    if (args.includes('--cleanup')) {
      await cleanupTestData();
    }
  }
  
  process.exit(0);
}

// Gestion des erreurs
process.on('unhandledRejection', (error) => {
  log.error(`Erreur non gérée: ${error.message}`);
  process.exit(1);
});

// Exécuter le test
main().catch(error => {
  log.error(`Erreur: ${error.message}`);
  process.exit(1);
});
