/**
 * Service de base de données pour Nowee
 * Gère toutes les interactions avec PostgreSQL via Prisma
 */

import { PrismaClient } from '@prisma/client';

// Instance Prisma globale
const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
});

// Gestion de la fermeture propre
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});

/**
 * Service utilisateur avec base de données
 */
export class DatabaseUserService {
  
  /**
   * Obtient ou crée un utilisateur
   */
  async getUserProfile(phone) {
    const cleanPhone = phone.replace('whatsapp:', '');
    
    let user = await prisma.user.findUnique({
      where: { phone: cleanPhone },
      include: {
        needs: {
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        offers: {
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      }
    });
    
    if (!user) {
      user = await prisma.user.create({
        data: {
          phone: cleanPhone,
          lastActiveAt: new Date()
        },
        include: {
          needs: true,
          offers: true
        }
      });
    } else {
      // Mettre à jour la dernière activité
      user = await prisma.user.update({
        where: { id: user.id },
        data: { lastActiveAt: new Date() },
        include: {
          needs: {
            orderBy: { createdAt: 'desc' },
            take: 10
          },
          offers: {
            orderBy: { createdAt: 'desc' },
            take: 10
          }
        }
      });
    }
    
    return user;
  }
  
  /**
   * Met à jour le profil utilisateur
   */
  async updateUserProfile(phone, updates) {
    const cleanPhone = phone.replace('whatsapp:', '');
    
    const updateData = {
      lastActiveAt: new Date(),
      messageCount: { increment: 1 }
    };
    
    if (updates.location) {
      updateData.location = updates.location;
    }
    
    if (updates.preferences) {
      // Récupérer les préférences actuelles et les fusionner
      const currentUser = await prisma.user.findUnique({
        where: { phone: cleanPhone },
        select: { preferences: true }
      });
      
      const currentPrefs = currentUser?.preferences || {};
      updateData.preferences = { ...currentPrefs, ...updates.preferences };
    }
    
    if (updates.name) {
      updateData.name = updates.name;
    }
    
    return await prisma.user.upsert({
      where: { phone: cleanPhone },
      update: updateData,
      create: {
        phone: cleanPhone,
        ...updateData,
        messageCount: 1
      }
    });
  }
  
  /**
   * Obtient ou crée une conversation
   */
  async getOrCreateConversation(phone) {
    const user = await this.getUserProfile(phone);
    
    // Chercher une conversation active (non terminée)
    let conversation = await prisma.conversation.findFirst({
      where: {
        userId: user.id,
        endedAt: null
      },
      include: {
        messages: {
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      }
    });
    
    if (!conversation) {
      conversation = await prisma.conversation.create({
        data: {
          userId: user.id,
          context: {}
        },
        include: {
          messages: true
        }
      });
    }
    
    return conversation;
  }
  
  /**
   * Ajoute un message à la conversation
   */
  async addMessage(phone, userMessage, botResponse, analysis) {
    const conversation = await this.getOrCreateConversation(phone);
    
    // Ajouter le message utilisateur
    const userMsg = await prisma.message.create({
      data: {
        conversationId: conversation.id,
        senderId: conversation.userId,
        content: userMessage,
        messageType: 'TEXT',
        analysis: analysis
      }
    });
    
    // Ajouter la réponse du bot
    const botMsg = await prisma.message.create({
      data: {
        conversationId: conversation.id,
        content: botResponse,
        messageType: 'TEXT'
      }
    });
    
    return { userMsg, botMsg };
  }
  
  /**
   * Obtient l'historique de conversation
   */
  async getConversationHistory(phone, limit = 10) {
    const user = await this.getUserProfile(phone);
    
    const messages = await prisma.message.findMany({
      where: {
        conversation: {
          userId: user.id
        }
      },
      orderBy: { createdAt: 'desc' },
      take: limit * 2, // x2 car on a les messages user + bot
      include: {
        conversation: true
      }
    });
    
    return messages.reverse(); // Ordre chronologique
  }
  
  /**
   * Enregistre un besoin utilisateur
   */
  async recordUserNeed(phone, needData) {
    const user = await this.getUserProfile(phone);
    
    const need = await prisma.need.create({
      data: {
        userId: user.id,
        type: needData.type.toUpperCase(),
        title: needData.description.substring(0, 100),
        description: needData.description,
        location: needData.location,
        urgency: needData.urgency || 2,
        expiresAt: needData.expiresAt
      }
    });
    
    return need;
  }
  
  /**
   * Enregistre une offre d'aide
   */
  async recordUserOffer(phone, offerData) {
    const user = await this.getUserProfile(phone);
    
    const offer = await prisma.offer.create({
      data: {
        userId: user.id,
        type: offerData.type.toUpperCase(),
        title: offerData.description.substring(0, 100),
        description: offerData.description,
        location: offerData.location,
        availability: offerData.availability || {},
        conditions: offerData.conditions
      }
    });
    
    return offer;
  }
  
  /**
   * Recherche des correspondances pour un besoin
   */
  async findMatches(needId, maxDistance = 10) {
    const need = await prisma.need.findUnique({
      where: { id: needId },
      include: { user: true }
    });
    
    if (!need) return [];
    
    // Rechercher des offres compatibles
    const offers = await prisma.offer.findMany({
      where: {
        type: need.type,
        status: 'AVAILABLE',
        userId: { not: need.userId } // Pas le même utilisateur
      },
      include: {
        user: true
      }
    });
    
    // TODO: Implémenter le filtrage par distance géographique
    // Pour l'instant, retourner toutes les offres compatibles
    return offers;
  }
  
  /**
   * Crée une correspondance entre un besoin et une offre
   */
  async createMatch(needId, offerId, userId) {
    const match = await prisma.match.create({
      data: {
        needId,
        offerId,
        userId,
        status: 'PROPOSED'
      },
      include: {
        need: { include: { user: true } },
        offer: { include: { user: true } },
        user: true
      }
    });
    
    return match;
  }
  
  /**
   * Met à jour le statut d'une correspondance
   */
  async updateMatchStatus(matchId, status, rating = null, feedback = null) {
    const updateData = {
      status,
      updatedAt: new Date()
    };
    
    if (status === 'COMPLETED') {
      updateData.completedAt = new Date();
      if (rating) updateData.rating = rating;
      if (feedback) updateData.feedback = feedback;
    }
    
    return await prisma.match.update({
      where: { id: matchId },
      data: updateData
    });
  }
  
  /**
   * Obtient les statistiques globales
   */
  async getGlobalStats() {
    const [
      totalUsers,
      totalNeeds,
      totalOffers,
      totalMatches,
      activeConversations
    ] = await Promise.all([
      prisma.user.count(),
      prisma.need.count(),
      prisma.offer.count(),
      prisma.match.count(),
      prisma.conversation.count({ where: { endedAt: null } })
    ]);
    
    const totalMessages = await prisma.message.count();
    
    return {
      totalUsers,
      totalNeeds,
      totalOffers,
      totalMatches,
      totalMessages,
      activeConversations
    };
  }
  
  /**
   * Nettoie les données anciennes
   */
  async cleanup() {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    
    // Fermer les conversations inactives
    await prisma.conversation.updateMany({
      where: {
        endedAt: null,
        startedAt: { lt: thirtyDaysAgo }
      },
      data: {
        endedAt: new Date()
      }
    });
    
    // Expirer les besoins anciens
    await prisma.need.updateMany({
      where: {
        status: 'OPEN',
        createdAt: { lt: thirtyDaysAgo }
      },
      data: {
        status: 'EXPIRED'
      }
    });
  }
  
  /**
   * Enregistre un événement système
   */
  async logEvent(type, userId = null, data = {}) {
    return await prisma.systemEvent.create({
      data: {
        type,
        userId,
        data
      }
    });
  }
}

// Instance singleton
export const dbService = new DatabaseUserService();

export default dbService;
